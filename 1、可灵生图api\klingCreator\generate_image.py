#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI图片生成示例脚本
使用方法：
1. 将你的cookie字符串替换下面的 YOUR_COOKIE_HERE
2. 修改prompt为你想要的描述
3. 运行脚本：python generate_image.py
"""

from kling import ImageGen
from cookie_utils import get_cookie, check_cookie

def main():
    # 从cookie.txt文件读取cookie
    cookie = get_cookie()

    # 如果cookie为空，提示用户设置
    if not cookie:
        print("❌ 无法读取cookie！")
        print("请确保cookie.txt文件存在且包含有效的cookie字符串")
        return

    if not check_cookie():
        print("❌ Cookie格式不正确")
        return
    
    # 创建图片生成器
    print("🚀 正在初始化图片生成器...")
    image_gen = ImageGen(cookie)
    
    # 设置生成参数
    prompt = "一只在花园里玩耍的可爱小猫，阳光明媚，高质量，4K"
    output_dir = "./output"
    
    print(f"📝 提示词: {prompt}")
    print(f"📁 输出目录: {output_dir}")
    
    try:
        # 生成并保存图片
        print("🎨 开始生成图片...")
        image_gen.save_images(
            prompt=prompt,
            output_dir=output_dir,
            count=1,  # 生成4张图片
            ratio="1:1",  # 正方形比例
            model_name="2.1"  # 使用最新的2.1模型
        )
        
        print("✅ 图片生成完成！")
        print(f"📂 图片已保存到: {output_dir}")
        
        # 显示账户余额
        balance = image_gen.get_account_point()
        print(f"💰 账户余额: {balance} 积分")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        print("请检查:")
        print("1. Cookie是否正确且未过期")
        print("2. 账户是否有足够的积分")
        print("3. 网络连接是否正常")

if __name__ == "__main__":
    main()
