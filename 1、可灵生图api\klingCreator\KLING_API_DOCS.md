# 可灵AI独立API服务文档

## 🎯 概述

这是一个独立的可灵AI API服务，将可灵的图生图和积分查询功能封装成标准的REST API接口。

**服务地址**: `http://localhost:8080`

## 🚀 快速开始

### 启动服务

```bash
python kling_api_service.py
```

### 检查服务状态

```bash
curl http://localhost:8080/health
```

## 📋 API接口

### 1. 健康检查

**接口**: `GET /health`

**描述**: 检查服务运行状态和可灵AI连接状态

**响应示例**:
```json
{
    "status": "healthy",
    "message": "服务正常，当前积分: 1234",
    "timestamp": **********.123
}
```

### 2. 获取账户积分

**接口**: `GET /api/balance`

**描述**: 获取可灵AI账户剩余积分

**响应示例**:
```json
{
    "success": true,
    "balance": 1234,
    "timestamp": **********.123
}
```

### 3. 图生图

**接口**: `POST /api/generate`

**描述**: 基于参考图片和提示词生成新图片

#### 方式1: 文件上传

**Content-Type**: `multipart/form-data`

**参数**:
- `image` (file): 参考图片文件
- `prompt` (string): 提示词
- `style` (string, 可选): 风格，默认"默认"

**示例**:
```bash
curl -X POST http://localhost:8080/api/generate \
  -F "image=@/path/to/image.jpg" \
  -F "prompt=一座未来科技感的建筑" \
  -F "style=默认"
```

#### 方式2: JSON格式

**Content-Type**: `application/json`

**参数**:
- `image_base64` (string): Base64编码的图片数据
- `image_url` (string): 图片URL（与image_base64二选一）
- `prompt` (string): 提示词
- `style` (string, 可选): 风格，默认"默认"

**示例**:
```bash
curl -X POST http://localhost:8080/api/generate \
  -H "Content-Type: application/json" \
  -d '{
    "image_url": "https://example.com/image.jpg",
    "prompt": "一座未来科技感的建筑",
    "style": "默认"
  }'
```

**响应示例**:
```json
{
    "success": true,
    "image_url": "https://kling.kuaishou.com/bs2/image-kling/...",
    "prompt": "一座未来科技感的建筑",
    "style": "默认",
    "timestamp": **********.123
}
```

### 4. API信息

**接口**: `GET /api/info`

**描述**: 获取API服务信息和接口列表

**响应示例**:
```json
{
    "name": "可灵AI API服务",
    "version": "1.0.0",
    "description": "提供可灵AI图生图和积分查询功能",
    "endpoints": {
        "GET /health": "健康检查",
        "GET /api/balance": "获取账户积分",
        "POST /api/generate": "图生图（支持文件上传和JSON格式）",
        "GET /api/info": "获取API信息"
    },
    "timestamp": **********.123
}
```

## 🔧 错误处理

### 错误响应格式

```json
{
    "success": false,
    "error": "错误描述",
    "timestamp": **********.123
}
```

### 常见错误码

- `400`: 请求参数错误
- `500`: 服务器内部错误
- `503`: 服务不可用（健康检查失败）

## 💡 使用示例

### Python示例

```python
import requests
import base64

# 1. 检查服务状态
response = requests.get('http://localhost:8080/health')
print(response.json())

# 2. 获取积分
response = requests.get('http://localhost:8080/api/balance')
balance_data = response.json()
print(f"当前积分: {balance_data['balance']}")

# 3. 图生图 - 文件上传方式
with open('image.jpg', 'rb') as f:
    files = {'image': f}
    data = {
        'prompt': '一座未来科技感的建筑',
        'style': '默认'
    }
    response = requests.post('http://localhost:8080/api/generate', 
                           files=files, data=data)
    result = response.json()
    if result['success']:
        print(f"生成成功: {result['image_url']}")

# 4. 图生图 - Base64方式
with open('image.jpg', 'rb') as f:
    image_base64 = base64.b64encode(f.read()).decode()

data = {
    'image_base64': image_base64,
    'prompt': '一座未来科技感的建筑',
    'style': '默认'
}
response = requests.post('http://localhost:8080/api/generate', 
                        json=data)
result = response.json()
if result['success']:
    print(f"生成成功: {result['image_url']}")
```

### JavaScript示例

```javascript
// 1. 获取积分
async function getBalance() {
    const response = await fetch('http://localhost:8080/api/balance');
    const data = await response.json();
    console.log(`当前积分: ${data.balance}`);
}

// 2. 图生图 - 文件上传
async function generateImage(file, prompt) {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('prompt', prompt);
    formData.append('style', '默认');
    
    const response = await fetch('http://localhost:8080/api/generate', {
        method: 'POST',
        body: formData
    });
    
    const result = await response.json();
    if (result.success) {
        console.log(`生成成功: ${result.image_url}`);
        return result.image_url;
    } else {
        console.error(`生成失败: ${result.error}`);
    }
}

// 3. 图生图 - URL方式
async function generateImageFromUrl(imageUrl, prompt) {
    const response = await fetch('http://localhost:8080/api/generate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            image_url: imageUrl,
            prompt: prompt,
            style: '默认'
        })
    });
    
    const result = await response.json();
    if (result.success) {
        console.log(`生成成功: ${result.image_url}`);
        return result.image_url;
    } else {
        console.error(`生成失败: ${result.error}`);
    }
}
```

## 🔒 安全注意事项

1. **生产环境部署**:
   - 使用HTTPS
   - 添加API密钥认证
   - 限制请求频率
   - 配置防火墙

2. **文件安全**:
   - 验证上传文件类型
   - 限制文件大小
   - 定期清理临时文件

3. **错误处理**:
   - 不要在错误信息中暴露敏感信息
   - 记录详细的错误日志

## 🚀 部署建议

### 开发环境
```bash
python kling_api_service.py
```

### 生产环境
```bash
# 使用gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8080 kling_api_service:app

# 或使用uwsgi
pip install uwsgi
uwsgi --http :8080 --wsgi-file kling_api_service.py --callable app
```

## 📝 更新日志

### v1.0.0
- 初始版本
- 支持图生图和积分查询
- 支持多种图片输入方式
- 完整的错误处理和日志记录

## 🤝 支持

如有问题或建议，请查看日志文件或联系开发者。
