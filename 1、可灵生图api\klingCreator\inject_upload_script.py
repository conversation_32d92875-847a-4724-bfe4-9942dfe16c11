#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过JavaScript注入的方式在当前浏览器页面上传首尾帧
不需要启动新浏览器，直接在已登录的页面操作
"""

import os
import sys
import base64
import mimetypes
from pathlib import Path


def read_image_as_base64(image_path: str):
    """读取图片并转换为Base64"""
    # 获取MIME类型
    mime_type, _ = mimetypes.guess_type(image_path)
    if not mime_type or not mime_type.startswith('image/'):
        mime_type = 'image/png'
    
    # 读取文件
    with open(image_path, 'rb') as f:
        image_data = f.read()
    
    # 转换为Base64
    base64_data = base64.b64encode(image_data).decode('utf-8')
    data_url = f"data:{mime_type};base64,{base64_data}"
    
    return {
        "name": os.path.basename(image_path),
        "data": data_url,
        "type": mime_type
    }


def generate_upload_script(first_frame_path: str, last_frame_path: str, prompt: str = ""):
    """生成上传脚本"""
    
    print("🎬 生成即梦AI上传脚本")
    print("=" * 50)
    print(f"📸 首帧: {Path(first_frame_path).name}")
    print(f"📸 尾帧: {Path(last_frame_path).name}")
    print(f"💭 提示词: {prompt}")
    print()
    
    # 验证文件存在
    if not os.path.exists(first_frame_path):
        print(f"❌ 首帧图片不存在: {first_frame_path}")
        return None
    
    if not os.path.exists(last_frame_path):
        print(f"❌ 尾帧图片不存在: {last_frame_path}")
        return None
    
    # 读取图片数据
    print("📤 读取图片数据...")
    first_frame_data = read_image_as_base64(first_frame_path)
    last_frame_data = read_image_as_base64(last_frame_path)
    print("✅ 图片数据读取完成")
    
    # 生成JavaScript代码
    js_code = f"""
// 即梦AI自动上传脚本
(async function() {{
    console.log('🎬 开始即梦AI自动上传流程');
    
    // 图片数据
    const firstFrameData = {first_frame_data};
    const lastFrameData = {last_frame_data};
    const promptText = "{prompt}";
    
    // 辅助函数：Base64转File对象
    function dataURLtoFile(dataurl, filename) {{
        const arr = dataurl.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while(n--) {{
            u8arr[n] = bstr.charCodeAt(n);
        }}
        return new File([u8arr], filename, {{type: mime}});
    }}
    
    // 辅助函数：等待
    function sleep(ms) {{
        return new Promise(resolve => setTimeout(resolve, ms));
    }}
    
    // 辅助函数：查找并点击按钮
    function findAndClickButton(keywords) {{
        const buttons = document.querySelectorAll('button');
        for (const button of buttons) {{
            if (button.style.display === 'none' || !button.offsetParent) continue;
            
            const text = button.textContent.trim().toLowerCase();
            for (const keyword of keywords) {{
                if (text.includes(keyword)) {{
                    button.click();
                    console.log(`✅ 点击按钮: ${{button.textContent.trim()}}`);
                    return true;
                }}
            }}
        }}
        return false;
    }}
    
    try {{
        // 1. 查找文件上传元素
        console.log('🔍 查找文件上传元素...');
        await sleep(2000);
        
        let fileInputs = document.querySelectorAll('input[type="file"]');
        console.log(`找到 ${{fileInputs.length}} 个文件上传元素`);
        
        if (fileInputs.length < 2) {{
            console.log('⚠️ 文件上传元素不足，尝试点击上传区域...');
            
            // 尝试点击上传区域
            const uploadSelectors = [
                'div[class*="upload"]',
                'button[class*="upload"]',
                '.upload-area',
                '.upload-zone',
                'div[role="button"]'
            ];
            
            for (const selector of uploadSelectors) {{
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {{
                    if (element.offsetParent) {{
                        element.click();
                        console.log(`点击了: ${{selector}}`);
                        await sleep(1000);
                    }}
                }}
            }}
            
            await sleep(2000);
            fileInputs = document.querySelectorAll('input[type="file"]');
            console.log(`点击后找到 ${{fileInputs.length}} 个文件上传元素`);
        }}
        
        if (fileInputs.length >= 2) {{
            // 2. 上传首帧
            console.log('📤 上传首帧...');
            const firstFile = dataURLtoFile(firstFrameData.data, firstFrameData.name);
            const firstDataTransfer = new DataTransfer();
            firstDataTransfer.items.add(firstFile);
            fileInputs[0].files = firstDataTransfer.files;
            fileInputs[0].dispatchEvent(new Event('change', {{ bubbles: true }}));
            
            // 等待并处理保存弹窗
            console.log('⏳ 等待首帧保存弹窗...');
            await sleep(3000);
            
            // 查找并点击保存按钮
            for (let i = 0; i < 10; i++) {{
                if (findAndClickButton(['保存', '确定', '确认', 'save', 'ok'])) {{
                    break;
                }}
                await sleep(500);
            }}
            
            await sleep(2000);
            console.log('✅ 首帧上传完成');
            
            // 3. 上传尾帧
            console.log('📤 上传尾帧...');
            const lastFile = dataURLtoFile(lastFrameData.data, lastFrameData.name);
            const lastDataTransfer = new DataTransfer();
            lastDataTransfer.items.add(lastFile);
            fileInputs[1].files = lastDataTransfer.files;
            fileInputs[1].dispatchEvent(new Event('change', {{ bubbles: true }}));
            
            // 等待并处理保存弹窗
            console.log('⏳ 等待尾帧保存弹窗...');
            await sleep(3000);
            
            // 查找并点击保存按钮
            for (let i = 0; i < 10; i++) {{
                if (findAndClickButton(['保存', '确定', '确认', 'save', 'ok'])) {{
                    break;
                }}
                await sleep(500);
            }}
            
            await sleep(2000);
            console.log('✅ 尾帧上传完成');
            
            // 4. 设置提示词
            if (promptText) {{
                console.log('💭 设置提示词...');
                const textInputs = document.querySelectorAll('textarea, input[type="text"]');
                for (const input of textInputs) {{
                    if (input.offsetParent) {{
                        const placeholder = input.placeholder || '';
                        if (placeholder.includes('提示') || placeholder.includes('prompt')) {{
                            input.value = promptText;
                            input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                            input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                            console.log('✅ 提示词设置完成');
                            break;
                        }}
                    }}
                }}
            }}
            
            // 5. 设置10秒时长
            console.log('⏱️ 设置10秒时长...');
            await sleep(1000);
            findAndClickButton(['10s', '10秒']);
            
            // 6. 点击生成按钮
            console.log('🚀 点击生成按钮...');
            await sleep(2000);
            
            if (findAndClickButton(['生成', 'generate', '创建'])) {{
                console.log('✅ 视频生成已开始！');
                alert('✅ 视频生成已开始！请等待生成完成。');
            }} else {{
                console.log('❌ 未找到生成按钮');
                alert('❌ 未找到生成按钮，请手动点击生成。');
            }}
            
        }} else {{
            console.log('❌ 未找到足够的文件上传元素');
            alert('❌ 未找到文件上传元素，请确保在即梦AI视频生成页面。');
        }}
        
    }} catch (error) {{
        console.error('❌ 自动上传失败:', error);
        alert('❌ 自动上传失败: ' + error.message);
    }}
}})();
"""
    
    return js_code


def save_script_to_file(js_code: str, filename: str = "jimeng_upload_script.js"):
    """保存脚本到文件"""
    script_path = Path(filename)
    
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(js_code)
    
    print(f"✅ 脚本已保存到: {script_path.absolute()}")
    return str(script_path.absolute())


def print_usage_instructions(script_path: str):
    """打印使用说明"""
    print("\n📋 使用说明:")
    print("=" * 50)
    print("1. 在Chrome中打开即梦AI视频生成页面:")
    print("   https://jimeng.jianying.com/ai-tool/generate?type=video")
    print()
    print("2. 确保已登录即梦AI账号")
    print()
    print("3. 按F12打开开发者工具")
    print()
    print("4. 切换到Console（控制台）标签页")
    print()
    print("5. 复制以下脚本内容并粘贴到控制台中，然后按Enter执行:")
    print()
    print("=" * 50)
    print("或者直接复制脚本文件内容:")
    print(f"文件位置: {script_path}")
    print("=" * 50)


def main():
    """主函数"""
    
    # 测试图片路径
    first_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座孤立在纯黑色空间中的蒸汽朋克机械堡垒_建筑内部的锅炉透过格栅.png"
    last_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (4).png"
    prompt = "从蒸汽朋克机械堡垒平滑过渡到生物发光植物建筑"
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) >= 3:
        first_frame = sys.argv[1]
        last_frame = sys.argv[2]
        prompt = sys.argv[3] if len(sys.argv) > 3 else ""
    
    # 生成脚本
    js_code = generate_upload_script(first_frame, last_frame, prompt)
    
    if js_code:
        # 保存脚本到文件
        script_path = save_script_to_file(js_code)
        
        # 打印使用说明
        print_usage_instructions(script_path)
        
        # 也直接显示脚本内容（截取部分）
        print("\n🔧 脚本内容预览:")
        print("=" * 50)
        lines = js_code.split('\n')
        for i, line in enumerate(lines[:10]):
            print(f"{i+1:2d}: {line}")
        print("...")
        print(f"总共 {len(lines)} 行")
        
        print("\n💡 提示:")
        print("   - 脚本会自动上传图片并设置参数")
        print("   - 如果遇到保存弹窗，脚本会自动点击保存")
        print("   - 生成开始后会显示提示信息")
        print("   - 请在即梦AI页面的控制台中运行此脚本")
        
        return True
    else:
        print("❌ 脚本生成失败")
        return False


if __name__ == "__main__":
    main()
