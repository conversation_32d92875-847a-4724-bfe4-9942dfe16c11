import asyncio
from playwright.async_api import async_playwright
import json

async def connect_to_chrome_debug():
    """连接到Chrome调试端口9222"""
    async with async_playwright() as p:
        try:
            print("正在连接到Chrome调试端口9222...")
            browser = await p.chromium.connect_over_cdp("http://localhost:9222")
            print("✅ 成功连接到Chrome调试浏览器")
            
            # 获取所有上下文
            contexts = browser.contexts
            print(f"找到 {len(contexts)} 个浏览器上下文")
            
            if not contexts:
                print("❌ 没有找到浏览器上下文")
                return
            
            # 获取第一个上下文的所有页面
            context = contexts[0]
            pages = context.pages
            print(f"找到 {len(pages)} 个页面")
            
            if not pages:
                print("❌ 没有找到打开的页面")
                return
            
            # 列出所有页面
            print("\n📄 所有打开的页面:")
            for i, page in enumerate(pages):
                print(f"  {i+1}. {page.url}")
            
            # 查找即梦AI页面
            target_page = None
            for page in pages:
                if "jimeng.jianying.com" in page.url:
                    target_page = page
                    break
            
            if not target_page:
                print("\n⚠️  没有找到即梦AI页面，使用第一个页面")
                target_page = pages[0]
            
            print(f"\n🎯 使用页面: {target_page.url}")
            
            # 开始调试
            await debug_page(target_page)
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            print("请确保Chrome已经用以下命令启动:")
            print("chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug")

async def debug_page(page):
    """调试页面功能"""
    print("\n🔧 开始调试页面...")
    
    while True:
        print("\n" + "="*50)
        print("选择调试功能:")
        print("1. 检查页面基本信息")
        print("2. 检查首尾帧状态")
        print("3. 测试首尾帧清空")
        print("4. 执行自定义JavaScript")
        print("5. 截图")
        print("0. 退出")
        print("="*50)
        
        try:
            choice = input("请输入选择 (0-5): ").strip()
            
            if choice == "0":
                print("👋 退出调试")
                break
            elif choice == "1":
                await check_page_info(page)
            elif choice == "2":
                await check_frame_status(page)
            elif choice == "3":
                await test_frame_clearing(page)
            elif choice == "4":
                await execute_custom_js(page)
            elif choice == "5":
                await take_screenshot(page)
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 用户中断，退出调试")
            break
        except Exception as e:
            print(f"❌ 执行出错: {e}")

async def check_page_info(page):
    """检查页面基本信息"""
    print("\n📋 检查页面基本信息...")
    
    try:
        result = await page.evaluate("""
            () => {
                return {
                    url: window.location.href,
                    title: document.title,
                    userAgent: navigator.userAgent.substring(0, 100) + '...',
                    viewport: {
                        width: window.innerWidth,
                        height: window.innerHeight
                    },
                    timestamp: new Date().toISOString()
                };
            }
        """)
        
        print(f"🌐 URL: {result['url']}")
        print(f"📄 标题: {result['title']}")
        print(f"🖥️  视窗: {result['viewport']['width']}x{result['viewport']['height']}")
        print(f"⏰ 时间: {result['timestamp']}")
        
    except Exception as e:
        print(f"❌ 检查页面信息失败: {e}")

async def check_frame_status(page):
    """检查首尾帧状态"""
    print("\n🖼️  检查首尾帧状态...")
    
    try:
        result = await page.evaluate("""
            () => {
                const firstFrameGroup = document.querySelector('.reference-group-h6uyrf:not(.last-frame-ZAyskt)');
                const lastFrameGroup = document.querySelector('.reference-group-h6uyrf.last-frame-ZAyskt');
                
                return {
                    firstFrame: {
                        exists: !!firstFrameGroup,
                        hasImage: firstFrameGroup ? !!firstFrameGroup.querySelector('.reference-mgqKPd img') : false,
                        className: firstFrameGroup ? firstFrameGroup.className : null
                    },
                    lastFrame: {
                        exists: !!lastFrameGroup,
                        hasImage: lastFrameGroup ? !!lastFrameGroup.querySelector('.reference-mgqKPd img') : false,
                        className: lastFrameGroup ? lastFrameGroup.className : null
                    },
                    totalImages: document.querySelectorAll('.reference-mgqKPd img').length,
                    allFrameGroups: document.querySelectorAll('.reference-group-h6uyrf').length
                };
            }
        """)
        
        print(f"🖼️  首帧: 存在={result['firstFrame']['exists']}, 有图片={result['firstFrame']['hasImage']}")
        print(f"🖼️  尾帧: 存在={result['lastFrame']['exists']}, 有图片={result['lastFrame']['hasImage']}")
        print(f"📊 总图片数: {result['totalImages']}")
        print(f"📊 总帧组数: {result['allFrameGroups']}")
        
        if result['firstFrame']['className']:
            print(f"🏷️  首帧类名: {result['firstFrame']['className']}")
        if result['lastFrame']['className']:
            print(f"🏷️  尾帧类名: {result['lastFrame']['className']}")
        
    except Exception as e:
        print(f"❌ 检查帧状态失败: {e}")

async def test_frame_clearing(page):
    """测试首尾帧清空"""
    print("\n🧹 测试首尾帧清空...")
    
    try:
        # 先检查状态
        result = await page.evaluate("""
            () => {
                const firstFrameGroup = document.querySelector('.reference-group-h6uyrf:not(.last-frame-ZAyskt)');
                const lastFrameGroup = document.querySelector('.reference-group-h6uyrf.last-frame-ZAyskt');
                
                return {
                    firstFrameHasImage: firstFrameGroup ? !!firstFrameGroup.querySelector('.reference-mgqKPd img') : false,
                    lastFrameHasImage: lastFrameGroup ? !!lastFrameGroup.querySelector('.reference-mgqKPd img') : false
                };
            }
        """)
        
        print(f"📋 当前状态: 首帧有图片={result['firstFrameHasImage']}, 尾帧有图片={result['lastFrameHasImage']}")
        
        # 按顺序清空：先尾帧，再首帧
        if result['lastFrameHasImage']:
            print("🧹 开始清空尾帧...")
            success = await clear_frame(page, '.reference-group-h6uyrf.last-frame-ZAyskt .reference-mgqKPd', "尾帧")
            if success:
                print("✅ 尾帧清空成功")
            else:
                print("❌ 尾帧清空失败")
        
        if result['firstFrameHasImage']:
            print("🧹 开始清空首帧...")
            success = await clear_frame(page, '.reference-group-h6uyrf:not(.last-frame-ZAyskt) .reference-mgqKPd', "首帧")
            if success:
                print("✅ 首帧清空成功")
            else:
                print("❌ 首帧清空失败")
        
        if not result['firstFrameHasImage'] and not result['lastFrameHasImage']:
            print("ℹ️  没有发现需要清空的帧图片")
        
        print("🏁 首尾帧清空测试完成")
        
    except Exception as e:
        print(f"❌ 测试清空功能失败: {e}")

async def clear_frame(page, selector, frame_name):
    """清空指定帧"""
    try:
        # 查找帧元素
        frame_element = await page.query_selector(selector)
        if not frame_element:
            print(f"❌ 未找到{frame_name}元素")
            return False
        
        # 悬停显示删除按钮
        print(f"🖱️  悬停到{frame_name}...")
        await frame_element.hover()
        await page.wait_for_timeout(500)
        
        # 查找删除按钮
        remove_button = await frame_element.query_selector('.remove-button-CGHPzk')
        if not remove_button:
            print(f"❌ 未找到{frame_name}删除按钮")
            return False
        
        # 点击删除按钮
        print(f"🖱️  点击{frame_name}删除按钮...")
        await remove_button.click()
        await page.wait_for_timeout(1000)
        
        return True
        
    except Exception as e:
        print(f"❌ 清空{frame_name}失败: {e}")
        return False

async def execute_custom_js(page):
    """执行自定义JavaScript"""
    print("\n💻 执行自定义JavaScript")
    print("输入JavaScript代码 (输入'exit'退出):")
    
    while True:
        try:
            js_code = input("JS> ").strip()
            if js_code.lower() == 'exit':
                break
            if not js_code:
                continue
                
            result = await page.evaluate(f"() => {{ {js_code} }}")
            print(f"📤 结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
            
        except Exception as e:
            print(f"❌ 执行失败: {e}")

async def take_screenshot(page):
    """截图"""
    print("\n📸 正在截图...")
    
    try:
        import datetime
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"debug_screenshot_{timestamp}.png"
        
        await page.screenshot(path=filename)
        print(f"✅ 截图已保存: {filename}")
        
    except Exception as e:
        print(f"❌ 截图失败: {e}")

if __name__ == "__main__":
    print("🚀 Chrome调试连接工具")
    print("确保Chrome已经用以下命令启动:")
    print("chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug")
    print()
    
    asyncio.run(connect_to_chrome_debug())
