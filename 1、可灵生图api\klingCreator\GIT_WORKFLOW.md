# Git 工作流指南

## 🌳 分支策略

### 主要分支
- **main**: 主分支，包含稳定的生产代码
- **develop**: 开发分支，包含最新的开发功能

### 功能分支
- **feature/功能名**: 新功能开发
- **bugfix/问题描述**: Bug修复
- **hotfix/紧急修复**: 紧急修复

## 🔄 工作流程

### 1. 开发新功能

```bash
# 从develop分支创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/新功能名

# 开发完成后
git add .
git commit -m "feat: 添加新功能"
git push origin feature/新功能名

# 创建Pull Request合并到develop
```

### 2. 修复Bug

```bash
# 从develop分支创建修复分支
git checkout develop
git checkout -b bugfix/修复描述

# 修复完成后
git add .
git commit -m "fix: 修复某个问题"
git push origin bugfix/修复描述
```

### 3. 紧急修复

```bash
# 从main分支创建热修复分支
git checkout main
git checkout -b hotfix/紧急修复

# 修复完成后同时合并到main和develop
git checkout main
git merge hotfix/紧急修复
git checkout develop
git merge hotfix/紧急修复
```

## 📝 提交信息规范

### 提交类型
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 重构代码
- **test**: 测试相关
- **chore**: 构建工具或辅助工具变动

### 提交格式
```
类型: 简短描述 (不超过50字符)

详细说明 (可选)
- 变更点1
- 变更点2

Closes #issue号码
```

## 🛠️ 常用Git命令

### 基础操作
```bash
# 查看状态
git status

# 查看提交历史
git log --oneline --graph

# 查看分支
git branch -a

# 切换分支
git checkout 分支名

# 创建并切换分支
git checkout -b 新分支名
```

### 同步操作
```bash
# 拉取最新代码
git pull origin 分支名

# 推送代码
git push origin 分支名

# 推送新分支
git push -u origin 新分支名
```

### 合并操作
```bash
# 合并分支
git merge 分支名

# 变基合并
git rebase 分支名

# 解决冲突后继续
git add .
git rebase --continue
```

## 🔍 代码审查

### Pull Request检查清单
- [ ] 代码符合项目规范
- [ ] 添加了必要的注释
- [ ] 通过了所有测试
- [ ] 更新了相关文档
- [ ] 没有敏感信息泄露

## 📦 发布流程

### 版本发布
```bash
# 1. 确保develop分支稳定
git checkout develop
git pull origin develop

# 2. 合并到main分支
git checkout main
git merge develop

# 3. 创建版本标签
git tag -a v1.0.0 -m "Release version 1.0.0"

# 4. 推送标签
git push origin v1.0.0
git push origin main
```

## ⚠️ 注意事项

1. **不要直接在main分支开发**
2. **提交前先拉取最新代码**
3. **保持提交信息清晰明确**
4. **定期清理本地分支**
5. **敏感信息不要提交到仓库**

## 🧹 维护命令

```bash
# 清理已合并的分支
git branch --merged | grep -v main | xargs git branch -d

# 清理远程追踪分支
git remote prune origin

# 查看文件变更统计
git diff --stat

# 查看某个文件的提交历史
git log --follow 文件名
```
