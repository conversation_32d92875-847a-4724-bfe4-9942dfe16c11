// 画廊页面JavaScript

let allUploads = [];
let filteredUploads = [];

// DOM元素
const galleryGrid = document.getElementById('gallery-grid');
const loading = document.getElementById('loading');
const emptyState = document.getElementById('empty-state');
const searchInput = document.getElementById('search-input');
const sortSelect = document.getElementById('sort-select');
const previewModal = document.getElementById('preview-modal');
const previewImage = document.getElementById('preview-image');
const previewTitle = document.getElementById('preview-title');
const previewStats = document.getElementById('preview-stats');
const viewDetailsBtn = document.getElementById('view-details-btn');
const modalClose = document.getElementById('modal-close');

// 统计元素
const totalUploads = document.getElementById('total-uploads');
const totalGenerated = document.getElementById('total-generated');
const totalLikes = document.getElementById('total-likes');
const totalDislikes = document.getElementById('total-dislikes');

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadGalleryData();
    setupEventListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 搜索功能
    searchInput.addEventListener('input', function() {
        filterAndDisplayUploads();
    });
    
    // 排序功能
    sortSelect.addEventListener('change', function() {
        filterAndDisplayUploads();
    });
    
    // 模态框关闭
    modalClose.addEventListener('click', closeModal);
    previewModal.addEventListener('click', function(e) {
        if (e.target === previewModal) {
            closeModal();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

// 加载画廊数据
async function loadGalleryData() {
    try {
        showLoading();

        console.log('开始加载画廊数据...');
        const response = await fetch('/api/gallery');
        console.log('API响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('画廊数据:', data);

        if (data.success) {
            allUploads = data.uploads || [];
            console.log('加载的上传图片数量:', allUploads.length);
            updateStatistics(data.statistics || {});
            filterAndDisplayUploads();
        } else {
            throw new Error(data.error || '加载数据失败');
        }

    } catch (error) {
        console.error('加载画廊数据失败:', error);
        showError('加载数据失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// 更新统计信息
function updateStatistics(stats) {
    totalUploads.textContent = stats.total_uploads || 0;
    totalGenerated.textContent = stats.total_generated || 0;
    totalLikes.textContent = stats.total_likes || 0;
    totalDislikes.textContent = stats.total_dislikes || 0;
}

// 筛选和显示上传图片
function filterAndDisplayUploads() {
    const searchTerm = searchInput.value.toLowerCase().trim();
    const sortBy = sortSelect.value;
    
    // 筛选
    filteredUploads = allUploads.filter(upload => {
        return upload.filename.toLowerCase().includes(searchTerm);
    });
    
    // 排序
    filteredUploads.sort((a, b) => {
        switch (sortBy) {
            case 'newest':
                return new Date(b.upload_time) - new Date(a.upload_time);
            case 'oldest':
                return new Date(a.upload_time) - new Date(b.upload_time);
            case 'most-generated':
                return (b.generated_count || 0) - (a.generated_count || 0);
            case 'most-liked':
                return (b.like_count || 0) - (a.like_count || 0);
            default:
                return 0;
        }
    });
    
    displayUploads();
}

// 显示上传图片
function displayUploads() {
    if (filteredUploads.length === 0) {
        showEmptyState();
        return;
    }
    
    hideEmptyState();
    
    galleryGrid.innerHTML = '';
    
    filteredUploads.forEach(upload => {
        const card = createGalleryCard(upload);
        galleryGrid.appendChild(card);
    });
}

// 创建画廊卡片
function createGalleryCard(upload) {
    const card = document.createElement('div');
    card.className = 'gallery-card';
    card.onclick = () => openPreview(upload);
    
    const uploadDate = new Date(upload.upload_time).toLocaleDateString('zh-CN');
    
    card.innerHTML = `
        <img src="/api/upload/${encodeURIComponent(upload.filename)}"
             alt="${upload.filename}"
             class="gallery-image"
             onerror="handleImageError(this)"
        <div class="gallery-info">
            <div class="gallery-title" title="${upload.filename}">${upload.filename}</div>
            <div class="gallery-stats">
                <div class="stat-item">
                    <span>🎨</span>
                    <span>${upload.generated_count || 0}</span>
                </div>
                <div class="stat-item">
                    <span>👍</span>
                    <span>${upload.like_count || 0}</span>
                </div>
                <div class="stat-item">
                    <span>👎</span>
                    <span>${upload.dislike_count || 0}</span>
                </div>
            </div>
            <div class="gallery-date">📅 ${uploadDate}</div>
        </div>
    `;
    
    return card;
}

// 打开预览模态框
function openPreview(upload) {
    previewImage.src = `/api/upload/${encodeURIComponent(upload.filename)}`;
    previewTitle.textContent = upload.filename;
    previewStats.textContent = `生成 ${upload.generated_count || 0} 张图片 • 获得 ${upload.like_count || 0} 个赞 • ${upload.dislike_count || 0} 个踩`;
    viewDetailsBtn.href = `/details/${encodeURIComponent(upload.filename)}`;
    
    previewModal.style.display = 'flex';
}

// 关闭模态框
function closeModal() {
    previewModal.style.display = 'none';
}

// 显示加载状态
function showLoading() {
    loading.style.display = 'block';
    galleryGrid.style.display = 'none';
    emptyState.style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    loading.style.display = 'none';
    galleryGrid.style.display = 'grid';
}

// 显示空状态
function showEmptyState() {
    galleryGrid.style.display = 'none';
    emptyState.style.display = 'block';
}

// 隐藏空状态
function hideEmptyState() {
    emptyState.style.display = 'none';
    galleryGrid.style.display = 'grid';
}

// 处理图片加载错误
function handleImageError(img) {
    // 防止无限循环，只处理一次
    if (img.dataset.errorHandled) return;
    img.dataset.errorHandled = 'true';

    // 替换为错误提示
    img.style.display = 'none';
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = 'display:flex;align-items:center;justify-content:center;height:200px;background:#f7fafc;color:#a0aec0;font-size:2rem;';
    errorDiv.innerHTML = '📷';
    img.parentElement.appendChild(errorDiv);
}

// 显示错误
function showError(message) {
    galleryGrid.innerHTML = `
        <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: #e53e3e;">
            <h3>❌ 加载失败</h3>
            <p>${message}</p>
            <button onclick="loadGalleryData()" class="btn btn-primary">🔄 重试</button>
        </div>
    `;
}
