<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片详情 - 可灵AI图生图</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/details.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/video-features.css') }}">
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-brand">
                <h1>🎨 可灵AI图生图 - 图片详情</h1>
            </div>
            <div class="nav-links">
                <a href="/" class="nav-link">🏠 首页</a>
                <a href="/gallery" class="nav-link">🖼️ 画廊</a>
                <a href="#" class="nav-link active">📋 详情</a>
            </div>
        </nav>

        <!-- 返回按钮 -->
        <div class="back-section">
            <a href="/gallery" class="back-btn">
                <span>←</span> 返回画廊
            </a>
        </div>

        <!-- 原图信息 -->
        <div class="original-section">
            <div class="original-image-container">
                <img id="original-image" src="" alt="原图" class="original-image">
            </div>
            <div class="original-info">
                <h2 id="original-title">图片标题</h2>
                <div class="original-stats">
                    <div class="stat-item">
                        <span class="stat-icon">📅</span>
                        <span class="stat-label">上传时间:</span>
                        <span id="upload-time" class="stat-value">-</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🎨</span>
                        <span class="stat-label">生成数量:</span>
                        <span id="generated-count" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">⭐</span>
                        <span class="stat-label">收藏数量:</span>
                        <span id="total-likes" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🗑️</span>
                        <span class="stat-label">标记删除:</span>
                        <span id="total-dislikes" class="stat-value">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">📷</span>
                        <span class="stat-label">未处理:</span>
                        <span id="total-unprocessed" class="stat-value">0</span>
                    </div>
                </div>
                <div class="original-actions">
                    <button id="reuse-image-btn" class="btn btn-primary">
                        🔄 重新使用这张图片
                    </button>
                </div>
            </div>
        </div>

        <!-- 筛选选项 -->
        <div class="filter-section">
            <h3>🎨 生成的图片</h3>
            <div class="filter-controls">
                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">
                        <span>📋</span> 全部 (<span id="count-all">0</span>)
                    </button>
                    <button class="filter-tab" data-filter="liked">
                        <span>⭐</span> 已收藏 (<span id="count-liked">0</span>)
                    </button>
                    <button class="filter-tab" data-filter="disliked">
                        <span>🗑️</span> 标记删除 (<span id="count-disliked">0</span>)
                    </button>
                    <button class="filter-tab" data-filter="unprocessed">
                        <span>📷</span> 未处理 (<span id="count-unprocessed">0</span>)
                    </button>
                </div>
                <div class="sort-controls">
                    <select id="sort-select">
                        <option value="newest">📅 最新生成</option>
                        <option value="oldest">📅 最早生成</option>
                        <option value="style">🎨 按风格分组</option>
                    </select>
                </div>
            </div>

        </div>

        <!-- 多选工具栏 -->
        <div id="multi-select-section" class="multi-select-toolbar" style="display: none;">
            <div class="toolbar-content">
                <div class="selected-info">
                    <span class="toolbar-icon">🎬</span>
                    <span class="selected-text">
                        已选择 <span id="selected-count">0</span> 张图片
                    </span>
                    <span class="selected-hint">按选择顺序生成过渡视频</span>
                </div>
                <div class="toolbar-actions">
                    <button id="generate-transition-video-btn" class="btn btn-primary" disabled>
                        🎥 生成过渡视频
                    </button>
                    <button id="cancel-multi-select-btn" class="btn btn-secondary">
                        ❌ 取消多选
                    </button>
                </div>
            </div>
        </div>

        <!-- 生成图片网格 -->
        <div class="generated-grid" id="generated-grid">
            <!-- 动态加载生成的图片 -->
        </div>

        <!-- 视频任务区域 -->
        <div class="video-tasks-section">
            <h3>🎬 视频生成任务</h3>
            <div id="video-tasks-container">
                <!-- 动态加载视频任务 -->
            </div>
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <p>正在加载图片...</p>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="empty-state" style="display: none;">
            <div class="empty-icon">🎨</div>
            <h3>还没有生成任何图片</h3>
            <p>使用这张图片去首页开始生成吧！</p>
            <a href="/" class="btn btn-primary">🚀 开始生成</a>
        </div>
    </div>

    <!-- 图片查看模态框 -->
    <div class="modal" id="image-modal">
        <div class="modal-content">
            <span class="modal-close" id="modal-close">&times;</span>
            <img id="modal-image" src="" alt="图片预览">
            <div class="modal-info">
                <h3 id="modal-title">图片标题</h3>
                <div class="modal-stats">
                    <span id="modal-style">风格</span>
                    <span id="modal-time">生成时间</span>
                </div>
                <div class="modal-actions">
                    <button id="modal-download" class="btn btn-secondary">💾 下载</button>
                </div>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/details.js') }}?v=15"></script>
</body>
</html>
