<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>可灵AI图生图工具</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1>🎨 可灵AI图生图工具</h1>
                <div class="nav-links">
                    <a href="/" class="nav-link active">🏠 首页</a>
                    <a href="/gallery" class="nav-link">🖼️ 画廊</a>
                </div>
            </div>
            <div class="balance-info">
                💰 <span id="balance">加载中...</span> 积分
            </div>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 步骤1: 上传图片 -->
            <section class="upload-section">
                <h2><span class="step-number">1</span> 上传参考图片</h2>
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <div class="upload-icon">📁</div>
                        <p>拖拽图片到此处或点击选择</p>
                        <p class="upload-hint">支持 PNG, JPG, JPEG, GIF, BMP, WEBP 格式</p>
                    </div>
                    <input type="file" id="fileInput" accept="image/*" style="display: none;">
                </div>
                <div class="uploaded-image" id="uploadedImage" style="display: none;">
                    <div class="image-container">
                        <img id="previewImage" src="" alt="预览图片">
                    </div>
                    <div class="image-info">
                        <span id="imageName"></span>
                        <button class="btn-remove" id="removeImage">
                            ✕
                        </button>
                    </div>
                </div>
            </section>

            <!-- 步骤2: 选择提示词 -->
            <section class="prompt-section">
                <h2><span class="step-number">2</span> 选择风格提示词</h2>
                <div class="prompt-templates">
                    {% for template in prompt_templates %}
                    <div class="prompt-card" data-id="{{ template.id }}">
                        <div class="prompt-header">
                            <input type="checkbox" id="prompt_{{ template.id }}" class="prompt-checkbox">
                            <label for="prompt_{{ template.id }}" class="prompt-title">
                                {{ template.name }}
                            </label>
                        </div>
                        <div class="prompt-full" style="display: none;">
                            {{ template.prompt }}
                        </div>
                        <button class="btn-expand" onclick="togglePrompt('{{ template.id }}')">
                            ▼ 展开
                        </button>
                    </div>
                    {% endfor %}
                </div>
            </section>

            <!-- 步骤3: 生成图片 -->
            <section class="generate-section">
                <h2><span class="step-number">3</span> 生成图片</h2>
                <button class="btn-generate" id="generateBtn" disabled>
                    🎨 开始生成
                </button>
                <div class="progress-info" id="progressInfo" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>
            </section>

            <!-- 步骤4: 结果展示 -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-header">
                    <h2><span class="step-number">4</span> 生成结果</h2>
                    <button class="btn-clear-results" id="clearResultsBtn" title="清空所有结果">
                        🗑️ 清空结果
                    </button>
                </div>
                <div class="results-grid" id="resultsGrid"></div>
            </section>
        </main>
    </div>

    <!-- 图片查看模态框 -->
    <div class="image-modal" id="imageModal" style="display: none;">
        <div class="image-modal-content">
            <span class="image-modal-close" id="modalClose">&times;</span>
            <img id="modalImage" src="" alt="大图预览">
            <div class="image-modal-info">
                <h3 id="modalTitle"></h3>
                <button class="btn-download" id="modalDownload">
                    💾 下载图片
                </button>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/script.js') }}?v=12"></script>
</body>
</html>
