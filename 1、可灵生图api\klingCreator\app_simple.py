#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI图生图Web应用 - 简化版本（无数据库依赖）
"""

import os
import json
import time
import uuid
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
from kling import ImageGen
from cookie_utils import get_cookie, check_cookie
import requests

app = Flask(__name__)
app.config['SECRET_KEY'] = 'kling-ai-image-generator'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 配置目录
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'web_output'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# 提示词预设
PROMPT_TEMPLATES = [
    {
        "id": "bio_building",
        "name": "生物发光建筑",
        "prompt": "一座完全由生物发光植物构成的奇幻建筑，孤立在纯黑色背景中。巨大的、发光的奇异花朵和流光溢彩的藤蔓构成了建筑的主体结构，半透明的叶片下透出柔和的内部光芒。空气中漂浮着由它散发出的点点荧光。主色调为梦幻般的翠绿色、宝蓝色和淡紫色，充满生命力。幻想概念艺术，强烈的发光效果，细节丰富，固定视角。"
    },
    {
        "id": "steampunk_fortress",
        "name": "蒸汽朋克堡垒",
        "prompt": "一座孤立在纯黑色空间中的蒸汽朋克机械堡垒。建筑内部的锅炉透过格栅和窗口发出炽热的橙红色光芒，裸露的黄铜管道和玻璃真空管闪烁着微光。复杂的齿轮结构缓慢转动，表面有精密的金属质感和铆钉。整体色调为浓郁的古铜色和炽热的橙色，充满工业动力美感。数字绘画，高度详细，4K，固定机位，纯黑色背景。"
    },
    {
        "id": "cyberpunk_style",
        "name": "赛博朋克风格",
        "prompt": "保留黑色底背景，楼体的风格转换成赛博朋克风格，绚丽多彩，线条丰富"
    }
]

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def sanitize_filename_keep_chinese(filename):
    """清理文件名，保留中文字符"""
    import re
    # 移除不安全的字符，但保留中文、英文、数字、点、横线、下划线
    safe_chars = re.sub(r'[<>:"/\\|?*]', '', filename)
    # 移除多余的空格
    safe_chars = re.sub(r'\s+', ' ', safe_chars).strip()
    # 如果文件名为空或只有扩展名，使用默认名称
    if not safe_chars or safe_chars.startswith('.'):
        safe_chars = f"image_{uuid.uuid4().hex[:8]}{safe_chars}"
    return safe_chars

def get_unique_filename(base_name, extension, output_dir):
    """生成唯一的文件名，避免覆盖，保留中文"""
    # 清理基础文件名，保留中文
    clean_base_name = sanitize_filename_keep_chinese(base_name)

    counter = 1
    while True:
        filename = f"{clean_base_name}_{counter}.{extension}"
        filepath = os.path.join(output_dir, filename)
        if not os.path.exists(filepath):
            return filename
        counter += 1

class WebImageGenerator:
    """Web版图生图生成器"""
    
    def __init__(self):
        self.cookie = get_cookie()
        if not self.cookie or not check_cookie():
            raise Exception("Cookie无效或不存在")
        self.generator = ImageGen(self.cookie)
    
    def generate_image(self, image_path, prompt):
        """生成单张图片"""
        # 设置请求头
        self.generator.session.headers.update({
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh",
            "content-type": "application/json",
            "time-zone": "Asia/Shanghai",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        })
        
        # 上传图片
        image_payload_url = self.generator.image_uploader(image_path)
        
        # 构建请求载荷
        payload = {
            "type": "mmu_img2img_aiweb",
            "inputs": [
                {
                    "inputType": "URL",
                    "url": image_payload_url,
                    "name": "input"
                }
            ],
            "arguments": [
                {
                    "name": "biz",
                    "value": "klingai"
                },
                {
                    "name": "prompt",
                    "value": prompt
                },
                {
                    "name": "imageCount",
                    "value": "1"
                },
                {
                    "name": "kolors_version",
                    "value": "2.0"
                },
                {
                    "name": "style",
                    "value": "默认"
                },
                {
                    "name": "referenceType",
                    "value": "mmu_img2img_aiweb_v20_stylize"
                }
            ]
        }
        
        # 发送请求
        response = self.generator.session.post(
            self.generator.submit_url,
            json=payload,
            headers=self.generator.session.headers
        )
        
        if not response.ok:
            raise Exception(f"请求失败: {response.status_code} - {response.text}")
        
        response_body = response.json()
        data = response_body.get("data")
        
        if data and data.get("status") == 7:
            message = data.get("message")
            raise Exception(f"请求失败: {message}")
        
        request_id = data.get("task", {}).get("id") if data else None
        
        if not request_id:
            raise Exception("无法获取请求ID")
        
        # 等待结果
        start_wait = time.time()
        while True:
            if int(time.time() - start_wait) > 600:  # 10分钟超时
                raise Exception("请求超时")
            
            image_data, status = self.generator.fetch_metadata(request_id)
            
            if status.name == "PENDING":
                time.sleep(3)
            elif status.name == "FAILED":
                raise Exception("生成失败")
            else:
                works = image_data.get("works", [])
                if not works:
                    raise Exception("未找到生成的图片")
                
                resource = works[0].get("resource", {}).get("resource")
                if resource:
                    return resource
                else:
                    raise Exception("无法获取图片URL")

@app.route('/')
def index():
    """主页"""
    return render_template('index.html', prompt_templates=PROMPT_TEMPLATES)

@app.route('/admin')
def admin():
    """管理员页面（简化版本）"""
    return '''
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <title>管理员页面</title>
        <style>
            body { font-family: Arial, sans-serif; padding: 40px; text-align: center; }
            .notice { background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1>🛡️ 管理员页面</h1>
        <div class="notice">
            <h3>⚠️ 功能暂时不可用</h3>
            <p>管理员功能需要数据库支持，当前运行的是简化版本。</p>
            <p>如需使用完整功能，请配置MySQL数据库后使用完整版本。</p>
        </div>
        <a href="/" style="color: #007bff; text-decoration: none;">🏠 返回主页</a>
    </body>
    </html>
    '''

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """上传文件"""
    try:
        print("收到上传请求")
        
        if 'file' not in request.files:
            return jsonify({'error': '没有文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if file and allowed_file(file.filename):
            # 保留中文文件名
            original_filename = file.filename
            print(f"原始文件名: {original_filename}")

            # 使用新的文件名处理，保留中文字符
            name, ext = os.path.splitext(original_filename)
            safe_name = sanitize_filename_keep_chinese(name)
            unique_id = str(uuid.uuid4())[:8]
            unique_filename = f"{safe_name}_{unique_id}.{ext}"

            filepath = os.path.join(UPLOAD_FOLDER, unique_filename)
            print(f"保存路径: {filepath}")
            file.save(filepath)

            return jsonify({
                'success': True,
                'filename': unique_filename,
                'original_name': original_filename,
                'safe_name': safe_name
            })
        else:
            return jsonify({'error': '不支持的文件格式'}), 400
            
    except Exception as e:
        print(f"上传异常: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/generate', methods=['POST'])
def generate_images():
    """生成图片"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        prompts = data.get('prompts', [])
        
        if not filename or not prompts:
            return jsonify({'error': '缺少必要参数'}), 400
        
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        if not os.path.exists(filepath):
            return jsonify({'error': '文件不存在'}), 400
        
        # 初始化生成器
        generator = WebImageGenerator()
        
        # 获取原始文件名（不含扩展名），保留中文
        original_name = data.get('original_name', filename)
        base_name = os.path.splitext(original_name)[0]
        print(f"原始文件名: {original_name}, 基础名称: {base_name}")
        
        results = []
        
        for i, prompt in enumerate(prompts):
            try:
                # 生成图片
                image_url = generator.generate_image(filepath, prompt)
                
                # 下载图片
                response = requests.get(image_url)
                if response.status_code == 200:
                    # 生成唯一文件名
                    output_filename = get_unique_filename(base_name, 'png', OUTPUT_FOLDER)
                    output_path = os.path.join(OUTPUT_FOLDER, output_filename)
                    
                    with open(output_path, 'wb') as f:
                        f.write(response.content)
                    
                    results.append({
                        'success': True,
                        'filename': output_filename,
                        'prompt': prompt,
                        'index': i
                    })
                else:
                    results.append({
                        'success': False,
                        'error': '下载失败',
                        'prompt': prompt,
                        'index': i
                    })
                    
            except Exception as e:
                results.append({
                    'success': False,
                    'error': str(e),
                    'prompt': prompt,
                    'index': i
                })
        
        return jsonify({'results': results})
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/image/<filename>')
def get_image(filename):
    """获取生成的图片"""
    filepath = os.path.join(OUTPUT_FOLDER, filename)
    if os.path.exists(filepath):
        return send_file(filepath)
    return jsonify({'error': '文件不存在'}), 404

@app.route('/api/balance')
def get_balance():
    """获取账户余额"""
    try:
        cookie = get_cookie()
        if not cookie or not check_cookie():
            return jsonify({'error': 'Cookie无效'}), 400
        
        generator = ImageGen(cookie)
        balance = generator.get_account_point()
        return jsonify({'balance': balance})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 简化版本的历史记录（基于文件系统）
@app.route('/api/history')
def get_history():
    """获取生成历史（简化版本）"""
    try:
        # 简单返回空历史，避免数据库依赖
        return jsonify({'records': []})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 启动可灵AI图生图Web应用（简化版本）")
    print("📊 功能状态:")
    print("   ✅ 图片上传")
    print("   ✅ 图片生成") 
    print("   ✅ 结果展示")
    print("   ⚠️  历史记录（已禁用，需要数据库）")
    print("   ⚠️  点赞点踩（已禁用，需要数据库）")
    print()
    app.run(debug=True, host='0.0.0.0', port=5000)
