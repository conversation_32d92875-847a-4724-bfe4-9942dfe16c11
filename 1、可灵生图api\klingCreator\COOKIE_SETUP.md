# Cookie设置指南

## 📝 概述

本项目现在使用 `cookie.txt` 文件来存储可灵AI的认证信息，不再依赖环境变量。

## 🔧 设置步骤

### 1. 获取Cookie

1. 访问 [可灵AI官网](https://klingai.kuaishou.com/) 或 [国际版](https://klingai.com/)
2. 登录你的账号
3. 按 `F12` 打开开发者工具
4. 切换到 `Network`（网络）标签
5. 在网站上进行任何操作（如点击生成图片）
6. 在网络请求中找到任意一个请求
7. 在请求头中找到 `Cookie:` 字段
8. 复制完整的Cookie值

### 2. 创建cookie.txt文件

在项目根目录（`klingCreator/`）下创建 `cookie.txt` 文件，将复制的Cookie字符串粘贴进去。

**示例格式：**
```
did=web_xxx; __risk_web_device_id=xxx; KLING_LAST_ACCESS_REGION=cn; userId=xxx; kuaishou.ai.portal_st=xxx; kuaishou.ai.portal_ph=xxx
```

### 3. 验证设置

运行任意工具验证Cookie是否正确：
```bash
python diagnose_cookie.py
```

## 🛠️ 支持Cookie的工具

以下工具已更新为从 `cookie.txt` 读取Cookie：

- ✅ `image_to_image.py` - 交互式图生图工具
- ✅ `diagnose_cookie.py` - Cookie诊断工具
- ✅ `generate_image.py` - 图片生成示例
- ✅ `quick_test.py` - 快速测试工具

## 🔒 安全说明

- `cookie.txt` 文件已添加到 `.gitignore`，不会被提交到Git仓库
- Cookie包含敏感信息，请勿分享给他人
- 如果Cookie过期，请重新获取并更新 `cookie.txt` 文件

## 🔄 更新Cookie

如果需要更新Cookie：

1. 重新从网站获取新的Cookie
2. 直接编辑 `cookie.txt` 文件
3. 或者使用 `cookie_utils.py` 中的 `update_cookie()` 函数

## ❓ 常见问题

### Q: 提示"无法读取cookie"
A: 检查 `cookie.txt` 文件是否存在于项目根目录

### Q: 提示"Cookie格式不正确"
A: 确保Cookie包含必要字段：`userId` 和 `kuaishou.ai.portal_st`

### Q: 运行时提示认证失败
A: Cookie可能已过期，请重新获取

## 🔧 工具函数

`cookie_utils.py` 提供以下函数：

- `get_cookie()` - 读取cookie.txt文件
- `check_cookie()` - 检查cookie格式
- `update_cookie(new_cookie)` - 更新cookie.txt文件
