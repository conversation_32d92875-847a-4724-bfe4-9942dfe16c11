# 导入setuptools包中的find_packages和setup函数，用于Python包的构建和分发
from setuptools import find_packages, setup

# 调用setup函数配置包的安装信息
setup(
    # 包名称，用于pip安装时的标识符
    name="kling-creator",
    # 包版本号，遵循语义化版本控制
    version="0.5.0",
    # 包作者姓名
    author="yihong0618",
    # 包作者邮箱地址
    author_email="<EMAIL>",
    # 包的简短描述，说明这是一个可灵AI视频生成的逆向工程API
    description="High quality video generation by https://klingai.kuaishou.com/. Reverse engineered API.",
    # 项目的GitHub仓库地址
    url="https://github.com/yihong0618/klingCreator",
    # 定义包的依赖项列表，这些包会在安装时自动安装
    install_requires=[
        # HTTP请求库，用于与可灵AI API进行网络通信
        "requests",
        # 伪造用户代理库，版本要求>=2.0.3，用于模拟浏览器请求
        "fake-useragent>=2.0.3",
        # 富文本终端输出库，用于美化控制台输出
        "rich",
    ],
    # 自动查找所有包，但排除tests相关的测试包
    packages=find_packages(exclude=["tests", "tests.*"]),
    # 定义命令行入口点，安装后可以在终端使用"kling"命令
    entry_points={
        # 控制台脚本配置，将"kling"命令映射到kling.kling模块的main函数
        "console_scripts": ["kling = kling.kling:main"],
    },
    # 包的分类器，用于在PyPI上对包进行分类和标记
    classifiers=[
        # 目标受众：开发者
        "Intended Audience :: Developers",
        # 主题：软件开发库和Python模块
        "Topic :: Software Development :: Libraries :: Python Modules",
        # 支持的Python版本：3.9
        "Programming Language :: Python :: 3.9",
        # 支持的Python版本：3.10
        "Programming Language :: Python :: 3.10",
        # 支持的Python版本：3.11
        "Programming Language :: Python :: 3.11",
        # 支持的Python版本：3.12
        "Programming Language :: Python :: 3.12",
        # 支持的Python版本：3.13
        "Programming Language :: Python :: 3.13",
    ],
)
