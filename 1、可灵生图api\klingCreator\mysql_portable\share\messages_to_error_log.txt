# Copyright (c) 2017, 2023, Oracle and/or its affiliates.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License, version 2.0,
# as published by the Free Software Foundation.
#
# This program is also distributed with certain software (including
# but not limited to OpenSSL) that is licensed under separate terms,
# as designated in a particular file or component or in included license
# documentation.  The authors of MySQL hereby grant you an additional
# permission to link the program and your derivative works with the
# separately licensed software that they have included with MySQL.
#
# Without limiting anything contained in the foregoing, this file,
# which is part of C Driver for MySQL (Connector/C), is also subject to the
# Universal FOSS Exception, version 1.0, a copy of which can be found at
# http://oss.oracle.com/licenses/universal-foss-exception.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License, version 2.0, for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA

################################################################################
# DO NOT add server-to-client messages here;
# they go in messages_to_clients.txt
# in the same directory as this file.
#
# This file is for messages intended for the error log only.
#
# See the FAQ in errmsg_readme.txt in the
# same directory as this file for more
# information.
################################################################################


# "languages" and "default-language" directives should not be set in this
# file; their values are carried over from messages_to_clients.txt.


################################################################################
#
# Start of 8.0 error messages (error log).
#
# The build process automatically starts with this offset
# for messages intended for the error-log. Setting it again
# here would be harmless; changing it would not as this
# offset is mentioned in the documentation and #defined in
# the source:
# start-error-number 10000

ER_PARSER_TRACE XX999
  eng "Parser saw: %s"

ER_BOOTSTRAP_CANT_THREAD
  eng "Can't create thread to handle bootstrap (errno: %d)"

ER_TRIGGER_INVALID_VALUE
  eng "Trigger for table '%s'.'%s': invalid %s value (%s)."

ER_OPT_WRONG_TREE
  eng "Wrong tree: %s"

ER_DD_FAILSAFE
  eng "Error: Invalid %s"

ER_DD_NO_WRITES_NO_REPOPULATION
  eng "Skip re-populating collations and character sets tables in %s%sread-only mode."

ER_DD_VERSION_FOUND
  eng "Using data dictionary with version '%d'."

ER_DD_VERSION_INSTALLED
  eng "Installed data dictionary with version %d"

ER_DD_VERSION_UNSUPPORTED
  eng "Data Dictionary version '%d' not supported."

OBSOLETE_ER_LOG_SYSLOG_FACILITY_FAIL
  eng "Failed to set syslog facility to \"%s\", setting to \"%s\" (%d) instead."

ER_LOG_SYSLOG_CANNOT_OPEN
  eng "Cannot open %s; check privileges, or remove syseventlog from --log-error-services!"

ER_LOG_SLOW_CANNOT_OPEN
  eng " either restart the query logging by using \"SET GLOBAL SLOW_QUERY_LOG=ON\" or"

ER_LOG_GENERAL_CANNOT_OPEN
  eng " either restart the query logging by using \"SET GLOBAL GENERAL_LOG=ON\" or"

ER_LOG_CANNOT_WRITE
  eng "Failed to write to %s: %s"

ER_RPL_ZOMBIE_ENCOUNTERED
  eng "While initializing dump thread for replica with %s <%s>, found a zombie dump thread with the same %s. Source is killing the zombie dump thread(%u)."

ER_RPL_GTID_TABLE_CANNOT_OPEN
  eng "Gtid table is not ready to be used. Table '%s.%s' cannot be opened."

ER_SYSTEM_SCHEMA_NOT_FOUND
  eng "System schema directory does not exist."

ER_DD_INIT_UPGRADE_FAILED
  eng "Error in initializing dictionary, upgrade will do a cleanup and exit"

ER_VIEW_UNKNOWN_CHARSET_OR_COLLATION
  eng "View '%s'.'%s': unknown charset name and/or collation name (client: '%s'; connection: '%s')."

ER_DD_VIEW_CANT_ALLOC_CHARSET
  eng "Error in allocating memory for character set name for view %s.%s."

ER_DD_INIT_FAILED
  eng "Data Dictionary initialization failed."

ER_DD_UPDATING_PLUGIN_MD_FAILED
  eng "Failed to update plugin metadata in dictionary tables."

ER_DD_POPULATING_TABLES_FAILED
  eng "Failed to Populate DD tables."

ER_DD_VIEW_CANT_CREATE
  eng "Error in Creating View %s.%s"

ER_DD_METADATA_NOT_FOUND
  eng "Unable to start server. Cannot find the meta data for data dictionary table '%s'."

ER_DD_CACHE_NOT_EMPTY_AT_SHUTDOWN
  eng "Dictionary cache not empty at shutdown."

ER_DD_OBJECT_REMAINS
  eng "Dictionary objects used but not released."

ER_DD_OBJECT_REMAINS_IN_RELEASER
  eng "Dictionary objects left in default releaser."

ER_DD_OBJECT_RELEASER_REMAINS
  eng "Dictionary object auto releaser not deleted"

ER_DD_CANT_GET_OBJECT_KEY
  eng "Error: Unable to create primary object key"

ER_DD_CANT_CREATE_OBJECT_KEY
  eng "Error: Unable to create object key"

ER_CANT_CREATE_HANDLE_MGR_THREAD
  eng "Can't create handle_manager thread (errno= %d)"

ER_RPL_REPO_HAS_GAPS
  eng "It is not possible to change the type of the relay log's repository because there are workers' repositories with gaps. Please, fix the gaps first before doing such change."

ER_INVALID_VALUE_FOR_ENFORCE_GTID_CONSISTENCY
  eng "option 'enforce-gtid-consistency': value '%s' was not recognized. Setting enforce-gtid-consistency to OFF."

ER_CHANGED_ENFORCE_GTID_CONSISTENCY
  eng "Changed ENFORCE_GTID_CONSISTENCY from %s to %s."

ER_CHANGED_GTID_MODE
 eng "Changed GTID_MODE from %s to %s."

ER_DISABLED_STORAGE_ENGINE_AS_DEFAULT
  eng "%s is set to a disabled storage engine %s."

ER_DEBUG_SYNC_HIT
  eng "Debug sync points hit:                   %22s"

ER_DEBUG_SYNC_EXECUTED
  eng "Debug sync points executed:              %22s"

ER_DEBUG_SYNC_THREAD_MAX
  eng "Debug sync points max active per thread: %22s"

ER_DEBUG_SYNC_OOM
  eng "Debug Sync Facility disabled due to lack of memory."

ER_CANT_INIT_TC_LOG
  eng "Can't init tc log"

ER_EVENT_CANT_INIT_QUEUE
  eng "Event Scheduler: Can't initialize the execution queue"

ER_EVENT_PURGING_QUEUE
  eng "Event Scheduler: Purging the queue. %u events"

ER_EVENT_LAST_EXECUTION
  eng "Event Scheduler: Last execution of %s.%s. %s"

ER_EVENT_MESSAGE_STACK
  eng "%*s"

ER_EVENT_EXECUTION_FAILED
  eng "Event Scheduler: [%s].[%s.%s] event execution failed."

ER_CANT_INIT_SCHEDULER_THREAD
  eng "Event Scheduler: Cannot initialize the scheduler thread"

ER_SCHEDULER_STOPPED
  eng "Event Scheduler: Stopped"

ER_CANT_CREATE_SCHEDULER_THREAD
  eng "Event scheduler: Failed to start scheduler, Can not create thread for event scheduler (errno=%d)"

ER_SCHEDULER_WAITING
  eng "Event Scheduler: Waiting for the scheduler thread to reply"

ER_SCHEDULER_STARTED
  eng "Event Scheduler: scheduler thread started with id %u"

ER_SCHEDULER_STOPPING_FAILED_TO_GET_EVENT
  eng "Event Scheduler: Serious error during getting next event to execute. Stopping"

ER_SCHEDULER_STOPPING_FAILED_TO_CREATE_WORKER
  eng "Event_scheduler::execute_top: Can not create event worker thread (errno=%d). Stopping event scheduler"

ER_SCHEDULER_KILLING
  eng "Event Scheduler: Killing the scheduler thread, thread id %u"

ER_UNABLE_TO_RESOLVE_IP
  eng "IP address '%s' could not be resolved: %s"

ER_UNABLE_TO_RESOLVE_HOSTNAME
  eng "Host name '%s' could not be resolved: %s"

ER_HOSTNAME_RESEMBLES_IPV4
  eng "IP address '%s' has been resolved to the host name '%s', which resembles IPv4-address itself."

ER_HOSTNAME_DOESNT_RESOLVE_TO
  eng "Hostname '%s' does not resolve to '%s'."

ER_ADDRESSES_FOR_HOSTNAME_HEADER
  eng "Hostname '%s' has the following IP addresses:"

ER_ADDRESSES_FOR_HOSTNAME_LIST_ITEM
  eng " - %s"

ER_TRG_WITHOUT_DEFINER
  eng "Definer clause is missing in Trigger of Table %s. Rebuild Trigger to fix definer."

ER_TRG_NO_CLIENT_CHARSET
  eng "Client character set is missing for trigger of table %s. Using default character set."

ER_PARSING_VIEW
  eng "Error in parsing view %s.%s"

ER_COMPONENTS_INFRASTRUCTURE_BOOTSTRAP
  eng "Failed to bootstrap components infrastructure."

ER_COMPONENTS_INFRASTRUCTURE_SHUTDOWN
  eng "Failed to shutdown components infrastructure."

ER_COMPONENTS_PERSIST_LOADER_BOOTSTRAP
  eng "Failed to bootstrap persistent components loader."

ER_DEPART_WITH_GRACE
  eng "Giving %d client threads a chance to die gracefully"

ER_CA_SELF_SIGNED
  eng "CA certificate %s is self signed."

ER_SSL_LIBRARY_ERROR
   eng "Failed to set up SSL because of the following SSL library error: %s"

ER_NO_THD_NO_UUID
  eng "Failed to generate a server UUID because it is failed to allocate the THD."

ER_UUID_SALT
  eng "Salting uuid generator variables, current_pid: %lu, server_start_time: %lu, bytes_sent: %llu, "

ER_UUID_IS
  eng "Generated uuid: '%s', server_start_time: %lu, bytes_sent: %llu"

ER_UUID_INVALID
  eng "The server_uuid stored in auto.cnf file is not a valid UUID."

ER_UUID_SCRUB
  eng "Garbage characters found at the end of the server_uuid value in auto.cnf file. It should be of length '%d' (UUID_LENGTH). Clear it and restart the server. "

ER_CREATING_NEW_UUID
  eng "No existing UUID has been found, so we assume that this is the first time that this server has been started. Generating a new UUID: %s."

ER_CANT_CREATE_UUID
  eng "Initialization of the server's UUID failed because it could not be read from the auto.cnf file. If this is a new server, the initialization failed because it was not possible to generate a new UUID."

ER_UNKNOWN_UNSUPPORTED_STORAGE_ENGINE
  eng "Unknown/unsupported storage engine: %s"

ER_SECURE_AUTH_VALUE_UNSUPPORTED
  eng "Unsupported value 0 for secure-auth"

ER_INVALID_INSTRUMENT
  eng "Invalid instrument name or value for performance_schema_instrument '%s'",

ER_INNODB_MANDATORY
  eng "The use of InnoDB is mandatory since MySQL 5.7. The former options like '--innodb=0/1/OFF/ON' or '--skip-innodb' are ignored."

OBSOLETE_ER_INNODB_CANNOT_BE_IGNORED
  eng "ignore-builtin-innodb is ignored and will be removed in future releases."

OBSOLETE_ER_OLD_PASSWORDS_NO_MIDDLE_GROUND
  eng "Invalid old_passwords mode: 1. Valid values are 2 and 0"

ER_VERBOSE_REQUIRES_HELP
  eng "--verbose is for use with --help; did you mean --log-error-verbosity?"

ER_POINTLESS_WITHOUT_SLOWLOG
  eng "options --log-slow-admin-statements, --log-queries-not-using-indexes and --log-slow-replica-statements have no effect if --slow-query-log is not set"

ER_WASTEFUL_NET_BUFFER_SIZE
  eng "net_buffer_length (%lu) is set to be larger than max_allowed_packet (%lu). Please rectify."

ER_DEPRECATED_TIMESTAMP_IMPLICIT_DEFAULTS
  eng "TIMESTAMP with implicit DEFAULT value is deprecated. Please use --explicit_defaults_for_timestamp server option (see documentation for more details)."

ER_FT_BOOL_SYNTAX_INVALID
  eng "Invalid ft-boolean-syntax string: %s"

ER_CREDENTIALLESS_AUTO_USER_BAD
  eng "'NO_AUTO_CREATE_USER' sql mode was not set."

ER_CONNECTION_HANDLING_OOM
  eng "Could not allocate memory for connection handling"

ER_THREAD_HANDLING_OOM
  eng "Could not allocate memory for thread handling"

ER_CANT_CREATE_TEST_FILE
  eng "Can't create test file %s"

ER_CANT_CREATE_PID_FILE
  eng "Can't start server: can't create PID file: %s"

ER_CANT_REMOVE_PID_FILE
  eng "Unable to delete pid file: %s"

ER_CANT_CREATE_SHUTDOWN_THREAD
  eng "Can't create thread to handle shutdown requests (errno= %d)"

ER_SEC_FILE_PRIV_CANT_ACCESS_DIR
  eng "Failed to access directory for --secure-file-priv. Please make sure that directory exists and is accessible by MySQL Server. Supplied value : %s"

ER_SEC_FILE_PRIV_IGNORED
  eng "Ignoring --secure-file-priv value as server is running with --initialize(-insecure)."

ER_SEC_FILE_PRIV_EMPTY
  eng "Insecure configuration for --secure-file-priv: Current value does not restrict location of generated files. Consider setting it to a valid, non-empty path."

ER_SEC_FILE_PRIV_NULL
  eng "--secure-file-priv is set to NULL. Operations related to importing and exporting data are disabled"

ER_SEC_FILE_PRIV_DIRECTORY_INSECURE
  eng "Insecure configuration for --secure-file-priv: %s is accessible through --secure-file-priv. Consider choosing a different directory."

ER_SEC_FILE_PRIV_CANT_STAT
  eng "Failed to get stat for directory pointed out by --secure-file-priv"

ER_SEC_FILE_PRIV_DIRECTORY_PERMISSIONS
  eng "Insecure configuration for --secure-file-priv: Location is accessible to all OS users. Consider choosing a different directory."

ER_SEC_FILE_PRIV_ARGUMENT_TOO_LONG
  eng "Value for --secure-file-priv is longer than maximum limit of %d"

ER_CANT_CREATE_NAMED_PIPES_THREAD
  eng "Can't create thread to handle named pipes (errno= %d)"

ER_CANT_CREATE_TCPIP_THREAD
  eng "Can't create thread to handle TCP/IP (errno= %d)"

ER_CANT_CREATE_SHM_THREAD
  eng "Can't create thread to handle shared memory (errno= %d)"

ER_CANT_CREATE_INTERRUPT_THREAD
  eng "Can't create interrupt-thread (error %d, errno: %d)"

ER_WRITABLE_CONFIG_REMOVED
  eng "World-writable config file '%s' has been removed."

ER_CORE_VALUES
  eng "setrlimit could not change the size of core files to 'infinity';  We may not be able to generate a core file on signals"

ER_WRONG_DATETIME_SPEC
  eng "Wrong date/time format specifier: %s"

ER_RPL_BINLOG_FILTERS_OOM
  eng "Could not allocate replication and binlog filters: %s"

ER_KEYCACHE_OOM
  eng "Cannot allocate the keycache"

ER_CONFIRMING_THE_FUTURE
  eng "Current time has got past year 2038. Validating current time with %d iterations before initiating the normal server shutdown process."

ER_BACK_IN_TIME
  eng "Iteration %d: Obtained valid current time from system"

ER_FUTURE_DATE
  eng "Iteration %d: Current time obtained from system is greater than 2038"

ER_UNSUPPORTED_DATE
  eng "This MySQL server doesn't support dates later then 2038"

ER_STARTING_AS
  eng "%s (mysqld %s) starting as process %lu"

ER_SHUTTING_DOWN_REPLICA_THREADS
  eng "Shutting down replica threads"

ER_DISCONNECTING_REMAINING_CLIENTS
  eng "Forcefully disconnecting %d remaining clients"

ER_ABORTING
  eng "Aborting"

ER_BINLOG_END
  eng "Binlog end"

ER_CALL_ME_LOCALHOST
  eng "gethostname failed, using '%s' as hostname"

ER_USER_REQUIRES_ROOT
  eng "One can only use the --user switch if running as root"

ER_REALLY_RUN_AS_ROOT
  eng "Fatal error: Please read \"Security\" section of the manual to find out how to run mysqld as root!"

ER_USER_WHAT_USER
  eng "Fatal error: Can't change to run as user '%s' ;  Please check that the user exists!"

ER_TRANSPORTS_WHAT_TRANSPORTS
  eng "Server is started with --require-secure-transport=ON but no secure transports (SSL or Shared Memory) are configured."

ER_FAIL_SETGID
  eng "setgid: %s"

ER_FAIL_SETUID
  eng "setuid: %s"

ER_FAIL_SETREGID
  eng "setregid: %s"

ER_FAIL_SETREUID
  eng "setreuid: %s"

ER_FAIL_CHROOT
  eng "chroot: %s"

ER_WIN_LISTEN_BUT_HOW
  eng "TCP/IP, --shared-memory, or --named-pipe should be configured on NT OS"

ER_NOT_RIGHT_NOW
  eng "CTRL-C ignored during startup"

ER_FIXING_CLIENT_CHARSET
  eng "'%s' can not be used as client character set. '%s' will be used as default client character set."

ER_OOM
  eng "Out of memory"

ER_FAILED_TO_LOCK_MEM
  eng "Failed to lock memory. Errno: %d"

ER_MYINIT_FAILED
  eng "my_init() failed."

ER_BEG_INITFILE
  eng "Execution of init_file \'%s\' started."

ER_END_INITFILE
  eng "Execution of init_file \'%s\' ended."

ER_CHANGED_MAX_OPEN_FILES
  eng "Changed limits: max_open_files: %lu (requested %lu)"

ER_CANT_INCREASE_MAX_OPEN_FILES
  eng "Could not increase number of max_open_files to more than %lu (request: %lu)"

ER_CHANGED_MAX_CONNECTIONS
  eng "Changed limits: max_connections: %lu (requested %lu)"

ER_CHANGED_TABLE_OPEN_CACHE
  eng "Changed limits: table_open_cache: %lu (requested %lu)"

ER_THE_USER_ABIDES
  eng "Ignoring user change to '%s' because the user was set to '%s' earlier on the command line"

ER_RPL_CANT_ADD_DO_TABLE
  eng "Could not add do table rule '%s'!"

ER_RPL_CANT_ADD_IGNORE_TABLE
  eng "Could not add ignore table rule '%s'!"

ER_TRACK_VARIABLES_BOGUS
  eng "The variable session_track_system_variables either has duplicate values or invalid values."

ER_EXCESS_ARGUMENTS
  eng "Too many arguments (first extra is '%s')."

ER_VERBOSE_HINT
  eng "Use --verbose --help to get a list of available options!"

ER_CANT_READ_ERRMSGS
  eng "Unable to read errmsg.sys file"

ER_CANT_INIT_DBS
  eng "Can't init databases"

ER_LOG_OUTPUT_CONTRADICTORY
  eng "There were other values specified to log-output besides NONE. Disabling slow and general logs anyway."

ER_NO_CSV_NO_LOG_TABLES
  eng "CSV engine is not present, falling back to the log files"

ER_RPL_REWRITEDB_MISSING_ARROW
  eng "Bad syntax in replicate-rewrite-db - missing '->'!"

ER_RPL_REWRITEDB_EMPTY_FROM
  eng "Bad syntax in replicate-rewrite-db - empty FROM db!"

ER_RPL_REWRITEDB_EMPTY_TO
  eng "Bad syntax in replicate-rewrite-db - empty TO db!"

ER_LOG_FILES_GIVEN_LOG_OUTPUT_IS_TABLE
  eng "Although a path was specified for the %s, log tables are used. To enable logging to files use the --log-output=file option."

ER_LOG_FILE_INVALID
  eng "Invalid value for %s: %s"

ER_LOWER_CASE_TABLE_NAMES_CS_DD_ON_CI_FS_UNSUPPORTED
  eng "The server option 'lower_case_table_names' is configured to use case sensitive table names but the data directory is on a case-insensitive file system which is an unsupported combination. Please consider either using a case sensitive file system for your data directory or switching to a case-insensitive table name mode."

ER_LOWER_CASE_TABLE_NAMES_USING_2
  eng "Setting lower_case_table_names=2 because file system for %s is case insensitive"

ER_LOWER_CASE_TABLE_NAMES_USING_0
  eng "lower_case_table_names was set to 2, even though your the file system '%s' is case sensitive.  Now setting lower_case_table_names to 0 to avoid future problems."

ER_NEED_LOG_BIN
  eng "You need to use --log-bin to make %s work."

ER_NEED_FILE_INSTEAD_OF_DIR
  eng "Path '%s' is a directory name, please specify a file name for %s option"

# Unused since MySQL 8.0.3
ER_LOG_BIN_BETTER_WITH_NAME
  eng "No argument was provided to --log-bin, and --log-bin-index was not used; so replication may break when this MySQL server acts as a source and has his hostname changed!! Please use '--log-bin=%s' to avoid this problem."

ER_BINLOG_NEEDS_SERVERID
  eng "You have enabled the binary log, but you haven't provided the mandatory server-id. Please refer to the proper server start-up parameters documentation"

ER_RPL_CANT_MAKE_PATHS
  eng "Unable to create replication path names: out of memory or path names too long (path name exceeds %d or file name exceeds %d)."

ER_CANT_INITIALIZE_GTID
  eng "Failed to initialize GTID structures."

ER_CANT_INITIALIZE_EARLY_PLUGINS
  eng "Failed to initialize early plugins."

ER_CANT_INITIALIZE_BUILTIN_PLUGINS
  eng "Failed to initialize builtin plugins."

ER_CANT_INITIALIZE_DYNAMIC_PLUGINS
  eng "Failed to initialize dynamic plugins."

ER_PERFSCHEMA_INIT_FAILED
  eng "Performance schema disabled (reason: init failed)."

ER_STACKSIZE_UNEXPECTED
  eng "Asked for %lu thread stack, but got %ld"

OBSOLETE_ER_CANT_SET_DATADIR
  eng "failed to set datadir to %s"

ER_CANT_STAT_DATADIR
  eng "Can't read data directory's stats (%d): %s. Assuming that it's not owned by the same user/group"

ER_CANT_CHOWN_DATADIR
  eng "Can't change data directory owner to %s"

ER_CANT_SET_UP_PERSISTED_VALUES
  eng "Setting persistent options failed."

ER_CANT_SAVE_GTIDS
  eng "Failed to save the set of Global Transaction Identifiers of the last binary log into the mysql.gtid_executed table while the server was shutting down. The next server restart will make another attempt to save Global Transaction Identifiers into the table."

ER_AUTH_CANT_SET_DEFAULT_PLUGIN
  eng "Can't start server: Invalid value for --default-authentication-plugin"

ER_CANT_JOIN_SHUTDOWN_THREAD
  eng "Could not join %sthread. error:%d"

ER_CANT_HASH_DO_AND_IGNORE_RULES
  eng "An error occurred while building do_table and ignore_table rules to hashes for global replication filter."

ER_CANT_OPEN_CA
  eng "Error opening CA certificate file"

ER_CANT_ACCESS_CAPATH
  eng "Error accessing directory pointed by --ssl-capath"

ER_SSL_TRYING_DATADIR_DEFAULTS
  eng "Found %s, %s and %s in data directory. Trying to enable SSL support using them."

ER_AUTO_OPTIONS_FAILED
  eng "Failed to create %s(file: '%s', errno %d)"

ER_CANT_INIT_TIMER
  eng "Failed to initialize timer component (errno %d)."

ER_SERVERID_TOO_LARGE
  eng "server-id configured is too large to represent with server-id-bits configured."

ER_DEFAULT_SE_UNAVAILABLE
  eng "Default%s storage engine (%s) is not available"

ER_CANT_OPEN_ERROR_LOG
  eng "Could not open file '%s' for error logging%s%s"

ER_INVALID_ERROR_LOG_NAME
  eng "Invalid log file name after expanding symlinks: '%s'"

ER_RPL_INFINITY_DENIED
  eng "using --replicate-same-server-id in conjunction with --log-replica-updates is impossible, it would lead to infinite loops in this server."

ER_RPL_INFINITY_IGNORED
  eng "using --replicate-same-server-id in conjunction with --log-replica-updates would lead to infinite loops in this server. However this will be ignored as the --log-bin option is not defined or your server is running with global transaction identiers enabled."

OBSOLETE_ER_NDB_TABLES_NOT_READY
  eng "NDB : Tables not available after %lu seconds. Consider increasing --ndb-wait-setup value"

# could use verbatim, but we want an error-code
ER_TABLE_CHECK_INTACT
  eng "%s"

ER_DD_TABLESPACE_NOT_FOUND
  eng "Unable to start server. The data dictionary tablespace '%s' does not exist."

ER_DD_TRG_CONNECTION_COLLATION_MISSING
  eng "Connection collation is missing for trigger of table %s. Using default connection collation."

ER_DD_TRG_DB_COLLATION_MISSING
  eng "Database collation is missing for trigger of table %s. Using Default character set."

ER_DD_TRG_DEFINER_OOM
  eng "Error in Memory allocation for Definer %s for Trigger."

# ER_TRG_CORRUPTED_FILE
ER_DD_TRG_FILE_UNREADABLE
  eng "Error in reading %s.TRG file."

ER_TRG_CANT_PARSE
  eng "Error in parsing Triggers from %s.TRG file."

ER_DD_TRG_CANT_ADD
  eng "Error in creating DD entry for Trigger %s.%s"

ER_DD_CANT_RESOLVE_VIEW
  eng "Resolving dependency for the view '%s.%s' failed. View is no more valid to use"

ER_DD_VIEW_WITHOUT_DEFINER
  eng "%s.%s has no definer (as per an old view format). Current user is used as definer. Please recreate the view."

ER_PLUGIN_INIT_FAILED
  eng "Plugin '%s' init function returned error."

ER_RPL_TRX_DELEGATES_INIT_FAILED
  eng "Initialization of transaction delegates failed. Please report a bug."

ER_RPL_BINLOG_STORAGE_DELEGATES_INIT_FAILED
  eng "Initialization binlog storage delegates failed. Please report a bug."

ER_RPL_BINLOG_TRANSMIT_DELEGATES_INIT_FAILED
  eng "Initialization of binlog transmit delegates failed. Please report a bug."

ER_RPL_BINLOG_RELAY_DELEGATES_INIT_FAILED
  eng "Initialization binlog relay IO delegates failed. Please report a bug."

ER_RPL_PLUGIN_FUNCTION_FAILED
  eng "Run function '...' in plugin '%s' failed"

ER_SQL_HA_READ_FAILED
  eng "mysql_ha_read: Got error %d when reading table '%s'"

ER_SR_BOGUS_VALUE
  eng "Stored routine '%s'.'%s': invalid value in column %s."

ER_SR_INVALID_CONTEXT
  eng "Invalid creation context '%s.%s'."

ER_READING_TABLE_FAILED
  eng "Got error %d when reading table '%s'"

ER_DES_FILE_WRONG_KEY
  eng "load_des_file:  Found wrong key_number: %c"

OBSOLETE_ER_CANT_SET_PERSISTED
  eng "Failed to set persisted options."

ER_JSON_PARSE_ERROR
  eng "Persisted config file is corrupt. Please ensure mysqld-auto.cnf file is valid JSON."

ER_CONFIG_OPTION_WITHOUT_GROUP
  eng "Found option without preceding group in config file"

ER_VALGRIND_DO_QUICK_LEAK_CHECK
  eng "VALGRIND_DO_QUICK_LEAK_CHECK"

ER_VALGRIND_COUNT_LEAKS
  eng "VALGRIND_COUNT_LEAKS reports %lu leaked bytes for query '%.*s'"

ER_LOAD_DATA_INFILE_FAILED_IN_UNEXPECTED_WAY
  eng "LOAD DATA INFILE in the replica SQL Thread can only read from --replica-load-tmpdir. Please, report a bug."

ER_UNKNOWN_ERROR_NUMBER
  eng "Got unknown error: %d"

ER_UDF_CANT_ALLOC_FOR_STRUCTURES
  eng "Can't allocate memory for udf structures"

ER_UDF_CANT_ALLOC_FOR_FUNCTION
  eng "Can't alloc memory for udf function: '%.64s'"

ER_UDF_INVALID_ROW_IN_FUNCTION_TABLE
  eng "Invalid row in mysql.func table for function '%.64s'"

ER_UDF_CANT_OPEN_FUNCTION_TABLE
  eng "Could not open the mysql.func table. Please perform the MySQL upgrade procedure."

ER_XA_RECOVER_FOUND_TRX_IN_SE
  eng "Found %d prepared transaction(s) in %s"

ER_XA_RECOVER_FOUND_XA_TRX
  eng "Found %d prepared XA transactions"

OBSOLETE_ER_XA_IGNORING_XID
  eng "ignore xid %s"

OBSOLETE_ER_XA_COMMITTING_XID
  eng "commit xid %s"

OBSOLETE_ER_XA_ROLLING_BACK_XID
  eng "rollback xid %s"

ER_XA_STARTING_RECOVERY
  eng "Starting XA crash recovery..."

ER_XA_NO_MULTI_2PC_HEURISTIC_RECOVER
  eng "--tc-heuristic-recover rollback strategy is not safe on systems with more than one 2-phase-commit-capable storage engine. Aborting crash recovery."

ER_XA_RECOVER_EXPLANATION
  eng "Found %d prepared transactions! It means that mysqld was not shut down properly last time and critical recovery information (last binlog or %s file) was manually deleted after a crash. You have to start mysqld with --tc-heuristic-recover switch to commit or rollback pending transactions."

ER_XA_RECOVERY_DONE
  eng "XA crash recovery finished."

ER_TRX_GTID_COLLECT_REJECT
  eng "Failed to collect GTID to send in the response packet!"

ER_SQL_AUTHOR_DEFAULT_ROLES_FAIL
  eng "MYSQL.DEFAULT_ROLES couldn't be updated for authorization identifier %s"

ER_SQL_USER_TABLE_CREATE_WARNING
  eng "Following users were specified in CREATE USER IF NOT EXISTS but they already exist. Corresponding entry in binary log used default authentication plugin '%s' to rewrite authentication information (if any) for them: %s"

ER_SQL_USER_TABLE_ALTER_WARNING
  eng "Following users were specified in ALTER USER IF EXISTS but they do not exist. Corresponding entry in binary log used default authentication plugin '%s' to rewrite authentication information (if any) for them: %s"

ER_ROW_IN_WRONG_PARTITION_PLEASE_REPAIR
  eng "Table '%-192s' corrupted: row in wrong partition: %s -- Please REPAIR the table!"

ER_MYISAM_CRASHED_ERROR_IN_THREAD
  eng "Got an error from thread_id=%u, %s:%d"

ER_MYISAM_CRASHED_ERROR_IN
  eng "Got an error from unknown thread, %s:%d"

ER_TOO_MANY_STORAGE_ENGINES
  eng "Too many storage engines!"

ER_SE_TYPECODE_CONFLICT
  eng "Storage engine '%s' has conflicting typecode. Assigning value %d."

ER_TRX_WRITE_SET_OOM
  eng "Out of memory on transaction write set extraction"

ER_HANDLERTON_OOM
  eng "Unable to allocate memory for plugin '%s' handlerton."

ER_CONN_SHM_LISTENER
  eng "Shared memory setting up listener"

ER_CONN_SHM_CANT_CREATE_SERVICE
  eng "Can't create shared memory service: %s. : %s"

ER_CONN_SHM_CANT_CREATE_CONNECTION
  eng "Can't create shared memory connection: %s. : %s"

ER_CONN_PIP_CANT_CREATE_EVENT
  eng "Can't create event, last error=%u"

ER_CONN_PIP_CANT_CREATE_PIPE
  eng "Can't create new named pipe!: %s"

ER_CONN_PER_THREAD_NO_THREAD
  eng "Can't create thread to handle new connection(errno= %d)"

ER_CONN_TCP_NO_SOCKET
  eng "Failed to create a socket for %s '%s': errno: %d."

ER_CONN_TCP_CREATED
  eng "Server socket created on IP: '%s'."

ER_CONN_TCP_ADDRESS
  eng "Server hostname (bind-address): '%s'; port: %d"

ER_CONN_TCP_IPV6_AVAILABLE
  eng "IPv6 is available."

ER_CONN_TCP_IPV6_UNAVAILABLE
  eng "IPv6 is not available."

ER_CONN_TCP_ERROR_WITH_STRERROR
  eng "Can't create IP socket: %s"

ER_CONN_TCP_CANT_RESOLVE_HOSTNAME
  eng "Can't start server: cannot resolve hostname!"

ER_CONN_TCP_IS_THERE_ANOTHER_USING_PORT
  eng "Do you already have another mysqld server running on port: %d ?"

ER_CONN_UNIX_IS_THERE_ANOTHER_USING_SOCKET
  eng "Do you already have another mysqld server running on socket: %s ?"

ER_CONN_UNIX_PID_CLAIMED_SOCKET_FILE
  eng "Another process with pid %d is using unix socket file."

ER_CONN_TCP_CANT_RESET_V6ONLY
  eng "Failed to reset IPV6_V6ONLY flag (error: %d). The server will listen to IPv6 addresses only."

ER_CONN_TCP_BIND_RETRY
  eng "Retrying bind on TCP/IP port %u"

ER_CONN_TCP_BIND_FAIL
  eng "Can't start server: Bind on TCP/IP port: %s"

ER_CONN_TCP_IP_NOT_LOGGED
  eng "Fails to print out IP-address."

ER_CONN_TCP_RESOLVE_INFO
  eng "  - '%s' resolves to '%s';"

ER_CONN_TCP_START_FAIL
  eng "Can't start server: listen() on TCP/IP port: %s"

ER_CONN_TCP_LISTEN_FAIL
  eng "listen() on TCP/IP failed with error %d"

ER_CONN_UNIX_PATH_TOO_LONG
  eng "The socket file path is too long (> %u): %s"

ER_CONN_UNIX_LOCK_FILE_FAIL
  eng "Unable to setup unix socket lock file."

ER_CONN_UNIX_NO_FD
  eng "Can't start server: UNIX Socket : %s"

ER_CONN_UNIX_NO_BIND_NO_START
  eng "Can't start server : Bind on unix socket: %s"

ER_CONN_UNIX_LISTEN_FAILED
  eng "listen() on Unix socket failed with error %d"

ER_CONN_UNIX_LOCK_FILE_GIVING_UP
  eng "Unable to create unix socket lock file %s after retries."

ER_CONN_UNIX_LOCK_FILE_CANT_CREATE
  eng "Could not create unix socket lock file %s."

ER_CONN_UNIX_LOCK_FILE_CANT_OPEN
  eng "Could not open unix socket lock file %s."

ER_CONN_UNIX_LOCK_FILE_CANT_READ
  eng "Could not read unix socket lock file %s."

ER_CONN_UNIX_LOCK_FILE_EMPTY
  eng "Unix socket lock file is empty %s."

ER_CONN_UNIX_LOCK_FILE_PIDLESS
  eng "Invalid pid in unix socket lock file %s."

ER_CONN_UNIX_LOCK_FILE_CANT_WRITE
  eng "Could not write unix socket lock file %s errno %d."

ER_CONN_UNIX_LOCK_FILE_CANT_DELETE
  eng "Could not remove unix socket lock file %s errno %d."

ER_CONN_UNIX_LOCK_FILE_CANT_SYNC
  eng "Could not sync unix socket lock file %s errno %d."

ER_CONN_UNIX_LOCK_FILE_CANT_CLOSE
  eng "Could not close unix socket lock file %s errno %d."

ER_CONN_SOCKET_SELECT_FAILED
  eng "mysqld: Got error %d from select"

ER_CONN_SOCKET_ACCEPT_FAILED
  eng "Error in accept: %s"

ER_AUTH_RSA_CANT_FIND
  eng "RSA %s key file not found: %s. Some authentication plugins will not work."

ER_AUTH_RSA_CANT_PARSE
  eng "Failure to parse RSA %s key (file exists): %s: %s"

ER_AUTH_RSA_CANT_READ
  eng "Failure to read key file: %s"

ER_AUTH_RSA_FILES_NOT_FOUND
  eng "RSA key files not found. Some authentication plugins will not work."

ER_CONN_ATTR_TRUNCATED
  eng "Connection attributes of length %lu were truncated (%d bytes lost) for connection %llu, user %s@%s (as %s), auth: %s"

ER_X509_CIPHERS_MISMATCH
  eng "X.509 ciphers mismatch: should be '%s' but is '%s'"

ER_X509_ISSUER_MISMATCH
  eng "X.509 issuer mismatch: should be '%s' but is '%s'"

ER_X509_SUBJECT_MISMATCH
  eng "X.509 subject mismatch: should be '%s' but is '%s'"

ER_AUTH_CANT_ACTIVATE_ROLE
  eng "Failed to activate default role %s for %s"

ER_X509_NEEDS_RSA_PRIVKEY
  eng "Could not generate RSA private key required for X.509 certificate."

ER_X509_CANT_WRITE_KEY
  eng "Could not write key file: %s"

ER_X509_CANT_CHMOD_KEY
  eng "Could not set file permission for %s"

ER_X509_CANT_READ_CA_KEY
  eng "Could not read CA key file: %s"

ER_X509_CANT_READ_CA_CERT
  eng "Could not read CA certificate file: %s"

ER_X509_CANT_CREATE_CERT
  eng "Could not generate X.509 certificate."

ER_X509_CANT_WRITE_CERT
  eng "Could not write certificate file: %s"

ER_AUTH_CANT_CREATE_RSA_PAIR
  eng "Could not generate RSA Private/Public key pair"

ER_AUTH_CANT_WRITE_PRIVKEY
  eng "Could not write private key file: %s"

ER_AUTH_CANT_WRITE_PUBKEY
  eng "Could not write public key file: %s"

ER_AUTH_SSL_CONF_PREVENTS_CERT_GENERATION
  eng "Skipping generation of SSL certificates as options related to SSL are specified."

ER_AUTH_USING_EXISTING_CERTS
  eng "Skipping generation of SSL certificates as certificate files are present in data directory."

ER_AUTH_CERTS_SAVED_TO_DATADIR
  eng "Auto generated SSL certificates are placed in data directory."

ER_AUTH_CERT_GENERATION_DISABLED
  eng "Skipping generation of SSL certificates as --auto_generate_certs is set to OFF."

ER_AUTH_RSA_CONF_PREVENTS_KEY_GENERATION
  eng "Skipping generation of RSA key pair through %s as options related to RSA keys are specified."

ER_AUTH_KEY_GENERATION_SKIPPED_PAIR_PRESENT
  eng "Skipping generation of RSA key pair through %s as key files are present in data directory."

ER_AUTH_KEYS_SAVED_TO_DATADIR
  eng "Auto generated RSA key files through %s are placed in data directory."

ER_AUTH_KEY_GENERATION_DISABLED
  eng "Skipping generation of RSA key pair as %s is set to OFF."

ER_AUTHCACHE_PROXIES_PRIV_SKIPPED_NEEDS_RESOLVE
  eng "'proxies_priv' entry '%s@%s %s@%s' ignored in --skip-name-resolve mode."

ER_AUTHCACHE_PLUGIN_MISSING
  eng "The plugin '%.*s' used to authenticate user '%s'@'%.*s' is not loaded. Nobody can currently login using this account."

ER_AUTHCACHE_PLUGIN_CONFIG
  eng "The plugin '%s' is used to authenticate user '%s'@'%.*s', %s configured. Nobody can currently login using this account."

OBSOLETE_ER_AUTHCACHE_ROLE_TABLES_DODGY
  eng "Could not load mysql.role_edges and mysql.default_roles tables. ACL DDLs will not work unless the MySQL upgrade procedure is performed."

ER_AUTHCACHE_USER_SKIPPED_NEEDS_RESOLVE
  eng "'user' entry '%s@%s' ignored in --skip-name-resolve mode."

ER_AUTHCACHE_USER_TABLE_DODGY
  eng "Fatal error: Could not read the column 'authentication_string' from table 'mysql.user'. Please perform the MySQL upgrade procedure."

ER_AUTHCACHE_USER_IGNORED_DEPRECATED_PASSWORD
  eng "User entry '%s'@'%s' has a deprecated pre-4.1 password. The user will be ignored and no one can login with this user anymore."

ER_AUTHCACHE_USER_IGNORED_NEEDS_PLUGIN
  eng "User entry '%s'@'%s' has an empty plugin value. The user will be ignored and no one can login with this user anymore."

ER_AUTHCACHE_USER_IGNORED_INVALID_PASSWORD
  eng "Found invalid password for user: '%s@%s'; Ignoring user"

ER_AUTHCACHE_EXPIRED_PASSWORD_UNSUPPORTED
  eng "'user' entry '%s@%s' has the password ignore flag raised, but its authentication plugin doesn't support password expiration. The user id will be ignored."

ER_NO_SUPER_WITHOUT_USER_PLUGIN
  eng "Some of the user accounts with SUPER privileges were disabled because of empty mysql.user.plugin value. If you are upgrading from MySQL 5.6 to MySQL 5.7 it means that substitution for the empty plugin column was not possible. Probably because of pre 4.1 password hash. If your account is disabled you will need to perform the MySQL upgrade procedure. For complete instructions on how to upgrade MySQL to a new version please see the 'Upgrading MySQL' section from the MySQL manual."

ER_AUTHCACHE_DB_IGNORED_EMPTY_NAME
  eng "Found an entry in the 'db' table with empty database name; Skipped"

ER_AUTHCACHE_DB_SKIPPED_NEEDS_RESOLVE
  eng "'db' entry '%s %s@%s' ignored in --skip-name-resolve mode."

ER_AUTHCACHE_DB_ENTRY_LOWERCASED_REVOKE_WILL_FAIL
  eng "'db' entry '%s %s@%s' had database in mixed case that has been forced to lowercase because lower_case_table_names is set. It will not be possible to remove this privilege using REVOKE."

ER_AUTHCACHE_TABLE_PROXIES_PRIV_MISSING
  eng "The system table mysql.proxies_priv is missing. Please perform the MySQL upgrade procedure."

ER_AUTHCACHE_CANT_OPEN_AND_LOCK_PRIVILEGE_TABLES
  eng "Fatal error: Can't open and lock privilege tables: %s"

ER_AUTHCACHE_CANT_INIT_GRANT_SUBSYSTEM
  eng "Fatal: can't initialize grant subsystem - '%s'"

ER_AUTHCACHE_PROCS_PRIV_SKIPPED_NEEDS_RESOLVE
  eng "'procs_priv' entry '%s %s@%s' ignored in --skip-name-resolve mode."

ER_AUTHCACHE_PROCS_PRIV_ENTRY_IGNORED_BAD_ROUTINE_TYPE
  eng "'procs_priv' entry '%s' ignored, bad routine type"

ER_AUTHCACHE_TABLES_PRIV_SKIPPED_NEEDS_RESOLVE
  eng "'tables_priv' entry '%s %s@%s' ignored in --skip-name-resolve mode."

ER_USER_NOT_IN_EXTRA_USERS_BINLOG_POSSIBLY_INCOMPLETE
  eng "Failed to add %s in extra_users. Binary log entry may miss some of the users."

ER_DD_SCHEMA_NOT_FOUND
  eng "Unable to start server. The data dictionary schema '%s' does not exist."

ER_DD_TABLE_NOT_FOUND
  eng "Unable to start server. The data dictionary table '%s' does not exist."

ER_DD_SE_INIT_FAILED
  eng "Failed to initialize DD Storage Engine"

ER_DD_ABORTING_PARTIAL_UPGRADE
  eng "Found partially upgraded DD. Aborting upgrade and deleting all DD tables. Start the upgrade process again."

ER_DD_FRM_EXISTS_FOR_TABLE
  eng "Found .frm file with same name as one of the Dictionary Tables."

ER_DD_CREATED_FOR_UPGRADE
  eng "Created Data Dictionary for upgrade"

ER_ERRMSG_CANT_FIND_FILE
  eng "Can't find error-message file '%s'. Check error-message file location and 'lc-messages-dir' configuration directive."

ER_ERRMSG_LOADING_55_STYLE
  eng "Using pre 5.5 semantics to load error messages from %s. If this is not intended, refer to the documentation for valid usage of --lc-messages-dir and --language parameters."

ER_ERRMSG_MISSING_IN_FILE
  eng "Error message file '%s' had only %d error messages, but it should contain at least %d error messages. Check that the above file is the right version for this program!"

ER_ERRMSG_OOM
  eng "Not enough memory for messagefile '%s'"

ER_ERRMSG_CANT_READ
  eng "Can't read from messagefile '%s'"

ER_TABLE_INCOMPATIBLE_DECIMAL_FIELD
  eng "Found incompatible DECIMAL field '%s' in %s; Please do \"ALTER TABLE `%s` FORCE\" to fix it!"

ER_TABLE_INCOMPATIBLE_YEAR_FIELD
  eng "Found incompatible YEAR(x) field '%s' in %s; Please do \"ALTER TABLE `%s` FORCE\" to fix it!"

ER_INVALID_CHARSET_AND_DEFAULT_IS_MB
  eng "'%s' had no or invalid character set, and default character set is multi-byte, so character column sizes may have changed"

ER_TABLE_WRONG_KEY_DEFINITION
  eng "Found wrong key definition in %s; Please do \"ALTER TABLE `%s` FORCE \" to fix it!"

ER_CANT_OPEN_FRM_FILE
  eng "Unable to open file %s"

ER_CANT_READ_FRM_FILE
  eng "Error in reading file %s"

ER_TABLE_CREATED_WITH_DIFFERENT_VERSION
  eng "Table '%s' was created with a different version of MySQL and cannot be read"

ER_VIEW_UNPARSABLE
  eng "Unable to read view %s"

ER_FILE_TYPE_UNKNOWN
  eng "File %s has unknown type in its header."

ER_INVALID_INFO_IN_FRM
  eng "Incorrect information in file %s"

ER_CANT_OPEN_AND_LOCK_PRIVILEGE_TABLES
  eng "Can't open and lock privilege tables: %s"

ER_AUDIT_PLUGIN_DOES_NOT_SUPPORT_AUDIT_AUTH_EVENTS
  eng "Plugin '%s' cannot subscribe to MYSQL_AUDIT_AUTHORIZATION events. Currently not supported."

ER_AUDIT_PLUGIN_HAS_INVALID_DATA
  eng "Plugin '%s' has invalid data."

ER_TZ_OOM_INITIALIZING_TIME_ZONES
  eng "Fatal error: OOM while initializing time zones"

ER_TZ_CANT_OPEN_AND_LOCK_TIME_ZONE_TABLE
  eng "Can't open and lock time zone table: %s trying to live without them"

ER_TZ_OOM_LOADING_LEAP_SECOND_TABLE
  eng "Fatal error: Out of memory while loading mysql.time_zone_leap_second table"

ER_TZ_TOO_MANY_LEAPS_IN_LEAP_SECOND_TABLE
  eng "Fatal error: While loading mysql.time_zone_leap_second table: too much leaps"

ER_TZ_ERROR_LOADING_LEAP_SECOND_TABLE
  eng "Fatal error: Error while loading mysql.time_zone_leap_second table"

ER_TZ_UNKNOWN_OR_ILLEGAL_DEFAULT_TIME_ZONE
  eng "Fatal error: Illegal or unknown default time zone '%s'"

ER_TZ_CANT_FIND_DESCRIPTION_FOR_TIME_ZONE
  eng "Can't find description of time zone '%.*s'"

ER_TZ_CANT_FIND_DESCRIPTION_FOR_TIME_ZONE_ID
  eng "Can't find description of time zone '%u'"

ER_TZ_TRANSITION_TYPE_TABLE_TYPE_TOO_LARGE
  eng "Error while loading time zone description from mysql.time_zone_transition_type table: too big transition type id"

ER_TZ_TRANSITION_TYPE_TABLE_ABBREVIATIONS_EXCEED_SPACE
  eng "Error while loading time zone description from mysql.time_zone_transition_type table: not enough room for abbreviations"

ER_TZ_TRANSITION_TYPE_TABLE_LOAD_ERROR
  eng "Error while loading time zone description from mysql.time_zone_transition_type table"

ER_TZ_TRANSITION_TABLE_TOO_MANY_TRANSITIONS
  eng "Error while loading time zone description from mysql.time_zone_transition table: too much transitions"

ER_TZ_TRANSITION_TABLE_BAD_TRANSITION_TYPE
  eng "Error while loading time zone description from mysql.time_zone_transition table: bad transition type id"

ER_TZ_TRANSITION_TABLE_LOAD_ERROR
  eng "Error while loading time zone description from mysql.time_zone_transition table"

ER_TZ_NO_TRANSITION_TYPES_IN_TIME_ZONE
  eng "loading time zone without transition types"

ER_TZ_OOM_LOADING_TIME_ZONE_DESCRIPTION
  eng "Out of memory while loading time zone description"

ER_TZ_CANT_BUILD_MKTIME_MAP
  eng "Unable to build mktime map for time zone"

ER_TZ_OOM_WHILE_LOADING_TIME_ZONE
  eng "Out of memory while loading time zone"

ER_TZ_OOM_WHILE_SETTING_TIME_ZONE
  eng "Fatal error: Out of memory while setting new time zone"

ER_REPLICA_SQL_THREAD_STOPPED_UNTIL_CONDITION_BAD
  eng "Replica SQL thread is stopped because UNTIL condition is bad(%s:%llu)."

ER_REPLICA_SQL_THREAD_STOPPED_UNTIL_POSITION_REACHED
  eng "Replica SQL thread stopped because it reached its UNTIL position %llu"

ER_REPLICA_SQL_THREAD_STOPPED_BEFORE_GTIDS_ALREADY_APPLIED
  eng "Replica SQL thread stopped because UNTIL SQL_BEFORE_GTIDS %s is already applied"

ER_REPLICA_SQL_THREAD_STOPPED_BEFORE_GTIDS_REACHED
  eng "Replica SQL thread stopped because it reached UNTIL SQL_BEFORE_GTIDS %s"

ER_REPLICA_SQL_THREAD_STOPPED_AFTER_GTIDS_REACHED
  eng "Replica SQL thread stopped because it reached UNTIL SQL_AFTER_GTIDS %s"

ER_REPLICA_SQL_THREAD_STOPPED_GAP_TRX_PROCESSED
  eng "Replica SQL thread stopped according to UNTIL SQL_AFTER_MTS_GAPS as it has processed all gap transactions left from the previous replica session."

ER_GROUP_REPLICATION_PLUGIN_NOT_INSTALLED
  eng "Group Replication plugin is not installed."

ER_GTID_ALREADY_ADDED_BY_USER
  eng "The transaction owned GTID is already in the %s table, which is caused by an explicit modifying from user client."

ER_FAILED_TO_DELETE_FROM_GTID_EXECUTED_TABLE
  eng "Failed to delete the row: '%s' from the gtid_executed table."

ER_FAILED_TO_COMPRESS_GTID_EXECUTED_TABLE
  eng "Failed to compress the gtid_executed table."

ER_FAILED_TO_COMPRESS_GTID_EXECUTED_TABLE_OOM
  eng "Failed to compress the gtid_executed table, because it is failed to allocate the THD."

ER_FAILED_TO_INIT_THREAD_ATTR_FOR_GTID_TABLE_COMPRESSION
  eng "Failed to initialize thread attribute when creating compression thread."

ER_FAILED_TO_CREATE_GTID_TABLE_COMPRESSION_THREAD
  eng "Can not create thread to compress gtid_executed table (errno= %d)"

ER_FAILED_TO_JOIN_GTID_TABLE_COMPRESSION_THREAD
  eng "Could not join gtid_executed table compression thread. error:%d"

ER_NPIPE_FAILED_TO_INIT_SECURITY_DESCRIPTOR
  eng "Can't start server : Initialize security descriptor: %s"

ER_NPIPE_FAILED_TO_SET_SECURITY_DESCRIPTOR
  eng "Can't start server : Set security descriptor: %s"

ER_NPIPE_PIPE_ALREADY_IN_USE
  eng "Can't start server : Named Pipe \"%s\" already in use."

OBSOLETE_ER_NDB_SLAVE_SAW_EPOCH_LOWER_THAN_PREVIOUS_ON_START
  eng "NDB Replica : At SQL thread start applying epoch %llu/%llu (%llu) from Source ServerId %u which is lower than previously applied epoch %llu/%llu (%llu).  Group Source Log : %s  Group Source Log Pos : %llu.  Check replica positioning."

OBSOLETE_ER_NDB_SLAVE_SAW_EPOCH_LOWER_THAN_PREVIOUS
  eng "NDB Replica : SQL thread stopped as applying epoch %llu/%llu (%llu) from Source ServerId %u which is lower than previously applied epoch %llu/%llu (%llu).  Group Source Log : %s  Group Source Log Pos : %llu"

OBSOLETE_ER_NDB_SLAVE_SAW_ALREADY_COMMITTED_EPOCH
  eng "NDB Replica : SQL thread stopped as attempted to reapply already committed epoch %llu/%llu (%llu) from server id %u.  Group Source Log : %s  Group Source Log Pos : %llu."

OBSOLETE_ER_NDB_SLAVE_PREVIOUS_EPOCH_NOT_COMMITTED
  eng "NDB Replica : SQL thread stopped as attempting to apply new epoch %llu/%llu (%llu) while lower received epoch %llu/%llu (%llu) has not been committed.  Source server id : %u.  Group Source Log : %s  Group Source Log Pos : %llu."

OBSOLETE_ER_NDB_SLAVE_MISSING_DATA_FOR_TIMESTAMP_COLUMN
  eng "NDB Replica: missing data for %s timestamp column %u."

OBSOLETE_ER_NDB_SLAVE_LOGGING_EXCEPTIONS_TO
  eng "NDB Replica: Table %s.%s logging exceptions to %s.%s"

OBSOLETE_ER_NDB_SLAVE_LOW_EPOCH_RESOLUTION
  eng "NDB Replica: Table %s.%s : %s, low epoch resolution"

OBSOLETE_ER_NDB_INFO_FOUND_UNEXPECTED_FIELD_TYPE
  eng "Found unexpected field type %u"

OBSOLETE_ER_NDB_INFO_FAILED_TO_CREATE_NDBINFO
  eng "Failed to create NdbInfo"

OBSOLETE_ER_NDB_INFO_FAILED_TO_INIT_NDBINFO
  eng "Failed to init NdbInfo"

OBSOLETE_ER_NDB_CLUSTER_WRONG_NUMBER_OF_FUNCTION_ARGUMENTS
  eng "ndb_serialize_cond: Unexpected mismatch of found and expected number of function arguments %u"

OBSOLETE_ER_NDB_CLUSTER_SCHEMA_INFO
  eng "%s - %s.%s"

OBSOLETE_ER_NDB_CLUSTER_GENERIC_MESSAGE
  eng "%s"

ER_RPL_CANT_OPEN_INFO_TABLE
  eng "Info table is not ready to be used. Table '%s.%s' cannot be opened."

ER_RPL_CANT_SCAN_INFO_TABLE
  eng "Info table is not ready to be used. Table '%s.%s' cannot be scanned."

ER_RPL_CORRUPTED_INFO_TABLE
  eng "Corrupted table %s.%s. Check out table definition."

ER_RPL_CORRUPTED_KEYS_IN_INFO_TABLE
  eng "Info table has a problem with its key field(s). Table '%s.%s' expected field #%u to be '%s' but found '%s' instead."

ER_RPL_WORKER_ID_IS
  eng "Choosing worker id %lu, the following is going to be %lu"

ER_RPL_INCONSISTENT_TIMESTAMPS_IN_TRX
  eng "Transaction is tagged with inconsistent logical timestamps: sequence_number (%lld) <= last_committed (%lld)"

ER_RPL_INCONSISTENT_SEQUENCE_NO_IN_TRX
  eng "Transaction's sequence number is inconsistent with that of a preceding one: sequence_number (%lld) <= previous sequence_number (%lld)"

ER_RPL_CHANNELS_REQUIRE_TABLES_AS_INFO_REPOSITORIES
  eng "For the creation of replication channels the connection metadata and applier metadata repositories must be set to TABLE"

ER_RPL_CHANNELS_REQUIRE_NON_ZERO_SERVER_ID
  eng "For the creation of replication channels the server id must be different from 0"

ER_RPL_REPO_SHOULD_BE_TABLE
  eng "Replica: Wrong repository. Repository should be TABLE"

ER_RPL_ERROR_CREATING_CONNECTION_METADATA
  eng "Error creating connection metadata: %s."

ER_RPL_ERROR_CHANGING_CONNECTION_METADATA_REPO_TYPE
  eng "Error changing the type of connection metadata's repository: %s."

ER_RPL_CHANGING_APPLIER_METADATA_REPO_TYPE_FAILED_DUE_TO_GAPS
  eng "It is not possible to change the type of the relay log repository because there are workers repositories with possible execution gaps. The value of --relay_log_info_repository is altered to one of the found Worker repositories. The gaps have to be sorted out before resuming with the type change."

ER_RPL_ERROR_CREATING_APPLIER_METADATA
  eng "Error creating applier metadata: %s."

ER_RPL_ERROR_CHANGING_APPLIER_METADATA_REPO_TYPE
  eng "Error changing the type of applier metadata's repository: %s."

ER_RPL_FAILED_TO_DELETE_FROM_REPLICA_WORKERS_INFO_REPOSITORY
  eng "Could not delete from Replica Workers info repository."

ER_RPL_FAILED_TO_RESET_STATE_IN_REPLICA_INFO_REPOSITORY
  eng "Could not store the reset Replica Worker state into the replica info repository."

ER_RPL_ERROR_CHECKING_REPOSITORY
  eng "Error in checking %s repository info type of %s."

ER_RPL_REPLICA_GENERIC_MESSAGE
  eng "Replica: %s"

ER_RPL_REPLICA_COULD_NOT_CREATE_CHANNEL_LIST
  eng "Replica: Could not create channel list"

ER_RPL_MULTISOURCE_REQUIRES_TABLE_TYPE_REPOSITORIES
  eng "Replica: This replica was a multisourced replica previously which is supported only by both TABLE based connection metadata and applier metadata repositories. Found one or both of the info repos to be type FILE. Set both repos to type TABLE."

ER_RPL_REPLICA_FAILED_TO_INIT_A_CONNECTION_METADATA_STRUCTURE
  eng "Replica: Failed to initialize the connection metadata structure for channel '%s'; its record may still be present in the applier metadata repository, consider deleting it."

ER_RPL_REPLICA_FAILED_TO_INIT_CONNECTION_METADATA_STRUCTURE
  eng "Failed to initialize the connection metadata structure%s"

ER_RPL_REPLICA_FAILED_TO_CREATE_CHANNEL_FROM_CONNECTION_METADATA
  eng "Replica: Failed to create a channel from connection metadata repository."

ER_RPL_FAILED_TO_CREATE_NEW_INFO_FILE
  eng "Failed to create a new info file (file '%s', errno %d)"

ER_RPL_FAILED_TO_CREATE_CACHE_FOR_INFO_FILE
  eng "Failed to create a cache on info file (file '%s')"

ER_RPL_FAILED_TO_OPEN_INFO_FILE
  eng "Failed to open the existing info file (file '%s', errno %d)"

ER_RPL_GTID_MEMORY_FINALLY_AVAILABLE
  eng "Server overcomes the temporary 'out of memory' in '%d' tries while allocating a new chunk of intervals for storing GTIDs."

ER_SERVER_COST_UNKNOWN_COST_CONSTANT
  eng "Unknown cost constant \"%s\" in mysql.server_cost table"

ER_SERVER_COST_INVALID_COST_CONSTANT
  eng "Invalid value for cost constant \"%s\" in mysql.server_cost table: %.1f"

ER_ENGINE_COST_UNKNOWN_COST_CONSTANT
  eng "Unknown cost constant \"%s\" in mysql.engine_cost table"

ER_ENGINE_COST_UNKNOWN_STORAGE_ENGINE
  eng "Unknown storage engine \"%s\" in mysql.engine_cost table"

ER_ENGINE_COST_INVALID_DEVICE_TYPE_FOR_SE
  eng "Invalid device type %d for \"%s\" storage engine for cost constant \"%s\" in mysql.engine_cost table"

ER_ENGINE_COST_INVALID_CONST_CONSTANT_FOR_SE_AND_DEVICE
  eng "Invalid value for cost constant \"%s\" for \"%s\" storage engine and device type %d in mysql.engine_cost table: %.1f"

ER_SERVER_COST_FAILED_TO_READ
  eng "Error while reading from mysql.server_cost table."

ER_ENGINE_COST_FAILED_TO_READ
  eng "Error while reading from mysql.engine_cost table."

ER_FAILED_TO_OPEN_COST_CONSTANT_TABLES
  eng "Failed to open optimizer cost constant tables"

ER_RPL_UNSUPPORTED_UNIGNORABLE_EVENT_IN_STREAM
  eng "Unsupported non-ignorable event fed into the event stream."

ER_RPL_GTID_LOG_EVENT_IN_STREAM
  eng "GTID_LOG_EVENT or ANONYMOUS_GTID_LOG_EVENT is not expected in an event stream %s."

ER_RPL_UNEXPECTED_BEGIN_IN_STREAM
  eng "QUERY(BEGIN) is not expected in an event stream in the middle of a %s."

ER_RPL_UNEXPECTED_COMMIT_ROLLBACK_OR_XID_LOG_EVENT_IN_STREAM
  eng "QUERY(COMMIT or ROLLBACK) or XID_LOG_EVENT is not expected in an event stream %s."

ER_RPL_UNEXPECTED_XA_ROLLBACK_IN_STREAM
  eng "QUERY(XA ROLLBACK) is not expected in an event stream %s."

ER_EVENT_EXECUTION_FAILED_CANT_AUTHENTICATE_USER
  eng "Event Scheduler: [%s].[%s.%s] execution failed, failed to authenticate the user."

ER_EVENT_EXECUTION_FAILED_USER_LOST_EVEN_PRIVILEGE
  eng "Event Scheduler: [%s].[%s.%s] execution failed, user no longer has EVENT privilege."

ER_EVENT_ERROR_DURING_COMPILATION
  eng "Event Scheduler: %serror during compilation of %s.%s"

ER_EVENT_DROPPING
  eng "Event Scheduler: Dropping %s.%s"

OBSOLETE_ER_NDB_SCHEMA_GENERIC_MESSAGE
  eng "Ndb schema[%s.%s]: %s"

ER_RPL_INCOMPATIBLE_DECIMAL_IN_RBR
  eng "In RBR mode, Replica received incompatible DECIMAL field (old-style decimal field) from Source while creating conversion table. Please consider changing datatype on Source to new style decimal by executing ALTER command for column Name: %s.%s.%s."

ER_INIT_ROOT_WITHOUT_PASSWORD
  eng "root@localhost is created with an empty password ! Please consider switching off the --initialize-insecure option."

ER_INIT_GENERATING_TEMP_PASSWORD_FOR_ROOT
  eng "A temporary password is generated for root@localhost: %s"

ER_INIT_CANT_OPEN_BOOTSTRAP_FILE
  eng "Failed to open the bootstrap file %s"

ER_INIT_BOOTSTRAP_COMPLETE
  eng "Bootstrapping complete"

ER_INIT_DATADIR_NOT_EMPTY_WONT_INITIALIZE
  eng "--initialize specified but the data directory has files in it. Aborting."

ER_INIT_DATADIR_EXISTS_WONT_INITIALIZE
  eng "--initialize specified on an existing data directory."

ER_INIT_DATADIR_EXISTS_AND_PATH_TOO_LONG_WONT_INITIALIZE
  eng "--initialize specified but the data directory exists and the path is too long. Aborting."

ER_INIT_DATADIR_EXISTS_AND_NOT_WRITABLE_WONT_INITIALIZE
  eng "--initialize specified but the data directory exists and is not writable. Aborting."

ER_INIT_CREATING_DD
  eng "Creating the data directory %s"

ER_RPL_BINLOG_STARTING_DUMP
  eng "Start binlog_dump to source_thread_id(%u) replica_server(%u), pos(%s, %llu)"

ER_RPL_BINLOG_SOURCE_SENDS_HEARTBEAT
  eng "source sends heartbeat message"

ER_RPL_BINLOG_SKIPPING_REMAINING_HEARTBEAT_INFO
  eng "the rest of heartbeat info skipped ..."

ER_RPL_BINLOG_SOURCE_USES_CHECKSUM_AND_REPLICA_CANT
  eng "Source is configured to log replication events with checksum, but will not send such events to replicas that cannot process them"

OBSOLETE_ER_NDB_QUERY_FAILED
  eng "NDB: Query '%s' failed, error: %d: %s"

ER_KILLING_THREAD
  eng "Killing thread %lu"

ER_DETACHING_SESSION_LEFT_BY_PLUGIN
  eng "Plugin %s is deinitializing a thread but left a session attached. Detaching it forcefully."

ER_CANT_DETACH_SESSION_LEFT_BY_PLUGIN
  eng "Failed to detach the session."

ER_DETACHED_SESSIONS_LEFT_BY_PLUGIN
  eng "Closed forcefully %u session%s left opened by plugin %s"

ER_FAILED_TO_DECREMENT_NUMBER_OF_THREADS
  eng "Failed to decrement the number of threads"

ER_PLUGIN_DID_NOT_DEINITIALIZE_THREADS
  eng "Plugin %s did not deinitialize %u threads"

ER_KILLED_THREADS_OF_PLUGIN
  eng "Killed %u threads of plugin %s"

OBSOLETE_ER_NDB_SLAVE_MAX_REPLICATED_EPOCH_UNKNOWN
  eng "NDB Replica : Could not determine maximum replicated epoch from %s.%s at Replica start, error %u %s"

OBSOLETE_ER_NDB_SLAVE_MAX_REPLICATED_EPOCH_SET_TO
  eng "NDB Replica : MaxReplicatedEpoch set to %llu (%u/%u) at Replica start"

OBSOLETE_ER_NDB_NODE_ID_AND_MANAGEMENT_SERVER_INFO
  eng "NDB: NodeID is %lu, management server '%s:%lu'"

OBSOLETE_ER_NDB_DISCONNECT_INFO
  eng "tid %u: node[%u] transaction_hint=%u, transaction_no_hint=%u"

OBSOLETE_ER_NDB_COLUMN_DEFAULTS_DIFFER
  eng "NDB Internal error: Default values differ for column %u, ndb_default: %d"

OBSOLETE_ER_NDB_COLUMN_SHOULD_NOT_HAVE_NATIVE_DEFAULT
  eng "NDB Internal error: Column %u has native default, but shouldn't. Flags=%u, type=%u"

OBSOLETE_ER_NDB_FIELD_INFO
  eng "field[ name: '%s', type: %u, real_type: %u, flags: 0x%x, is_null: %d]"

OBSOLETE_ER_NDB_COLUMN_INFO
  eng "ndbCol[name: '%s', type: %u, column_no: %d, nullable: %d]"

OBSOLETE_ER_NDB_OOM_IN_FIX_UNIQUE_INDEX_ATTR_ORDER
  eng "fix_unique_index_attr_order: my_malloc(%u) failure"

OBSOLETE_ER_NDB_SLAVE_MALFORMED_EVENT_RECEIVED_ON_TABLE
  eng "NDB Replica : Malformed event received on table %s cannot parse.  Stopping Replica."

OBSOLETE_ER_NDB_SLAVE_CONFLICT_FUNCTION_REQUIRES_ROLE
  eng "NDB Replica : Conflict function %s defined on table %s requires ndb_replica_conflict_role variable to be set.  Stopping replica."

OBSOLETE_ER_NDB_SLAVE_CONFLICT_TRANSACTION_IDS
  eng "NDB Replica : Transactional conflict detection defined on table %s, but events received without transaction ids.  Check --ndb-log-transaction-id setting on upstream Cluster."

OBSOLETE_ER_NDB_SLAVE_BINLOG_MISSING_INFO_FOR_CONFLICT_DETECTION
  eng "NDB Replica : Binlog event on table %s missing info necessary for conflict detection.  Check binlog format options on upstream cluster."

OBSOLETE_ER_NDB_ERROR_IN_READAUTOINCREMENTVALUE
  eng "Error %lu in readAutoIncrementValue(): %s"

OBSOLETE_ER_NDB_FOUND_UNCOMMITTED_AUTOCOMMIT
  eng "found uncommitted autocommit+rbwr transaction, commit status: %d"

OBSOLETE_ER_NDB_SLAVE_TOO_MANY_RETRIES
  eng "Ndb replica retried transaction %u time(s) in vain.  Giving up."

OBSOLETE_ER_NDB_SLAVE_ERROR_IN_UPDATE_CREATE_INFO
  eng "Error %lu in ::update_create_info(): %s"

OBSOLETE_ER_NDB_SLAVE_CANT_ALLOCATE_TABLE_SHARE
  eng "NDB: allocating table share for %s failed"

OBSOLETE_ER_NDB_BINLOG_ERROR_INFO_FROM_DA
  eng "NDB Binlog: (%d)%s"

OBSOLETE_ER_NDB_BINLOG_CREATE_TABLE_EVENT
  eng "NDB Binlog: CREATE TABLE Event: %s"

OBSOLETE_ER_NDB_BINLOG_FAILED_CREATE_TABLE_EVENT_OPERATIONS
  eng "NDB Binlog: FAILED CREATE TABLE event operations. Event: %s"

OBSOLETE_ER_NDB_BINLOG_RENAME_EVENT
  eng "NDB Binlog: RENAME Event: %s"

OBSOLETE_ER_NDB_BINLOG_FAILED_CREATE_DURING_RENAME
  eng "NDB Binlog: FAILED create event operations during RENAME. Event %s"

OBSOLETE_ER_NDB_UNEXPECTED_RENAME_TYPE
  eng "Unexpected rename case detected, sql_command: %d"

OBSOLETE_ER_NDB_ERROR_IN_GET_AUTO_INCREMENT
  eng "Error %lu in ::get_auto_increment(): %s"

OBSOLETE_ER_NDB_CREATING_SHARE_IN_OPEN
  eng "Calling ndbcluster_create_binlog_setup(%s) in ::open"

OBSOLETE_ER_NDB_TABLE_OPENED_READ_ONLY
  eng "table '%s' opened read only"

OBSOLETE_ER_NDB_INITIALIZE_GIVEN_CLUSTER_PLUGIN_DISABLED
  eng "NDB: '--initialize' -> ndbcluster plugin disabled"

OBSOLETE_ER_NDB_BINLOG_FORMAT_CHANGED_FROM_STMT_TO_MIXED
  eng "NDB: Changed global value of binlog_format from STATEMENT to MIXED"

OBSOLETE_ER_NDB_TRAILING_SHARE_RELEASED_BY_CLOSE_CACHED_TABLES
  eng "NDB_SHARE: trailing share %s, released by close_cached_tables"

OBSOLETE_ER_NDB_SHARE_ALREADY_EXISTS
  eng "NDB_SHARE: %s already exists use_count=%d. Moving away for safety, but possible memleak."

OBSOLETE_ER_NDB_HANDLE_TRAILING_SHARE_INFO
  eng "handle_trailing_share: %s use_count: %u"

OBSOLETE_ER_NDB_CLUSTER_GET_SHARE_INFO
  eng "ndbcluster_get_share: %s use_count: %u"

OBSOLETE_ER_NDB_CLUSTER_REAL_FREE_SHARE_INFO
  eng "ndbcluster_real_free_share: %s use_count: %u"

OBSOLETE_ER_NDB_CLUSTER_REAL_FREE_SHARE_DROP_FAILED
  eng "ndbcluster_real_free_share: %s, still open - ignored 'free' (leaked?)"

OBSOLETE_ER_NDB_CLUSTER_FREE_SHARE_INFO
  eng "ndbcluster_free_share: %s use_count: %u"

OBSOLETE_ER_NDB_CLUSTER_MARK_SHARE_DROPPED_INFO
  eng "ndbcluster_mark_share_dropped: %s use_count: %u"

OBSOLETE_ER_NDB_CLUSTER_MARK_SHARE_DROPPED_DESTROYING_SHARE
  eng "ndbcluster_mark_share_dropped: destroys share %s"

OBSOLETE_ER_NDB_CLUSTER_OOM_THD_NDB
  eng "Could not allocate Thd_ndb object"

OBSOLETE_ER_NDB_BINLOG_NDB_TABLES_INITIALLY_READ_ONLY
  eng "NDB Binlog: Ndb tables initially read only."

OBSOLETE_ER_NDB_UTIL_THREAD_OOM
  eng "ndb util thread: malloc failure, query cache not maintained properly"

OBSOLETE_ER_NDB_ILLEGAL_VALUE_FOR_NDB_RECV_THREAD_CPU_MASK
  eng "Trying to set ndb_recv_thread_cpu_mask to illegal value = %s, ignored"

OBSOLETE_ER_NDB_TOO_MANY_CPUS_IN_NDB_RECV_THREAD_CPU_MASK
  eng "Trying to set too many CPU's in ndb_recv_thread_cpu_mask, ignored this variable, erroneus value = %s"

ER_DBUG_CHECK_SHARES_OPEN
  eng "dbug_check_shares open:"

ER_DBUG_CHECK_SHARES_INFO
  eng "  %s.%s: state: %s(%u) use_count: %u"

ER_DBUG_CHECK_SHARES_DROPPED
  eng "dbug_check_shares dropped:"

ER_INVALID_OR_OLD_TABLE_OR_DB_NAME
  eng "Invalid (old?) table or database name '%s'"

ER_TC_RECOVERING_AFTER_CRASH_USING
  eng "Recovering after a crash using %s"

ER_TC_CANT_AUTO_RECOVER_WITH_TC_HEURISTIC_RECOVER
  eng "Cannot perform automatic crash recovery when --tc-heuristic-recover is used"

ER_TC_BAD_MAGIC_IN_TC_LOG
  eng "Bad magic header in tc log"

ER_TC_NEED_N_SE_SUPPORTING_2PC_FOR_RECOVERY
  eng "Recovery failed! You must enable exactly %d storage engines that support two-phase commit protocol"

ER_TC_RECOVERY_FAILED_THESE_ARE_YOUR_OPTIONS
  eng "Crash recovery failed. Either correct the problem (if it's, for example, out of memory error) and restart, or delete tc log and start mysqld with --tc-heuristic-recover={commit|rollback}"

ER_TC_HEURISTIC_RECOVERY_MODE
  eng "Heuristic crash recovery mode"

ER_TC_HEURISTIC_RECOVERY_FAILED
  eng "Heuristic crash recovery failed"

ER_TC_RESTART_WITHOUT_TC_HEURISTIC_RECOVER
  eng "Please restart mysqld without --tc-heuristic-recover"

ER_RPL_REPLICA_FAILED_TO_CREATE_OR_RECOVER_INFO_REPOSITORIES
  eng "Failed to create or recover replication info repositories."

ER_RPL_REPLICA_AUTO_POSITION_IS_1_AND_GTID_MODE_IS_OFF
  eng "Detected misconfiguration: replication channel '%.192s' was configured with AUTO_POSITION = 1, but the server was started with --gtid-mode=off. Either reconfigure replication using CHANGE REPLICATION SOURCE TO SOURCE_AUTO_POSITION = 0 FOR CHANNEL '%.192s', or change GTID_MODE to some value other than OFF, before starting the replica receiver thread."

ER_RPL_REPLICA_CANT_START_REPLICA_FOR_CHANNEL
  eng "Replica: Could not start replica for channel '%s'. operation discontinued"

ER_RPL_REPLICA_CANT_STOP_REPLICA_FOR_CHANNEL
  eng "Replica: Could not stop replica for channel '%s' operation discontinued"

ER_RPL_RECOVERY_NO_ROTATE_EVENT_FROM_SOURCE
  eng "Error during --relay-log-recovery: Could not locate rotate event from the source."

ER_RPL_RECOVERY_ERROR_READ_RELAY_LOG
  eng "Error during --relay-log-recovery: Error reading events from relay log: %d"

OBSOLETE_ER_RPL_RECOVERY_ERROR_FREEING_IO_CACHE
  eng "Error during --relay-log-recovery: Error while freeing IO_CACHE object"

ER_RPL_RECOVERY_SKIPPED_GROUP_REPLICATION_CHANNEL
  eng "Relay log recovery skipped for group replication channel."

ER_RPL_RECOVERY_ERROR
  eng "Error during --relay-log-recovery: %s"

ER_RPL_RECOVERY_IO_ERROR_READING_RELAY_LOG_INDEX
  eng "Error during --relay-log-recovery: Could not read relay log index file due to an IO error."

ER_RPL_RECOVERY_FILE_SOURCE_POS_INFO
  eng "Recovery from source pos %ld and file %s%s. Previous relay log pos and relay log file had been set to %lld, %s respectively."

ER_RPL_RECOVERY_REPLICATE_SAME_SERVER_ID_REQUIRES_POSITION
  eng "Error during --relay-log-recovery: replicate_same_server_id is in use and sql thread's positions are not initialized, hence relay log recovery cannot happen."

ER_RPL_MTA_RECOVERY_STARTING_COORDINATOR
  eng "MTA recovery: starting coordinator thread to fill MTA gaps."

ER_RPL_MTA_RECOVERY_FAILED_TO_START_COORDINATOR
  eng "MTA recovery: failed to start the coordinator thread. Check the error log for additional details."

ER_RPL_MTA_AUTOMATIC_RECOVERY_FAILED
  eng "MTA recovery: automatic recovery failed. Either the replica server had stopped due to an error during an earlier session or relay logs are corrupted.Fix the cause of the replica side error and restart the replica server or consider using RESET REPLICA."

ER_RPL_MTA_RECOVERY_CANT_OPEN_RELAY_LOG
  eng "Failed to open the relay log '%s' (relay_log_pos %s)."

ER_RPL_MTA_RECOVERY_SUCCESSFUL
  eng "MTA recovery: completed successfully."

ER_RPL_SERVER_ID_MISSING
  eng "Server id not set, will not start replica%s"

ER_RPL_CANT_CREATE_REPLICA_THREAD
  eng "Can't create replica thread%s."

ER_RPL_REPLICA_IO_THREAD_WAS_KILLED
  eng "The replica IO thread%s was killed while executing initialization query '%s'"

OBSOLETE_ER_RPL_REPLICA_SOURCE_UUID_HAS_CHANGED
  eng "The source's UUID has changed, although this should not happen unless you have changed it manually. The old UUID was %s."

ER_RPL_REPLICA_USES_CHECKSUM_AND_SOURCE_PRE_50
  eng "Found a source with MySQL server version older than 5.0. With checksums enabled on the replica, replication might not work correctly. To ensure correct replication, restart the replica server with --replica_sql_verify_checksum=0."

ER_RPL_REPLICA_SECONDS_BEHIND_SOURCE_DUBIOUS
  eng "\"SELECT UNIX_TIMESTAMP()\" failed on source, do not trust column Seconds_Behind_Source of SHOW REPLICA STATUS. Error: %s (%d)"

ER_RPL_REPLICA_CANT_FLUSH_CONNECTION_METADATA_REPOS
  eng "Failed to flush connection metadata repository."

ER_RPL_REPLICA_REPORT_HOST_TOO_LONG
  eng "The length of report_host is %zu. It is larger than the max length(%d), so this replica cannot be registered to the source%s."

ER_RPL_REPLICA_REPORT_USER_TOO_LONG
  eng "The length of report_user is %zu. It is larger than the max length(%d), so this replica cannot be registered to the source%s."

ER_RPL_REPLICA_REPORT_PASSWORD_TOO_LONG
  eng "The length of report_password is %zu. It is larger than the max length(%d), so this replica cannot be registered to the source%s."

ER_RPL_REPLICA_ERROR_RETRYING
  eng "Error on %s: %d  %s, will retry in %d secs"

ER_RPL_REPLICA_ERROR_READING_FROM_SERVER
  eng "Error reading packet from server%s: %s (server_errno=%d)"

ER_RPL_REPLICA_DUMP_THREAD_KILLED_BY_SOURCE
  eng "Replica%s: received end packet from server due to dump thread being killed on source. Dump threads are killed for example during source shutdown, explicitly by a user, or when the source receives a binlog send request from a duplicate server UUID <%s> : Error %s"

ER_RPL_MTA_STATISTICS
  eng "Multi-threaded replica statistics%s: seconds elapsed = %lu; events assigned = %llu; worker queues filled over overrun level = %lu; waited due a Worker queue full = %lu; waited due the total size = %lu; waited at clock conflicts = %llu waited (count) when Workers occupied = %lu waited when Workers occupied = %llu"

ER_RPL_MTA_RECOVERY_COMPLETE
  eng "Replica%s: MTA Recovery has completed at relay log %s, position %llu source log %s, position %llu."

ER_RPL_REPLICA_CANT_INIT_RELAY_LOG_POSITION
  eng "Error initializing relay log position%s: %s"

OBSOLETE_ER_RPL_REPLICA_CONNECTED_TO_SOURCE_REPLICATION_STARTED
  eng "Replica I/O thread%s: connected to source '%s@%s:%d',replication started in log '%s' at position %s"

ER_RPL_REPLICA_IO_THREAD_KILLED
  eng "Replica I/O thread%s killed while connecting to source"

ER_RPL_REPLICA_IO_THREAD_CANT_REGISTER_ON_SOURCE
  eng "Replica I/O thread couldn't register on source"

ER_RPL_REPLICA_FORCING_TO_RECONNECT_IO_THREAD
  eng "Forcing to reconnect replica I/O thread%s"

ER_RPL_REPLICA_ERROR_REQUESTING_BINLOG_DUMP
  eng "Failed on request_dump()%s"

ER_RPL_LOG_ENTRY_EXCEEDS_REPLICA_MAX_ALLOWED_PACKET
  eng "Log entry on source is longer than replica_max_allowed_packet (%lu) on replica. If the entry is correct, restart the server with a higher value of replica_max_allowed_packet"

ER_RPL_REPLICA_STOPPING_AS_SOURCE_OOM
  eng "Stopping replica I/O thread due to out-of-memory error from source"

ER_RPL_REPLICA_IO_THREAD_ABORTED_WAITING_FOR_RELAY_LOG_SPACE
  eng "Replica I/O thread aborted while waiting for relay log space"

ER_RPL_REPLICA_IO_THREAD_EXITING
  eng "Replica I/O thread exiting%s, read up to log '%s', position %s"

ER_RPL_REPLICA_CANT_INITIALIZE_REPLICA_WORKER
  eng "Failed during replica worker initialization%s"

ER_RPL_MTA_GROUP_RECOVERY_APPLIER_METADATA_FOR_WORKER
  eng "Replica: MTA group recovery applier metadata based on Worker-Id %lu, group_relay_log_name %s, group_relay_log_pos %llu group_source_log_name %s, group_source_log_pos %llu"

ER_RPL_ERROR_LOOKING_FOR_LOG
  eng "Error looking for %s."

ER_RPL_MTA_GROUP_RECOVERY_APPLIER_METADATA
  eng "Replica: MTA group recovery applier metadata group_source_log_name %s, event_source_log_pos %llu."

ER_RPL_CANT_FIND_FOLLOWUP_FILE
  eng "Error looking for file after %s."

ER_RPL_MTA_CHECKPOINT_PERIOD_DIFFERS_FROM_CNT
  eng "This an error cnt != mta_checkpoint_period"

ER_RPL_REPLICA_WORKER_THREAD_CREATION_FAILED
  eng "Failed during replica worker thread creation%s"

ER_RPL_REPLICA_WORKER_THREAD_CREATION_FAILED_WITH_ERRNO
  eng "Failed during replica worker thread creation%s (errno= %d)"

ER_RPL_REPLICA_FAILED_TO_INIT_PARTITIONS_HASH
  eng "Failed to init partitions hash"

OBSOLETE_ER_RPL_SLAVE_NDB_TABLES_NOT_AVAILABLE
  eng "Replica SQL thread : NDB : Tables not available after %lu seconds. Consider increasing --ndb-wait-setup value"

ER_RPL_REPLICA_SQL_THREAD_STARTING
  eng "Replica SQL thread%s initialized, starting replication in log '%s' at position %s, relay log '%s' position: %s"

ER_RPL_REPLICA_SKIP_COUNTER_EXECUTED
  eng "'SQL_REPLICA_SKIP_COUNTER=%ld' executed at relay_log_file='%s', relay_log_pos='%ld', source_log_name='%s', source_log_pos='%ld' and new position at relay_log_file='%s', relay_log_pos='%ld', source_log_name='%s', source_log_pos='%ld' "

ER_RPL_REPLICA_ADDITIONAL_ERROR_INFO_FROM_DA
  eng "Replica (additional info): %s Error_code: MY-%06d"

ER_RPL_REPLICA_ERROR_INFO_FROM_DA
  eng "Replica: %s Error_code: MY-%06d"

ER_RPL_REPLICA_ERROR_LOADING_USER_DEFINED_LIBRARY
  eng "Error loading user-defined library, replica SQL thread aborted. Install the missing library, and restart the replica SQL thread with \"START REPLICA\". We stopped at log '%s' position %s"

ER_RPL_REPLICA_ERROR_RUNNING_QUERY
  eng "Error running query, replica SQL thread aborted. Fix the problem, and restart the replica SQL thread with \"START REPLICA\". We stopped at log '%s' position %s"

ER_RPL_REPLICA_SQL_THREAD_EXITING
  eng "Replica SQL thread%s exiting, replication stopped in log '%s' at position %s"

ER_RPL_REPLICA_READ_INVALID_EVENT_FROM_SOURCE
  eng "Read invalid event from source: '%s', source could be corrupt but a more likely cause of this is a bug"

ER_RPL_REPLICA_QUEUE_EVENT_FAILED_INVALID_CONFIGURATION
  eng "The queue event failed for channel '%s' as its configuration is invalid."

ER_RPL_REPLICA_IO_THREAD_DETECTED_UNEXPECTED_EVENT_SEQUENCE
  eng "An unexpected event sequence was detected by the IO thread while queuing the event received from source '%s' binary log file, at position %llu."

ER_RPL_REPLICA_CANT_USE_CHARSET
  eng "'%s' can not be used as client character set. '%s' will be used as default client character set while connecting to source."

ER_RPL_REPLICA_CONNECTED_TO_SOURCE_REPLICATION_RESUMED
  eng "Replica%s: connected to source '%s@%s:%d',replication resumed in log '%s' at position %s"

ER_RPL_REPLICA_NEXT_LOG_IS_ACTIVE
  eng "next log '%s' is active"

ER_RPL_REPLICA_NEXT_LOG_IS_INACTIVE
  eng "next log '%s' is not active"

ER_RPL_REPLICA_SQL_THREAD_IO_ERROR_READING_EVENT
  eng "Replica SQL thread%s: I/O error reading event (errno: %d  cur_log->error: %d)"

ER_RPL_REPLICA_ERROR_READING_RELAY_LOG_EVENTS
  eng "Error reading relay log event%s: %s"

ER_REPLICA_CHANGE_SOURCE_TO_EXECUTED
  eng "'CHANGE REPLICATION SOURCE TO%s executed'. Previous state source_host='%s', source_port= %u, source_log_file='%s', source_log_pos= %ld, source_bind='%s'. New state source_host='%s', source_port= %u, source_log_file='%s', source_log_pos= %ld, source_bind='%s'."

ER_RPL_REPLICA_NEW_C_M_NEEDS_REPOS_TYPE_OTHER_THAN_FILE
  eng "Replica: Cannot create new connection metadata structure when repositories are of type FILE. Convert replica repositories to TABLE to replicate from multiple sources."

ER_RPL_FAILED_TO_STAT_LOG_IN_INDEX
  eng "log %s listed in the index, but failed to stat."

ER_RPL_LOG_NOT_FOUND_WHILE_COUNTING_RELAY_LOG_SPACE
  eng "Could not find first log while counting relay log space."

ER_REPLICA_CANT_USE_TEMPDIR
  eng "Unable to use replica's temporary directory '%s'."

ER_RPL_RELAY_LOG_NEEDS_FILE_NOT_DIRECTORY
  eng "Path '%s' is a directory name, please specify a file name for --relay-log option."

ER_RPL_RELAY_LOG_INDEX_NEEDS_FILE_NOT_DIRECTORY
  eng "Path '%s' is a directory name, please specify a file name for --relay-log-index option."

ER_RPL_PLEASE_USE_OPTION_RELAY_LOG
  eng "Neither --relay-log nor --relay-log-index were used; so replication may break when this MySQL server acts as a replica and has his hostname changed!! Please use '--relay-log=%s' to avoid this problem."

ER_RPL_OPEN_INDEX_FILE_FAILED
  eng "Failed in open_index_file() called from Relay_log_info::rli_init_info()."

ER_RPL_CANT_INITIALIZE_GTID_SETS_IN_AM_INIT_INFO
  eng "Failed in init_gtid_sets() called from Relay_log_info::rli_init_info()."

ER_RPL_CANT_OPEN_LOG_IN_AM_INIT_INFO
  eng "Failed in open_log() called from Relay_log_info::rli_init_info()."

ER_RPL_ERROR_WRITING_RELAY_LOG_CONFIGURATION
  eng "Error writing relay log configuration."

OBSOLETE_ER_NDB_OOM_GET_NDB_BLOBS_VALUE
  eng "get_ndb_blobs_value: my_malloc(%u) failed"

OBSOLETE_ER_NDB_THREAD_TIMED_OUT
  eng "NDB: Thread id %u timed out (30s) waiting for epoch %u/%u to be handled.  Progress : %u/%u -> %u/%u."

OBSOLETE_ER_NDB_TABLE_IS_NOT_DISTRIBUTED
  eng "NDB: Inconsistency detected in distributed privilege tables. Table '%s.%s' is not distributed"

OBSOLETE_ER_NDB_CREATING_TABLE
  eng "NDB: Creating %s.%s"

OBSOLETE_ER_NDB_FLUSHING_TABLE_INFO
  eng "NDB: Flushing %s.%s"

OBSOLETE_ER_NDB_CLEANING_STRAY_TABLES
  eng "NDB: Cleaning stray tables from database '%s'"

OBSOLETE_ER_NDB_DISCOVERED_MISSING_DB
  eng "NDB: Discovered missing database '%s'"

OBSOLETE_ER_NDB_DISCOVERED_REMAINING_DB
  eng "NDB: Discovered remaining database '%s'"

OBSOLETE_ER_NDB_CLUSTER_FIND_ALL_DBS_RETRY
  eng "NDB: ndbcluster_find_all_databases retry: %u - %s"

OBSOLETE_ER_NDB_CLUSTER_FIND_ALL_DBS_FAIL
  eng "NDB: ndbcluster_find_all_databases fail: %u - %s"

OBSOLETE_ER_NDB_SKIPPING_SETUP_TABLE
  eng "NDB: skipping setup table %s.%s, in state %d"

OBSOLETE_ER_NDB_FAILED_TO_SET_UP_TABLE
  eng "NDB: failed to setup table %s.%s, error: %d, %s"

OBSOLETE_ER_NDB_MISSING_FRM_DISCOVERING
  eng "NDB: missing frm for %s.%s, discovering..."

OBSOLETE_ER_NDB_MISMATCH_IN_FRM_DISCOVERING
  eng "NDB: mismatch in frm for %s.%s, discovering..."

OBSOLETE_ER_NDB_BINLOG_CLEANING_UP_SETUP_LEFTOVERS
  eng "ndb_binlog_setup: Clean up leftovers"

OBSOLETE_ER_NDB_WAITING_INFO
  eng "NDB %s: waiting max %u sec for %s %s.  epochs: (%u/%u,%u/%u,%u/%u)  injector proc_info: %s"

OBSOLETE_ER_NDB_WAITING_INFO_WITH_MAP
  eng "NDB %s: waiting max %u sec for %s %s.  epochs: (%u/%u,%u/%u,%u/%u)  injector proc_info: %s map: %x%08x"

OBSOLETE_ER_NDB_TIMEOUT_WHILE_DISTRIBUTING
  eng "NDB %s: distributing %s timed out. Ignoring..."

OBSOLETE_ER_NDB_NOT_WAITING_FOR_DISTRIBUTING
  eng "NDB %s: not waiting for distributing %s"

OBSOLETE_ER_NDB_DISTRIBUTED_INFO
  eng "NDB: distributed %s.%s(%u/%u) type: %s(%u) query: \'%s\' to %x%08x"

OBSOLETE_ER_NDB_DISTRIBUTION_COMPLETE
  eng "NDB: distribution of %s.%s(%u/%u) type: %s(%u) query: \'%s\' - complete!"

OBSOLETE_ER_NDB_SCHEMA_DISTRIBUTION_FAILED
  eng "NDB Schema dist: Data node: %d failed, subscriber bitmask %x%08x"

OBSOLETE_ER_NDB_SCHEMA_DISTRIBUTION_REPORTS_SUBSCRIBE
  eng "NDB Schema dist: Data node: %d reports subscribe from node %d, subscriber bitmask %x%08x"

OBSOLETE_ER_NDB_SCHEMA_DISTRIBUTION_REPORTS_UNSUBSCRIBE
  eng "NDB Schema dist: Data node: %d reports unsubscribe from node %d, subscriber bitmask %x%08x"

OBSOLETE_ER_NDB_BINLOG_CANT_DISCOVER_TABLE_FROM_SCHEMA_EVENT
  eng "NDB Binlog: Could not discover table '%s.%s' from binlog schema event '%s' from node %d. my_errno: %d"

OBSOLETE_ER_NDB_BINLOG_SIGNALLING_UNKNOWN_VALUE
  eng "NDB: unknown value for binlog signalling 0x%X, %s not logged"

OBSOLETE_ER_NDB_BINLOG_REPLY_TO
  eng "NDB: reply to %s.%s(%u/%u) from %s to %x%08x"

OBSOLETE_ER_NDB_BINLOG_CANT_RELEASE_SLOCK
  eng "NDB: Could not release slock on '%s.%s', Error code: %d Message: %s"

OBSOLETE_ER_NDB_CANT_FIND_TABLE
  eng "NDB schema: Could not find table '%s.%s' in NDB"

OBSOLETE_ER_NDB_DISCARDING_EVENT_NO_OBJ
  eng "NDB: Discarding event...no obj: %s (%u/%u)"

OBSOLETE_ER_NDB_DISCARDING_EVENT_ID_VERSION_MISMATCH
  eng "NDB: Discarding event...key: %s non matching id/version [%u/%u] != [%u/%u]"

OBSOLETE_ER_NDB_CLEAR_SLOCK_INFO
  eng "NDB: CLEAR_SLOCK key: %s(%u/%u) %x%08x, from %s to %x%08x"

OBSOLETE_ER_NDB_BINLOG_SKIPPING_LOCAL_TABLE
  eng "NDB Binlog: Skipping locally defined table '%s.%s' from binlog schema event '%s' from node %d."

OBSOLETE_ER_NDB_BINLOG_ONLINE_ALTER_RENAME
  eng "NDB Binlog: handling online alter/rename"

OBSOLETE_ER_NDB_BINLOG_CANT_REOPEN_SHADOW_TABLE
  eng "NDB Binlog: Failed to re-open shadow table %s.%s"

OBSOLETE_ER_NDB_BINLOG_ONLINE_ALTER_RENAME_COMPLETE
  eng "NDB Binlog: handling online alter/rename done"

OBSOLETE_ER_NDB_BINLOG_SKIPPING_DROP_OF_LOCAL_TABLE
  eng "NDB Binlog: Skipping drop of locally defined table '%s.%s' from binlog schema event '%s' from node %d. "

OBSOLETE_ER_NDB_BINLOG_SKIPPING_RENAME_OF_LOCAL_TABLE
  eng "NDB Binlog: Skipping renaming locally defined table '%s.%s' from binlog schema event '%s' from node %d. "

OBSOLETE_ER_NDB_BINLOG_SKIPPING_DROP_OF_TABLES
  eng "NDB Binlog: Skipping drop database '%s' since it contained local tables binlog schema event '%s' from node %d. "

OBSOLETE_ER_NDB_BINLOG_GOT_DIST_PRIV_EVENT_FLUSHING_PRIVILEGES
  eng "Got dist_priv event: %s, flushing privileges"

OBSOLETE_ER_NDB_BINLOG_GOT_SCHEMA_EVENT
  eng "NDB: got schema event on %s.%s(%u/%u) query: '%s' type: %s(%d) node: %u slock: %x%08x"

OBSOLETE_ER_NDB_BINLOG_SKIPPING_OLD_SCHEMA_OPERATION
  eng "NDB schema: Skipping old schema operation(RENAME_TABLE_NEW) on %s.%s"

OBSOLETE_ER_NDB_CLUSTER_FAILURE
  eng "NDB Schema dist: cluster failure at epoch %u/%u."

OBSOLETE_ER_NDB_TABLES_INITIALLY_READ_ONLY_ON_RECONNECT
  eng "NDB Binlog: ndb tables initially read only on reconnect."

OBSOLETE_ER_NDB_IGNORING_UNKNOWN_EVENT
  eng "NDB Schema dist: unknown event %u, ignoring..."

OBSOLETE_ER_NDB_BINLOG_OPENING_INDEX
  eng "NDB Binlog: Opening ndb_binlog_index: %d, '%s'"

OBSOLETE_ER_NDB_BINLOG_CANT_LOCK_NDB_BINLOG_INDEX
  eng "NDB Binlog: Unable to lock table ndb_binlog_index"

OBSOLETE_ER_NDB_BINLOG_INJECTING_RANDOM_WRITE_FAILURE
  eng "NDB Binlog: Injecting random write failure"

OBSOLETE_ER_NDB_BINLOG_CANT_WRITE_TO_NDB_BINLOG_INDEX
  eng "NDB Binlog: Failed writing to ndb_binlog_index for epoch %u/%u  orig_server_id %u orig_epoch %u/%u with error %d."

OBSOLETE_ER_NDB_BINLOG_WRITING_TO_NDB_BINLOG_INDEX
  eng "NDB Binlog: Writing row (%s) to ndb_binlog_index - %s"

OBSOLETE_ER_NDB_BINLOG_CANT_COMMIT_TO_NDB_BINLOG_INDEX
  eng "NDB Binlog: Failed committing transaction to ndb_binlog_index with error %d."

OBSOLETE_ER_NDB_BINLOG_WRITE_INDEX_FAILED_AFTER_KILL
  eng "NDB Binlog: Failed writing to ndb_binlog_index table while retrying after kill during shutdown"

OBSOLETE_ER_NDB_BINLOG_USING_SERVER_ID_0_SLAVES_WILL_NOT
  eng "NDB: server id set to zero - changes logged to bin log with server id zero will be logged with another server id by replica mysqlds"

OBSOLETE_ER_NDB_SERVER_ID_RESERVED_OR_TOO_LARGE
  eng "NDB: server id provided is too large to be represented in opt_server_id_bits or is reserved"

OBSOLETE_ER_NDB_BINLOG_REQUIRES_V2_ROW_EVENTS
  eng "NDB: --ndb-log-transaction-id requires v2 Binlog row events but server is using v1."

OBSOLETE_ER_NDB_BINLOG_STATUS_FORCING_FULL_USE_WRITE
  eng "NDB: ndb-log-apply-status forcing %s.%s to FULL USE_WRITE"

OBSOLETE_ER_NDB_BINLOG_GENERIC_MESSAGE
  eng "NDB Binlog: %s"

OBSOLETE_ER_NDB_CONFLICT_GENERIC_MESSAGE
  eng "%s"

OBSOLETE_ER_NDB_TRANS_DEPENDENCY_TRACKER_ERROR
  eng "%s"

OBSOLETE_ER_NDB_CONFLICT_FN_PARSE_ERROR
  eng "NDB Replica: Table %s.%s : Parse error on conflict fn : %s"

OBSOLETE_ER_NDB_CONFLICT_FN_SETUP_ERROR
  eng "NDB Replica: Table %s.%s : %s"

OBSOLETE_ER_NDB_BINLOG_FAILED_TO_GET_TABLE
  eng "NDB Binlog: Failed to get table %s from ndb: %s, %d"

OBSOLETE_ER_NDB_BINLOG_NOT_LOGGING
  eng "NDB Binlog: NOT logging %s"

OBSOLETE_ER_NDB_BINLOG_CREATE_TABLE_EVENT_FAILED
  eng "NDB Binlog: FAILED CREATE (DISCOVER) TABLE Event: %s"

OBSOLETE_ER_NDB_BINLOG_CREATE_TABLE_EVENT_INFO
  eng "NDB Binlog: CREATE (DISCOVER) TABLE Event: %s"

OBSOLETE_ER_NDB_BINLOG_DISCOVER_TABLE_EVENT_INFO
  eng "NDB Binlog: DISCOVER TABLE Event: %s"

OBSOLETE_ER_NDB_BINLOG_BLOB_REQUIRES_PK
  eng "NDB Binlog: logging of table %s with BLOB attribute and no PK is not supported"

OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_EVENT_IN_DB
  eng "NDB Binlog: Unable to create event in database. Event: %s  Error Code: %d  Message: %s"

OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_EVENT_IN_DB_AND_CANT_DROP
  eng "NDB Binlog: Unable to create event in database.  Attempt to correct with drop failed. Event: %s Error Code: %d Message: %s"

OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_EVENT_IN_DB_DROPPED
  eng "NDB Binlog: Unable to create event in database.  Attempt to correct with drop ok, but create failed. Event: %s Error Code: %d Message: %s"

OBSOLETE_ER_NDB_BINLOG_DISCOVER_REUSING_OLD_EVENT_OPS
  eng "NDB Binlog: discover reusing old ev op"

OBSOLETE_ER_NDB_BINLOG_CREATING_NDBEVENTOPERATION_FAILED
  eng "NDB Binlog: Creating NdbEventOperation failed for %s"

OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_BLOB
  eng "NDB Binlog: Creating NdbEventOperation blob field %u handles failed (code=%d) for %s"

OBSOLETE_ER_NDB_BINLOG_NDBEVENT_EXECUTE_FAILED
  eng "NDB Binlog: ndbevent->execute failed for %s; %d %s"

OBSOLETE_ER_NDB_CREATE_EVENT_OPS_LOGGING_INFO
  eng "NDB Binlog: logging %s (%s,%s)"

OBSOLETE_ER_NDB_BINLOG_CANT_DROP_EVENT_FROM_DB
  eng "NDB Binlog: Unable to drop event in database. Event: %s Error Code: %d Message: %s"

OBSOLETE_ER_NDB_TIMED_OUT_IN_DROP_TABLE
  eng "NDB %s: %s timed out. Ignoring..."

OBSOLETE_ER_NDB_BINLOG_UNHANDLED_ERROR_FOR_TABLE
  eng "NDB Binlog: unhandled error %d for table %s"

OBSOLETE_ER_NDB_BINLOG_CLUSTER_FAILURE
  eng "NDB Binlog: cluster failure for %s at epoch %u/%u."

OBSOLETE_ER_NDB_BINLOG_UNKNOWN_NON_DATA_EVENT
  eng "NDB Binlog: unknown non data event %d for %s. Ignoring..."

OBSOLETE_ER_NDB_BINLOG_INJECTOR_DISCARDING_ROW_EVENT_METADATA
  eng "NDB: Binlog Injector discarding row event meta data as server is using v1 row events. (%u %x)"

OBSOLETE_ER_NDB_REMAINING_OPEN_TABLES
  eng "remove_all_event_operations: Remaining open tables: "

OBSOLETE_ER_NDB_REMAINING_OPEN_TABLE_INFO
  eng "  %s.%s, use_count: %u"

OBSOLETE_ER_NDB_COULD_NOT_GET_APPLY_STATUS_SHARE
  eng "NDB: Could not get apply status share"

OBSOLETE_ER_NDB_BINLOG_SERVER_SHUTDOWN_DURING_NDB_CLUSTER_START
  eng "NDB Binlog: Server shutdown detected while waiting for ndbcluster to start..."

OBSOLETE_ER_NDB_BINLOG_CLUSTER_RESTARTED_RESET_MASTER_SUGGESTED
  eng "NDB Binlog: cluster has been restarted --initial or with older filesystem. ndb_latest_handled_binlog_epoch: %u/%u, while current epoch: %u/%u. RESET SOURCE should be issued. Resetting ndb_latest_handled_binlog_epoch."

OBSOLETE_ER_NDB_BINLOG_CLUSTER_HAS_RECONNECTED
  eng "NDB Binlog: cluster has reconnected. Changes to the database that occurred while disconnected will not be in the binlog"

OBSOLETE_ER_NDB_BINLOG_STARTING_LOG_AT_EPOCH
  eng "NDB Binlog: starting log at epoch %u/%u"

OBSOLETE_ER_NDB_BINLOG_NDB_TABLES_WRITABLE
  eng "NDB Binlog: ndb tables writable"

OBSOLETE_ER_NDB_BINLOG_SHUTDOWN_DETECTED
  eng "NDB Binlog: Server shutdown detected..."

OBSOLETE_ER_NDB_BINLOG_LOST_SCHEMA_CONNECTION_WAITING
  eng "NDB Binlog: Just lost schema connection, hanging around"

OBSOLETE_ER_NDB_BINLOG_LOST_SCHEMA_CONNECTION_CONTINUING
  eng "NDB Binlog: ...and on our way"

OBSOLETE_ER_NDB_BINLOG_ERROR_HANDLING_SCHEMA_EVENT
  eng "NDB: error %lu (%s) on handling binlog schema event"

OBSOLETE_ER_NDB_BINLOG_CANT_INJECT_APPLY_STATUS_WRITE_ROW
  eng "NDB Binlog: Failed to inject apply status write row"

OBSOLETE_ER_NDB_BINLOG_ERROR_DURING_GCI_ROLLBACK
  eng "NDB Binlog: Error during ROLLBACK of GCI %u/%u. Error: %d"

OBSOLETE_ER_NDB_BINLOG_ERROR_DURING_GCI_COMMIT
  eng "NDB Binlog: Error during COMMIT of GCI. Error: %d"

OBSOLETE_ER_NDB_BINLOG_LATEST_TRX_IN_EPOCH_NOT_IN_BINLOG
  eng "NDB Binlog: latest transaction in epoch %u/%u not in binlog as latest handled epoch is %u/%u"

OBSOLETE_ER_NDB_BINLOG_RELEASING_EXTRA_SHARE_REFERENCES
  eng "NDB Binlog: Release extra share references"

OBSOLETE_ER_NDB_BINLOG_REMAINING_OPEN_TABLES
  eng "NDB Binlog: remaining open tables: "

OBSOLETE_ER_NDB_BINLOG_REMAINING_OPEN_TABLE_INFO
  eng "  %s.%s state: %u use_count: %u"

ER_TREE_CORRUPT_PARENT_SHOULD_POINT_AT_PARENT
  eng "Wrong tree: Parent doesn't point at parent"

ER_TREE_CORRUPT_ROOT_SHOULD_BE_BLACK
  eng "Wrong tree: Root should be black"

ER_TREE_CORRUPT_2_CONSECUTIVE_REDS
  eng "Wrong tree: Found two red in a row"

ER_TREE_CORRUPT_RIGHT_IS_LEFT
  eng "Wrong tree: Found right == left"

ER_TREE_CORRUPT_INCORRECT_BLACK_COUNT
  eng "Wrong tree: Incorrect black-count: %d - %d"

ER_WRONG_COUNT_FOR_ORIGIN
  eng "Use_count: Wrong count %lu for origin %p"

ER_WRONG_COUNT_FOR_KEY
  eng "Use_count: Wrong count for key at %p, %lu should be %lu"

ER_WRONG_COUNT_OF_ELEMENTS
  eng "Wrong number of elements: %u (should be %u) for tree at %p"

ER_RPL_ERROR_READING_REPLICA_WORKER_CONFIGURATION
  eng "Error reading replica worker configuration"

OBSOLETE_ER_RPL_ERROR_WRITING_SLAVE_WORKER_CONFIGURATION
  eng "Error writing replica worker configuration"

ER_RPL_FAILED_TO_OPEN_RELAY_LOG
  eng "Failed to open relay log %s, error: %s"

ER_RPL_WORKER_CANT_READ_RELAY_LOG
  eng "Error when worker read relay log events,relay log name %s, position %llu"

ER_RPL_WORKER_CANT_FIND_NEXT_RELAY_LOG
  eng "Failed to find next relay log when retrying the transaction, current relay log is %s"

ER_RPL_MTA_REPLICA_COORDINATOR_HAS_WAITED
  eng "Multi-threaded replica: Coordinator has waited %lu times hitting replica_pending_jobs_size_max; current event size = %zu."

ER_BINLOG_FAILED_TO_WRITE_DROP_FOR_TEMP_TABLES
  eng "Failed to write the DROP statement for temporary tables to binary log"

ER_BINLOG_OOM_WRITING_DELETE_WHILE_OPENING_HEAP_TABLE
  eng "When opening HEAP table, could not allocate memory to write 'DELETE FROM `%s`.`%s`' to the binary log"

ER_FAILED_TO_REPAIR_TABLE
  eng "Couldn't repair table: %s.%s"

ER_FAILED_TO_REMOVE_TEMP_TABLE
  eng "Could not remove temporary table: '%s', error: %d"

ER_SYSTEM_TABLE_NOT_TRANSACTIONAL
  eng "System table '%.*s' is expected to be transactional."

ER_RPL_ERROR_WRITING_SOURCE_CONFIGURATION
  eng "Error writing source configuration."

ER_RPL_ERROR_READING_SOURCE_CONFIGURATION
  eng "Error reading source configuration."

ER_RPL_SSL_INFO_IN_CONNECTION_METADATA_IGNORED
  eng "SSL information in the connection metadata repository are ignored because this MySQL replica was compiled without SSL support."

ER_PLUGIN_FAILED_DEINITIALIZATION
  eng "Plugin '%s' of type %s failed deinitialization"

ER_PLUGIN_HAS_NONZERO_REFCOUNT_AFTER_DEINITIALIZATION
  eng "Plugin '%s' has ref_count=%d after deinitialization."

ER_PLUGIN_SHUTTING_DOWN_PLUGIN
  eng "Shutting down plugin '%s'"

ER_PLUGIN_REGISTRATION_FAILED
  eng "Plugin '%s' registration as a %s failed."

ER_PLUGIN_CANT_OPEN_PLUGIN_TABLE
  eng "Could not open the mysql.plugin table. Please perform the MySQL upgrade procedure."

ER_PLUGIN_CANT_LOAD
  eng "Couldn't load plugin named '%s' with soname '%s'."

ER_PLUGIN_LOAD_PARAMETER_TOO_LONG
  eng "plugin-load parameter too long"

ER_PLUGIN_FORCING_SHUTDOWN
  eng "Plugin '%s' will be forced to shutdown"

ER_PLUGIN_HAS_NONZERO_REFCOUNT_AFTER_SHUTDOWN
  eng "Plugin '%s' has ref_count=%d after shutdown."

ER_PLUGIN_UNKNOWN_VARIABLE_TYPE
  eng "Unknown variable type code 0x%x in plugin '%s'."

ER_PLUGIN_VARIABLE_SET_READ_ONLY
  eng "Server variable %s of plugin %s was forced to be read-only: string variable without update_func and PLUGIN_VAR_MEMALLOC flag"

ER_PLUGIN_VARIABLE_MISSING_NAME
  eng "Missing variable name in plugin '%s'."

ER_PLUGIN_VARIABLE_NOT_ALLOCATED_THREAD_LOCAL
  eng "Thread local variable '%s' not allocated in plugin '%s'."

ER_PLUGIN_OOM
  eng "Out of memory for plugin '%s'."

ER_PLUGIN_BAD_OPTIONS
  eng "Bad options for plugin '%s'."

ER_PLUGIN_PARSING_OPTIONS_FAILED
  eng "Parsing options for plugin '%s' failed."

ER_PLUGIN_DISABLED
  eng "Plugin '%s' is disabled."

ER_PLUGIN_HAS_CONFLICTING_SYSTEM_VARIABLES
  eng "Plugin '%s' has conflicting system variables"

ER_PLUGIN_CANT_SET_PERSISTENT_OPTIONS
  eng "Setting persistent options for plugin '%s' failed."

ER_MY_NET_WRITE_FAILED_FALLING_BACK_ON_STDERR
  eng "Failed on my_net_write, writing to stderr instead: %s"

ER_RETRYING_REPAIR_WITHOUT_QUICK
  eng "Retrying repair of: '%s' without quick"

ER_RETRYING_REPAIR_WITH_KEYCACHE
  eng "Retrying repair of: '%s' with keycache"

ER_FOUND_ROWS_WHILE_REPAIRING
  eng "Found %s of %s rows when repairing '%s'"

ER_ERROR_DURING_OPTIMIZE_TABLE
  eng "Warning: Optimize table got errno %d on %s.%s, retrying"

ER_ERROR_ENABLING_KEYS
  eng "Warning: Enabling keys got errno %d on %s.%s, retrying"

ER_CHECKING_TABLE
  eng "Checking table:   '%s'"

ER_RECOVERING_TABLE
  eng "Recovering table: '%s'"

ER_CANT_CREATE_TABLE_SHARE_FROM_FRM
  eng "Error in creating TABLE_SHARE from %s.frm file."

ER_CANT_LOCK_TABLE
  eng "Unable to acquire lock on %s.%s"

ER_CANT_ALLOC_TABLE_OBJECT
  eng "Error in allocation memory for TABLE object."

ER_CANT_CREATE_HANDLER_OBJECT_FOR_TABLE
  eng "Error in creating handler object for table %s.%s"

ER_CANT_SET_HANDLER_REFERENCE_FOR_TABLE
  eng "Error in setting handler reference for table %s.%s"

ER_CANT_LOCK_TABLESPACE
  eng "Unable to acquire lock on tablespace name %s"

ER_CANT_UPGRADE_GENERATED_COLUMNS_TO_DD
  eng "Error in processing generated columns for table %s.%s"

ER_DD_ERROR_CREATING_ENTRY
  eng "Error in Creating DD entry for %s.%s"

ER_DD_CANT_FETCH_TABLE_DATA
  eng "Error in fetching %s.%s table data from dictionary"

ER_DD_CANT_FIX_SE_DATA
  eng "Error in fixing SE data for %s.%s"

ER_DD_CANT_CREATE_SP
  eng "Error in creating stored program '%s.%s'"

ER_CANT_OPEN_DB_OPT_USING_DEFAULT_CHARSET
  eng "Unable to open db.opt file %s. Using default Character set."

ER_CANT_CREATE_CACHE_FOR_DB_OPT
  eng "Unable to intialize IO cache to open db.opt file %s. "

ER_CANT_IDENTIFY_CHARSET_USING_DEFAULT
  eng "Unable to identify the charset in %s. Using default character set."

ER_DB_OPT_NOT_FOUND_USING_DEFAULT_CHARSET
  eng "db.opt file not found for %s database. Using default Character set."

ER_EVENT_CANT_GET_TIMEZONE_FROM_FIELD
  eng "Event '%s'.'%s': invalid value in column mysql.event.time_zone."

ER_EVENT_CANT_FIND_TIMEZONE
  eng "Event '%s'.'%s': has invalid time zone value "

ER_EVENT_CANT_GET_CHARSET
  eng "Event '%s'.'%s': invalid value in column mysql.event.character_set_client."

ER_EVENT_CANT_GET_COLLATION
  eng "Event '%s'.'%s': invalid value in column mysql.event.collation_connection."

ER_EVENT_CANT_OPEN_TABLE_MYSQL_EVENT
  eng "Failed to open mysql.event Table."

ER_CANT_PARSE_STORED_ROUTINE_BODY
  eng "Parsing '%s.%s' routine body failed.%s"

ER_CANT_OPEN_TABLE_MYSQL_PROC
  eng "Failed to open mysql.proc Table."

ER_CANT_READ_TABLE_MYSQL_PROC
  eng "Failed to read mysql.proc table."

ER_FILE_EXISTS_DURING_UPGRADE
  eng "Found %s file in mysql schema. DD will create .ibd file with same name. Please rename table and start upgrade process again."

ER_CANT_OPEN_DATADIR_AFTER_UPGRADE_FAILURE
  eng "Unable to open the data directory %s during clean up after upgrade failed"

ER_CANT_SET_PATH_FOR
  eng "Failed to set path %s"

ER_CANT_OPEN_DIR
  eng "Failed to open dir %s"

OBSOLETE_ER_NDB_CLUSTER_CONNECTION_POOL_NODEIDS
  eng "NDB: Found empty nodeid specified in --ndb-cluster-connection-pool-nodeids='%s'."

OBSOLETE_ER_NDB_CANT_PARSE_NDB_CLUSTER_CONNECTION_POOL_NODEIDS
  eng "NDB: Could not parse '%s' in --ndb-cluster-connection-pool-nodeids='%s'."

OBSOLETE_ER_NDB_INVALID_CLUSTER_CONNECTION_POOL_NODEIDS
  eng "NDB: Invalid nodeid %d in --ndb-cluster-connection-pool-nodeids='%s'."

OBSOLETE_ER_NDB_DUPLICATE_CLUSTER_CONNECTION_POOL_NODEIDS
  eng "NDB: Found duplicate nodeid %d in --ndb-cluster-connection-pool-nodeids='%s'."

OBSOLETE_ER_NDB_POOL_SIZE_CLUSTER_CONNECTION_POOL_NODEIDS
  eng "NDB: The size of the cluster connection pool must be equal to the number of nodeids in --ndb-cluster-connection-pool-nodeids='%s'."

OBSOLETE_ER_NDB_NODEID_NOT_FIRST_CONNECTION_POOL_NODEIDS
  eng "NDB: The nodeid specified by --ndb-nodeid must be equal to the first nodeid in --ndb-cluster-connection-pool-nodeids='%s'."

OBSOLETE_ER_NDB_USING_NODEID
  eng "NDB: using nodeid %u"

OBSOLETE_ER_NDB_CANT_ALLOC_GLOBAL_NDB_CLUSTER_CONNECTION
  eng "NDB: failed to allocate global ndb cluster connection"

OBSOLETE_ER_NDB_CANT_ALLOC_GLOBAL_NDB_OBJECT
  eng "NDB: failed to allocate global ndb object"

OBSOLETE_ER_NDB_USING_NODEID_LIST
  eng "NDB[%u]: using nodeid %u"

OBSOLETE_ER_NDB_CANT_ALLOC_NDB_CLUSTER_CONNECTION
  eng "NDB[%u]: failed to allocate cluster connect object"

OBSOLETE_ER_NDB_STARTING_CONNECT_THREAD
  eng "NDB[%u]: starting connect thread"

OBSOLETE_ER_NDB_NODE_INFO
  eng "NDB[%u]: NodeID: %d, %s"

OBSOLETE_ER_NDB_CANT_START_CONNECT_THREAD
  eng "NDB[%u]: failed to start connect thread"

OBSOLETE_ER_NDB_GENERIC_ERROR
  eng "NDB: error (%u) %s"

OBSOLETE_ER_NDB_CPU_MASK_TOO_SHORT
  eng "Ignored receive thread CPU mask, mask too short, %u CPUs needed in mask, only %u CPUs provided"

ER_EVENT_ERROR_CREATING_QUERY_TO_WRITE_TO_BINLOG
  eng "Event Error: An error occurred while creating query string, before writing it into binary log."

ER_EVENT_SCHEDULER_ERROR_LOADING_FROM_DB
  eng "Event Scheduler: Error while loading from disk."

ER_EVENT_SCHEDULER_ERROR_GETTING_EVENT_OBJECT
  eng "Event Scheduler: Error getting event object."

ER_EVENT_SCHEDULER_GOT_BAD_DATA_FROM_TABLE
  eng "Event Scheduler: Error while loading events from mysql.events.The table probably contains bad data or is corrupted"

ER_EVENT_CANT_GET_LOCK_FOR_DROPPING_EVENT
  eng "Unable to obtain lock for dropping event %s from schema %s"

ER_EVENT_UNABLE_TO_DROP_EVENT
  eng "Unable to drop event %s from schema %s"

OBSOLETE_ER_BINLOG_ATTACHING_THREAD_MEMORY_FINALLY_AVAILABLE
  eng "Server overcomes the temporary 'out of memory' in '%d' tries while attaching to session thread during the group commit phase."

ER_BINLOG_CANT_RESIZE_CACHE
  eng "Unable to resize binlog IOCACHE auxiliary file"

ER_BINLOG_FILE_BEING_READ_NOT_PURGED
  eng "file %s was not purged because it was being read by thread number %u"

ER_BINLOG_IO_ERROR_READING_HEADER
  eng "I/O error reading the header from the binary log, errno=%d, io cache code=%d"

OBSOLETE_ER_BINLOG_CANT_OPEN_LOG
  eng "Failed to open log (file '%s', errno %d)"

OBSOLETE_ER_BINLOG_CANT_CREATE_CACHE_FOR_LOG
  eng "Failed to create a cache on log (file '%s')"

ER_BINLOG_FILE_EXTENSION_NUMBER_EXHAUSTED
  eng "Log filename extension number exhausted: %06lu. Please fix this by archiving old logs and updating the index files."

ER_BINLOG_FILE_NAME_TOO_LONG
  eng "Log filename too large: %s%s (%zu). Please fix this by archiving old logs and updating the index files."

ER_BINLOG_FILE_EXTENSION_NUMBER_RUNNING_LOW
  eng "Next log extension: %lu. Remaining log filename extensions: %lu. Please consider archiving some logs."

ER_BINLOG_CANT_OPEN_FOR_LOGGING
  eng "Could not open %s for logging (error %d). Turning logging off for the whole duration of the MySQL server process. To turn it on again: fix the cause, shutdown the MySQL server and restart it."

ER_BINLOG_FAILED_TO_SYNC_INDEX_FILE
  eng "MYSQL_BIN_LOG::open_index_file failed to sync the index file."

ER_BINLOG_ERROR_READING_GTIDS_FROM_RELAY_LOG
  eng "Error reading GTIDs from relaylog: %d"

ER_BINLOG_EVENTS_READ_FROM_APPLIER_METADATA
  eng "%lu events read in relaylog file '%s' for updating Retrieved_Gtid_Set and/or IO thread transaction parser state."

ER_BINLOG_ERROR_READING_GTIDS_FROM_BINARY_LOG
  eng "Error reading GTIDs from binary log: %d"

ER_BINLOG_EVENTS_READ_FROM_BINLOG_INFO
  eng "Read %lu events from binary log file '%s' to determine the GTIDs purged from binary logs."

ER_BINLOG_CANT_GENERATE_NEW_FILE_NAME
  eng "MYSQL_BIN_LOG::open failed to generate new file name."

ER_BINLOG_FAILED_TO_SYNC_INDEX_FILE_IN_OPEN
  eng "MYSQL_BIN_LOG::open failed to sync the index file."

ER_BINLOG_CANT_USE_FOR_LOGGING
  eng "Could not use %s for logging (error %d). Turning logging off for the whole duration of the MySQL server process. To turn it on again: fix the cause, shutdown the MySQL server and restart it."

ER_BINLOG_FAILED_TO_CLOSE_INDEX_FILE_WHILE_REBUILDING
  eng "While rebuilding index file %s: Failed to close the index file."

ER_BINLOG_FAILED_TO_DELETE_INDEX_FILE_WHILE_REBUILDING
  eng "While rebuilding index file %s: Failed to delete the existing index file. It could be that file is being used by some other process."

ER_BINLOG_FAILED_TO_RENAME_INDEX_FILE_WHILE_REBUILDING
  eng "While rebuilding index file %s: Failed to rename the new index file to the existing index file."

ER_BINLOG_FAILED_TO_OPEN_INDEX_FILE_AFTER_REBUILDING
  eng "After rebuilding the index file %s: Failed to open the index file."

ER_BINLOG_CANT_APPEND_LOG_TO_TMP_INDEX
  eng "MYSQL_BIN_LOG::add_log_to_index failed to append log file name: %s, to crash safe index file."

ER_BINLOG_CANT_LOCATE_OLD_BINLOG_OR_RELAY_LOG_FILES
  eng "Failed to locate old binlog or relay log files"

ER_BINLOG_CANT_DELETE_FILE
  eng "Failed to delete file '%s'"

ER_BINLOG_CANT_SET_TMP_INDEX_NAME
  eng "MYSQL_BIN_LOG::set_crash_safe_index_file_name failed to set file name."

ER_BINLOG_FAILED_TO_OPEN_TEMPORARY_INDEX_FILE
  eng "MYSQL_BIN_LOG::open_crash_safe_index_file failed to open temporary index file."

OBSOLETE_ER_BINLOG_ERROR_GETTING_NEXT_LOG_FROM_INDEX
  eng "next log error: %d  offset: %s  log: %s included: %d"

ER_BINLOG_CANT_OPEN_TMP_INDEX
  eng "%s failed to open the crash safe index file."

ER_BINLOG_CANT_COPY_INDEX_TO_TMP
  eng "%s failed to copy index file to crash safe index file."

ER_BINLOG_CANT_CLOSE_TMP_INDEX
  eng "%s failed to close the crash safe index file."

ER_BINLOG_CANT_MOVE_TMP_TO_INDEX
  eng "%s failed to move crash safe index file to index file."

ER_BINLOG_PURGE_LOGS_CALLED_WITH_FILE_NOT_IN_INDEX
  eng "MYSQL_BIN_LOG::purge_logs was called with file %s not listed in the index."

ER_BINLOG_PURGE_LOGS_CANT_SYNC_INDEX_FILE
  eng "MYSQL_BIN_LOG::purge_logs failed to sync the index file."

ER_BINLOG_PURGE_LOGS_CANT_COPY_TO_REGISTER_FILE
  eng "MYSQL_BIN_LOG::purge_logs failed to copy %s to register file."

ER_BINLOG_PURGE_LOGS_CANT_FLUSH_REGISTER_FILE
  eng "MYSQL_BIN_LOG::purge_logs failed to flush register file."

ER_BINLOG_PURGE_LOGS_CANT_UPDATE_INDEX_FILE
  eng "MYSQL_BIN_LOG::purge_logs failed to update the index file"

ER_BINLOG_PURGE_LOGS_FAILED_TO_PURGE_LOG
  eng "MYSQL_BIN_LOG::purge_logs failed to process registered files that would be purged."

ER_BINLOG_FAILED_TO_SET_PURGE_INDEX_FILE_NAME
  eng "MYSQL_BIN_LOG::set_purge_index_file_name failed to set file name."

ER_BINLOG_FAILED_TO_OPEN_REGISTER_FILE
  eng "MYSQL_BIN_LOG::open_purge_index_file failed to open register file."

ER_BINLOG_FAILED_TO_REINIT_REGISTER_FILE
  eng "MYSQL_BIN_LOG::purge_index_entry failed to reinit register file for read"

ER_BINLOG_FAILED_TO_READ_REGISTER_FILE
  eng "MYSQL_BIN_LOG::purge_index_entry error %d reading from register file."

ER_CANT_STAT_FILE
  eng "Failed to execute mysql_file_stat on file '%s'"

ER_BINLOG_CANT_DELETE_LOG_FILE_DOES_INDEX_MATCH_FILES
  eng "Failed to delete log file '%s'; consider examining correspondence of your binlog index file to the actual binlog files"

ER_BINLOG_CANT_DELETE_FILE_AND_READ_BINLOG_INDEX
  eng "Failed to delete file '%s' and read the binlog index file"

ER_BINLOG_FAILED_TO_DELETE_LOG_FILE
  eng "Failed to delete log file '%s'"

ER_BINLOG_LOGGING_INCIDENT_TO_STOP_REPLICAS
  eng "%s An incident event has been written to the binary log which will stop the replicas."

ER_BINLOG_CANT_FIND_LOG_IN_INDEX
  eng "find_log_pos() failed (error: %d)"

ER_BINLOG_RECOVERING_AFTER_CRASH_USING
  eng "Recovering after a crash using %s"

ER_BINLOG_CANT_OPEN_CRASHED_BINLOG
  eng "Failed to open the crashed binlog file when source server is recovering it."

ER_BINLOG_CANT_TRIM_CRASHED_BINLOG
  eng "Failed to trim the crashed binlog file when source server is recovering it."

ER_BINLOG_CRASHED_BINLOG_TRIMMED
  eng "Crashed binlog file %s size is %llu, but recovered up to %llu. Binlog trimmed to %llu bytes."

ER_BINLOG_CANT_CLEAR_IN_USE_FLAG_FOR_CRASHED_BINLOG
  eng "Failed to clear LOG_EVENT_BINLOG_IN_USE_F for the crashed binlog file when source server is recovering it."

ER_BINLOG_FAILED_TO_RUN_AFTER_SYNC_HOOK
  eng "Failed to run 'after_sync' hooks"

ER_TURNING_LOGGING_OFF_FOR_THE_DURATION
  eng "%s Hence turning logging off for the whole duration of the MySQL server process. To turn it on again: fix the cause, shutdown the MySQL server and restart it."

ER_BINLOG_FAILED_TO_RUN_AFTER_FLUSH_HOOK
  eng "Failed to run 'after_flush' hooks"

OBSOLETE_ER_BINLOG_CRASH_RECOVERY_FAILED
  eng "Crash recovery failed. Either correct the problem (if it's, for example, out of memory error) and restart, or delete (or rename) binary log and start mysqld with --tc-heuristic-recover={commit|rollback}"

ER_BINLOG_WARNING_SUPPRESSED
  eng "The following warning was suppressed %d times during the last %d seconds in the error log"

ER_NDB_LOG_ENTRY
  eng "NDB: %s"

ER_NDB_LOG_ENTRY_WITH_PREFIX
  eng "NDB %s: %s"

OBSOLETE_ER_NDB_BINLOG_CANT_CREATE_PURGE_THD
  eng "NDB: Unable to purge %s.%s File=%s (failed to setup thd)"

ER_INNODB_UNKNOWN_COLLATION
  eng "Unknown collation #%lu."

ER_INNODB_INVALID_LOG_GROUP_HOME_DIR
  eng "syntax error in innodb_log_group_home_dir"

ER_INNODB_INVALID_INNODB_UNDO_DIRECTORY
  eng "syntax error in innodb_undo_directory"

ER_INNODB_ILLEGAL_COLON_IN_POOL
  eng "InnoDB: innodb_buffer_pool_filename cannot have colon (:) in the file name."

ER_INNODB_INVALID_PAGE_SIZE
  eng "InnoDB: Invalid page size=%lu."

ER_INNODB_DIRTY_WATER_MARK_NOT_LOW
  eng "InnoDB: innodb_max_dirty_pages_pct_lwm cannot be set higher than innodb_max_dirty_pages_pct. Setting innodb_max_dirty_pages_pct_lwm to %lf"

ER_INNODB_IO_CAPACITY_EXCEEDS_MAX
  eng "InnoDB: innodb_io_capacity cannot be set higher than innodb_io_capacity_max. Setting innodb_io_capacity to %lu"

ER_INNODB_FILES_SAME
  eng "%s and %s file names seem to be the same."

ER_INNODB_UNREGISTERED_TRX_ACTIVE
  eng "Transaction not registered for MySQL 2PC, but transaction is active"

ER_INNODB_CLOSING_CONNECTION_ROLLS_BACK
  eng "MySQL is closing a connection that has an active InnoDB transaction. %llu row modifications will roll back."

ER_INNODB_TRX_XLATION_TABLE_OOM
  eng "InnoDB: fail to allocate memory for index translation table. Number of Index:%lu, array size:%lu"

ER_INNODB_CANT_FIND_INDEX_IN_INNODB_DD
  eng "Cannot find index %s in InnoDB index dictionary."

ER_INNODB_INDEX_COLUMN_INFO_UNLIKE_MYSQLS
  eng "Found index %s whose column info does not match that of MySQL."

OBSOLETE_ER_INNODB_CANT_OPEN_TABLE
  eng "Failed to open table %s."

ER_INNODB_CANT_BUILD_INDEX_XLATION_TABLE_FOR
  eng "Build InnoDB index translation table for Table %s failed"

ER_INNODB_PK_NOT_IN_MYSQL
  eng "Table %s has a primary key in InnoDB data dictionary, but not in MySQL!"

ER_INNODB_PK_ONLY_IN_MYSQL
  eng "Table %s has no primary key in InnoDB data dictionary, but has one in MySQL! If you created the table with a MySQL version < 3.23.54 and did not define a primary key, but defined a unique key with all non-NULL columns, then MySQL internally treats that key as the primary key. You can fix this error by dump + DROP + CREATE + reimport of the table."

ER_INNODB_CLUSTERED_INDEX_PRIVATE
  eng "Table %s key_used_on_scan is %lu even though there is no primary key inside InnoDB."

OBSOLETE_ER_INNODB_PARTITION_TABLE_LOWERCASED
  eng "Partition table %s opened after converting to lower case. The table may have been moved from a case-insensitive file system. Please recreate the table in the current file system."

ER_ERRMSG_REPLACEMENT_DODGY
  eng "Cannot replace error message (%s,%s,%s) \"%s\" with \"%s\"; wrong number or type of %% subsitutions."

ER_ERRMSG_REPLACEMENTS_FAILED
  eng "Table for error message replacements could not be found or read, or one or more replacements could not be applied."

ER_NPIPE_CANT_CREATE
  eng "%s: %s"

ER_PARTITION_MOVE_CREATED_DUPLICATE_ROW_PLEASE_FIX
  eng "Table '%-192s': Delete from part %d failed with error %d. But it was already inserted into part %d, when moving the misplaced row! Please manually fix the duplicate row: %s"

ER_AUDIT_CANT_ABORT_COMMAND
  eng "Command '%s' cannot be aborted. The trigger error was (%d) [%s]: %s"

ER_AUDIT_CANT_ABORT_EVENT
  eng "Event '%s' cannot be aborted. The trigger error was (%d) [%s]: %s"

ER_AUDIT_WARNING
  eng "%s. The trigger error was (%d) [%s]: %s"

OBSOLETE_ER_NDB_NUMBER_OF_CHANNELS
  eng "Replica SQL: Configuration with number of replication sources = %u' is not supported when applying to Ndb"

OBSOLETE_ER_NDB_REPLICA_PARALLEL_WORKERS
  eng "Replica SQL: Configuration 'replica_parallel_workers = %lu' is not supported when applying to Ndb"

OBSOLETE_ER_NDB_DISTRIBUTING_ERR
  eng "NDB %s: distributing %s err: %u"

ER_RPL_REPLICA_INSECURE_CHANGE_SOURCE
  eng "Storing MySQL user name or password information in the connection metadata repository is not secure and is therefore not recommended. Please consider using the USER and PASSWORD connection options for START REPLICA; see the 'START REPLICA Syntax' in the MySQL Manual for more information."

OBSOLETE_ER_RPL_SLAVE_FLUSH_RELAY_LOGS_NOT_ALLOWED
  eng "FLUSH RELAY LOGS cannot be performed on channel '%-.192s'."

ER_RPL_REPLICA_INCORRECT_CHANNEL
  eng "Replica channel '%s' does not exist."

ER_FAILED_TO_FIND_DL_ENTRY
  eng "Can't find symbol '%-.128s' in library."

ER_FAILED_TO_OPEN_SHARED_LIBRARY
  eng "Can't open shared library '%-.192s' (errno: %d %-.128s)."

ER_THREAD_PRIORITY_IGNORED
  eng "Thread priority attribute setting in Resource Group SQL shall be ignored due to unsupported platform or insufficient privilege."

ER_BINLOG_CACHE_SIZE_TOO_LARGE
  eng "Option binlog_cache_size (%lu) is greater than max_binlog_cache_size (%lu); setting binlog_cache_size equal to max_binlog_cache_size."

ER_BINLOG_STMT_CACHE_SIZE_TOO_LARGE
  eng "Option binlog_stmt_cache_size (%lu) is greater than max_binlog_stmt_cache_size (%lu); setting binlog_stmt_cache_size equal to max_binlog_stmt_cache_size."

ER_FAILED_TO_GENERATE_UNIQUE_LOGFILE
  eng "Can't generate a unique log-filename %-.200s.(1-999)."

ER_FAILED_TO_READ_FILE
  eng "Error reading file '%-.200s' (errno: %d - %s)"

ER_FAILED_TO_WRITE_TO_FILE
  eng "Error writing file '%-.200s' (errno: %d - %s)"

ER_BINLOG_UNSAFE_MESSAGE_AND_STATEMENT
  eng "%s Statement: %s"

ER_FORCE_CLOSE_THREAD
  eng "%s: Forcing close of thread %ld  user: '%-.48s'."

ER_SERVER_SHUTDOWN_COMPLETE
  eng "%s: Shutdown complete (mysqld %s)  %s."

ER_RPL_CANT_HAVE_SAME_BASENAME
  eng "Cannot have same base name '%s' for both binary and relay logs. Please check %s (default '%s' if --log-bin option is not used, default '%s' if --log-bin option is used without argument) and %s (default '%s') options to ensure they do not conflict."

ER_RPL_GTID_MODE_REQUIRES_ENFORCE_GTID_CONSISTENCY_ON
  eng "GTID_MODE = ON requires ENFORCE_GTID_CONSISTENCY = ON."

ER_WARN_NO_SERVERID_SPECIFIED
  eng "You have not provided a mandatory server-id. Servers in a replication topology must have unique server-ids. Please refer to the proper server start-up parameters documentation."

ER_ABORTING_USER_CONNECTION
  eng "Aborted connection %u to db: '%-.192s' user: '%-.48s' host: '%-.255s' (%-.64s)."

ER_SQL_MODE_MERGED_WITH_STRICT_MODE
  eng "'NO_ZERO_DATE', 'NO_ZERO_IN_DATE' and 'ERROR_FOR_DIVISION_BY_ZERO' sql modes should be used with strict mode. They will be merged with strict mode in a future release."

ER_GTID_PURGED_WAS_UPDATED
  eng "@@GLOBAL.GTID_PURGED was changed from '%s' to '%s'."

ER_GTID_EXECUTED_WAS_UPDATED
  eng "@@GLOBAL.GTID_EXECUTED was changed from '%s' to '%s'."

ER_DEPRECATE_MSG_WITH_REPLACEMENT
  eng "'%s' is deprecated and will be removed in a future release. Please use %s instead."

ER_TRG_CREATION_CTX_NOT_SET
  eng "Triggers for table `%-.64s`.`%-.64s` have no creation context"

ER_FILE_HAS_OLD_FORMAT
  eng "'%-.192s' has an old format, you should re-create the '%s' object(s)"

ER_VIEW_CREATION_CTX_NOT_SET
  eng "View `%-.64s`.`%-.64s` has no creation context"

OBSOLETE_ER_TABLE_NAME_CAUSES_TOO_LONG_PATH
  eng "Long database name and identifier for object resulted in a path length too long for table '%s'. Please check the path limit for your OS."

ER_TABLE_UPGRADE_REQUIRED
  eng "Table upgrade required. Please do \"REPAIR TABLE `%-.64s`\" or dump/reload to fix it!"

ER_GET_ERRNO_FROM_STORAGE_ENGINE
  eng "Got error %d - '%-.192s' from storage engine."

ER_ACCESS_DENIED_ERROR_WITHOUT_PASSWORD
  eng "Access denied for user '%-.48s'@'%-.64s'"

ER_ACCESS_DENIED_ERROR_WITH_PASSWORD
  eng "Access denied for user '%-.48s'@'%-.64s' (using password: %s)"

ER_ACCESS_DENIED_FOR_USER_ACCOUNT_LOCKED
  eng "Access denied for user '%-.48s'@'%-.64s'. Account is locked."

OBSOLETE_ER_MUST_CHANGE_EXPIRED_PASSWORD
  eng "Your password has expired. To log in you must change it using a client that supports expired passwords."

ER_SYSTEM_TABLES_NOT_SUPPORTED_BY_STORAGE_ENGINE
  eng "Storage engine '%s' does not support system tables. [%s.%s]."

OBSOLETE_ER_FILESORT_TERMINATED
  eng "Sort aborted"

ER_SERVER_STARTUP_MSG
  eng "%s: ready for connections. Version: '%s'  socket: '%s'  port: %d  %s."

ER_FAILED_TO_FIND_LOCALE_NAME
  eng "Unknown locale: '%-.64s'."

ER_FAILED_TO_FIND_COLLATION_NAME
  eng "Unknown collation: '%-.64s'."

ER_SERVER_OUT_OF_RESOURCES
  eng "Out of memory; check if mysqld or some other process uses all available memory; if not, you may have to use 'ulimit' to allow mysqld to use more memory or you can add more swap space"

ER_SERVER_OUTOFMEMORY
  eng "Out of memory; restart server and try again (needed %d bytes)"

ER_INVALID_COLLATION_FOR_CHARSET
  eng "COLLATION '%s' is not valid for CHARACTER SET '%s'"

ER_CANT_START_ERROR_LOG_SERVICE
  eng "Failed to set %s at or around \"%s\" -- service is valid, but can not be initialized; please check its configuration and make sure it can read its input(s) and write to its output(s)."

ER_CREATING_NEW_UUID_FIRST_START
  eng "Generating a new UUID: %s."

ER_FAILED_TO_GET_ABSOLUTE_PATH
  eng "Failed to get absolute path of program executable %s"

ER_PERFSCHEMA_COMPONENTS_INFRASTRUCTURE_BOOTSTRAP
  eng "Failed to bootstrap performance schema components infrastructure."

ER_PERFSCHEMA_COMPONENTS_INFRASTRUCTURE_SHUTDOWN
  eng "Failed to deinit performance schema components infrastructure."

ER_DUP_FD_OPEN_FAILED
  eng "Could not open duplicate fd for %s: %s."

ER_SYSTEM_VIEW_INIT_FAILED
  eng "System views initialization failed."

ER_RESOURCE_GROUP_POST_INIT_FAILED
  eng "Resource group post initialization failed."

ER_RESOURCE_GROUP_SUBSYSTEM_INIT_FAILED
  eng "Resource Group subsystem initialization failed."

ER_FAILED_START_MYSQLD_DAEMON
  eng "Failed to start mysqld daemon. Check mysqld error log."

ER_CANNOT_CHANGE_TO_ROOT_DIR
  eng "Cannot change to root directory: %s."

ER_PERSISTENT_PRIVILEGES_BOOTSTRAP
  eng "Failed to bootstrap persistent privileges."

ER_BASEDIR_SET_TO
  eng "Basedir set to %s."

ER_RPL_FILTER_ADD_WILD_DO_TABLE_FAILED
  eng "Could not add wild do table rule '%s'!"

ER_RPL_FILTER_ADD_WILD_IGNORE_TABLE_FAILED
  eng "Could not add wild ignore table rule '%s'!"

ER_PRIVILEGE_SYSTEM_INIT_FAILED
  eng "The privilege system failed to initialize correctly. For complete instructions on how to upgrade MySQL to a new version please see the 'Upgrading MySQL' section from the MySQL manual."

ER_CANNOT_SET_LOG_ERROR_SERVICES
  eng "Cannot set services \"%s\" requested in --log-error-services, using defaults."

ER_PERFSCHEMA_TABLES_INIT_FAILED
  eng "Performance schema tables initialization failed."

ER_TX_EXTRACTION_ALGORITHM_FOR_BINLOG_TX_DEPEDENCY_TRACKING
  eng "The transaction_write_set_extraction must be set to %s when binlog_transaction_dependency_tracking is %s."

OBSOLETE_ER_INVALID_REPLICATION_TIMESTAMPS
  eng "Invalid replication timestamps: original commit timestamp is more recent than the immediate commit timestamp. This may be an issue if delayed replication is active. Make sure that servers have their clocks set to the correct time. No further message will be emitted until after timestamps become valid again."

OBSOLETE_ER_RPL_TIMESTAMPS_RETURNED_TO_NORMAL
  eng "The replication timestamps have returned to normal values."

ER_BINLOG_FILE_OPEN_FAILED
  eng "%s."

ER_BINLOG_EVENT_WRITE_TO_STMT_CACHE_FAILED
  eng "Failed to write an incident event into stmt_cache."

ER_REPLICA_RELAY_LOG_TRUNCATE_INFO
  eng "Relaylog file %s size was %llu, but was truncated at %llu."

ER_REPLICA_RELAY_LOG_PURGE_FAILED
  eng "Unable to purge relay log files. %s:%s."

ER_RPL_REPLICA_FILTER_CREATE_FAILED
  eng "Replica: failed in creating filter for channel '%s'."

ER_RPL_REPLICA_GLOBAL_FILTERS_COPY_FAILED
  eng "Replica: failed in copying the global filters to its own per-channel filters on configuration for channel '%s'."

ER_RPL_REPLICA_RESET_FILTER_OPTIONS
  eng "There are per-channel replication filter(s) configured for channel '%.192s' which does not exist. The filter(s) have been discarded."

ER_MISSING_GRANT_SYSTEM_TABLE
  eng "The system table mysql.global_grants is missing. Please perform the MySQL upgrade procedure."

ER_MISSING_ACL_SYSTEM_TABLE
  eng "ACL table mysql.%.*s missing. Some operations may fail."

ER_ANONYMOUS_AUTH_ID_NOT_ALLOWED_IN_MANDATORY_ROLES
  eng "Can't set mandatory_role %s@%s: Anonymous authorization IDs are not allowed as roles."

ER_UNKNOWN_AUTH_ID_IN_MANDATORY_ROLE
  eng "Can't set mandatory_role: There's no such authorization ID %s@%s."

ER_WRITE_ROW_TO_PARTITION_FAILED
  eng "Table '%-192s' failed to move/insert a row from part %d into part %d: %s."

ER_RESOURCE_GROUP_METADATA_UPDATE_SKIPPED
  eng "Skipped updating resource group metadata in InnoDB read only mode."

ER_FAILED_TO_PERSIST_RESOURCE_GROUP_METADATA
  eng "Failed to persist resource group %s to Data Dictionary."

ER_FAILED_TO_DESERIALIZE_RESOURCE_GROUP
  eng "Failed to deserialize resource group %s."

ER_FAILED_TO_UPDATE_RESOURCE_GROUP
  eng "Update of resource group %s failed."

ER_RESOURCE_GROUP_VALIDATION_FAILED
  eng "Validation of resource group %s failed. Resource group is disabled."

ER_FAILED_TO_ALLOCATE_MEMORY_FOR_RESOURCE_GROUP
  eng "Unable to allocate memory for Resource Group %s."

ER_FAILED_TO_ALLOCATE_MEMORY_FOR_RESOURCE_GROUP_HASH
  eng "Failed to allocate memory for resource group hash."

ER_FAILED_TO_ADD_RESOURCE_GROUP_TO_MAP
  eng "Failed to add resource group %s to resource group map."

ER_RESOURCE_GROUP_IS_DISABLED
  eng "Resource group feature is disabled. (Server is compiled with DISABLE_PSI_THREAD)."

ER_FAILED_TO_APPLY_RESOURCE_GROUP_CONTROLLER
  eng "Unable to apply resource group controller %s."

ER_FAILED_TO_ACQUIRE_LOCK_ON_RESOURCE_GROUP
  eng "Unable to acquire lock on the resource group %s. Hint to switch resource group shall be ignored."

ER_PFS_NOTIFICATION_FUNCTION_REGISTER_FAILED
  eng "PFS %s notification function registration failed."

ER_RES_GRP_SET_THR_AFFINITY_FAILED
  eng "Unable to bind thread id %llu to cpu id %u (error code %d - %-.192s)."

ER_RES_GRP_SET_THR_AFFINITY_TO_CPUS_FAILED
  eng "Unable to bind thread id %llu to cpu ids (error code %d - %-.192s)."

ER_RES_GRP_THD_UNBIND_FROM_CPU_FAILED
  eng "Unbind thread id %llu failed. (error code %d - %-.192s)."

ER_RES_GRP_SET_THREAD_PRIORITY_FAILED
  eng "Setting thread priority %d to thread id %llu failed. (error code %d - %-.192s)."

ER_RES_GRP_FAILED_TO_DETERMINE_NICE_CAPABILITY
  eng "Unable to determine CAP_SYS_NICE capability."

ER_RES_GRP_FAILED_TO_GET_THREAD_HANDLE
  eng "%s failed: Failed to get handle for thread %llu."

ER_RES_GRP_GET_THREAD_PRIO_NOT_SUPPORTED
  eng "Retrieval of thread priority unsupported on %s."

ER_RES_GRP_FAILED_DETERMINE_CPU_COUNT
  eng "Unable to determine the number of CPUs."

ER_RES_GRP_FEATURE_NOT_AVAILABLE
  eng "Resource group feature shall not be available. Incompatible thread handling option."

ER_RES_GRP_INVALID_THREAD_PRIORITY
  eng "Invalid thread priority %d for a %s resource group. Allowed range is [%d, %d]."

ER_RES_GRP_SOLARIS_PROCESSOR_BIND_TO_CPUID_FAILED
  eng "bind_to_cpu failed: processor_bind for cpuid %u failed (error code %d - %-.192s)."

ER_RES_GRP_SOLARIS_PROCESSOR_BIND_TO_THREAD_FAILED
  eng "bind_to_cpu failed: processor_bind for thread %%llx with cpu id %u (error code %d - %-.192s)."

ER_RES_GRP_SOLARIS_PROCESSOR_AFFINITY_FAILED
  eng "%s failed: processor_affinity failed (error code %d - %-.192s)."

ER_DD_UPGRADE_RENAME_IDX_STATS_FILE_FAILED
  eng "Error in renaming mysql_index_stats.ibd."

ER_DD_UPGRADE_DD_OPEN_FAILED
  eng "Error in opening data directory %s."

ER_DD_UPGRADE_FAILED_TO_FETCH_TABLESPACES
  eng "Error in fetching list of tablespaces."

ER_DD_UPGRADE_FAILED_TO_ACQUIRE_TABLESPACE
  eng "Error in acquiring Tablespace for SDI insertion %s."

ER_DD_UPGRADE_FAILED_TO_RESOLVE_TABLESPACE_ENGINE
  eng "Error in resolving Engine name for tablespace %s with engine %s."

ER_FAILED_TO_CREATE_SDI_FOR_TABLESPACE
  eng "Error in creating SDI for %s tablespace."

ER_FAILED_TO_STORE_SDI_FOR_TABLESPACE
  eng "Error in storing SDI for %s tablespace."

ER_DD_UPGRADE_FAILED_TO_FETCH_TABLES
  eng "Error in fetching list of tables."

ER_DD_UPGRADE_DD_POPULATED
  eng "Finished populating Data Dictionary tables with data."

ER_DD_UPGRADE_INFO_FILE_OPEN_FAILED
  eng "Could not open the upgrade info file '%s' in the MySQL servers datadir, errno: %d."

ER_DD_UPGRADE_INFO_FILE_CLOSE_FAILED
  eng "Could not close the upgrade info file '%s' in the MySQL servers datadir, errno: %d."

ER_DD_UPGRADE_TABLESPACE_MIGRATION_FAILED
  eng "Got error %d from SE while migrating tablespaces."

ER_DD_UPGRADE_FAILED_TO_CREATE_TABLE_STATS
  eng "Error in creating TABLE statistics entry. Fix statistics data by using ANALYZE command."

ER_DD_UPGRADE_TABLE_STATS_MIGRATE_COMPLETED
  eng "Finished migrating TABLE statistics data."

ER_DD_UPGRADE_FAILED_TO_CREATE_INDEX_STATS
  eng "Error in creating Index statistics entry. Fix statistics data by using ANALYZE command."

ER_DD_UPGRADE_INDEX_STATS_MIGRATE_COMPLETED
  eng "Finished migrating INDEX statistics data."

ER_DD_UPGRADE_FAILED_FIND_VALID_DATA_DIR
  eng "Failed to find valid data directory."

ER_DD_UPGRADE_START
  eng "Starting upgrade of data directory."

ER_DD_UPGRADE_FAILED_INIT_DD_SE
  eng "Failed to initialize DD Storage Engine."

ER_DD_UPGRADE_FOUND_PARTIALLY_UPGRADED_DD_ABORT
  eng "Found partially upgraded DD. Aborting upgrade and deleting all DD tables. Start the upgrade process again."

ER_DD_UPGRADE_FOUND_PARTIALLY_UPGRADED_DD_CONTINUE
  eng "Found partially upgraded DD. Upgrade will continue and start the server."

ER_DD_UPGRADE_SE_LOGS_FAILED
  eng "Error in upgrading engine logs."

ER_DD_UPGRADE_SDI_INFO_UPDATE_FAILED
  eng "Error in updating SDI information."

ER_SKIP_UPDATING_METADATA_IN_SE_RO_MODE
  eng "Skip updating %s metadata in InnoDB read-only mode."

ER_CREATED_SYSTEM_WITH_VERSION
  eng "Created system views with I_S version %d."

ER_UNKNOWN_ERROR_DETECTED_IN_SE
  eng "Unknown error detected %d in handler."

ER_READ_LOG_EVENT_FAILED
  eng "Error in Log_event::read_log_event(): '%s', data_len: %lu, event_type: %d."

ER_ROW_DATA_TOO_BIG_TO_WRITE_IN_BINLOG
  eng "The row data is greater than 4GB, which is too big to write to the binary log."

ER_FAILED_TO_CONSTRUCT_DROP_EVENT_QUERY
  eng "Unable to construct DROP EVENT SQL query string."

ER_FAILED_TO_BINLOG_DROP_EVENT
  eng "Unable to binlog drop event %s.%s."

ER_FAILED_TO_START_REPLICA_THREAD
  eng "Failed to start replica threads for channel '%s'."

ER_RPL_IO_THREAD_KILLED
  eng "%s%s."

ER_REPLICA_RECONNECT_FAILED
  eng "Failed registering on source, reconnecting to try again, log '%s' at position %s. %s."

ER_REPLICA_KILLED_AFTER_RECONNECT
  eng "Replica I/O thread killed during or after reconnect."

ER_REPLICA_NOT_STARTED_ON_SOME_CHANNELS
  eng "Some of the channels are not created/initialized properly. Check for additional messages above. You will not be able to start replication on those channels until the issue is resolved and the server restarted."

ER_FAILED_TO_ADD_RPL_FILTER
  eng "Failed to add a replication filter into filter map for channel '%.192s'."

ER_PER_CHANNEL_RPL_FILTER_CONF_FOR_GRP_RPL
  eng "There are per-channel replication filter(s) configured for group replication channel '%.192s' which is disallowed. The filter(s) have been discarded."

ER_RPL_FILTERS_NOT_ATTACHED_TO_CHANNEL
  eng "There are per-channel replication filter(s) configured for channel '%.192s' which does not exist. The filter(s) have been discarded."

ER_FAILED_TO_BUILD_DO_AND_IGNORE_TABLE_HASHES
  eng "An error occurred while building do_table and ignore_table rules to hashes for per-channel filter."

ER_CLONE_PLUGIN_NOT_LOADED_TRACE
  eng "Clone plugin cannot be loaded."

ER_CLONE_HANDLER_EXIST_TRACE
  eng "Clone Handler exists."

ER_CLONE_CREATE_HANDLER_FAIL_TRACE
  eng "Could not create Clone Handler."

ER_CYCLE_TIMER_IS_NOT_AVAILABLE
  eng "The CYCLE timer is not available. WAIT events in the performance_schema will not be timed."

ER_NANOSECOND_TIMER_IS_NOT_AVAILABLE
  eng "The NANOSECOND timer is not available. IDLE/STAGE/STATEMENT/TRANSACTION events in the performance_schema will not be timed."

ER_MICROSECOND_TIMER_IS_NOT_AVAILABLE
  eng "The MICROSECOND timer is not available. IDLE/STAGE/STATEMENT/TRANSACTION events in the performance_schema will not be timed."

ER_PFS_MALLOC_ARRAY_OVERFLOW
  eng "Failed to allocate memory for %zu chunks each of size %zu for buffer '%s' due to overflow."

ER_PFS_MALLOC_ARRAY_OOM
  eng "Failed to allocate %zu bytes for buffer '%s' due to out-of-memory."

ER_INNODB_FAILED_TO_FIND_IDX_WITH_KEY_NO
  eng "InnoDB could not find index %s key no %u for table %s through its index translation table."

ER_INNODB_FAILED_TO_FIND_IDX
  eng "Cannot find index %s in InnoDB index translation table."

ER_INNODB_FAILED_TO_FIND_IDX_FROM_DICT_CACHE
  eng "InnoDB could not find key no %u with name %s from dict cache for table %s."

ER_INNODB_ACTIVE_INDEX_CHANGE_FAILED
  eng "InnoDB: change_active_index(%u) failed."

ER_INNODB_DIFF_IN_REF_LEN
  eng "Stored ref len is %lu, but table ref len is %lu."

ER_WRONG_TYPE_FOR_COLUMN_PREFIX_IDX_FLD
  eng "MySQL is trying to create a column prefix index field, on an inappropriate data type. Table name %s, column name %s."

ER_INNODB_CANNOT_CREATE_TABLE
  eng "Cannot create table %s."

ER_INNODB_INTERNAL_INDEX
  eng "Found index %s in InnoDB index list but not its MySQL index number. It could be an InnoDB internal index."

ER_INNODB_IDX_CNT_MORE_THAN_DEFINED_IN_MYSQL
  eng "InnoDB: Table %s contains %lu indexes inside InnoDB, which is different from the number of indexes %u defined in MySQL."

ER_INNODB_IDX_CNT_FEWER_THAN_DEFINED_IN_MYSQL
  eng "Table %s contains fewer indexes inside InnoDB than are defined in the MySQL. Have you mixed up with data dictionary from different installation?"

ER_INNODB_IDX_COLUMN_CNT_DIFF
  eng "Index %s of %s has %lu columns unique inside InnoDB, but MySQL is asking statistics for %lu columns. Have you mixed data dictionary from different installation?"

ER_INNODB_USE_MONITOR_GROUP_NAME
  eng "Monitor counter '%s' cannot be turned on/off individually. Please use its module name to turn on/off the counters in the module as a group."

ER_INNODB_MONITOR_DEFAULT_VALUE_NOT_DEFINED
  eng "Default value is not defined for this set option. Please specify correct counter or module name."

ER_INNODB_MONITOR_IS_ENABLED
  eng "InnoDB: Monitor %s is already enabled."

ER_INNODB_INVALID_MONITOR_COUNTER_NAME
  eng "Invalid monitor counter : %s."

ER_WIN_LOAD_LIBRARY_FAILED
  eng "LoadLibrary(\"%s\") failed: GetLastError returns %lu."

ER_PARTITION_HANDLER_ADMIN_MSG
  eng "%s."

ER_RPL_AM_INIT_INFO_MSG
  eng "%s."

ER_DD_UPGRADE_TABLE_INTACT_ERROR
  eng "%s."

ER_SERVER_INIT_COMPILED_IN_COMMANDS
  eng "%s."

ER_MYISAM_CHECK_METHOD_ERROR
  eng "%s."

ER_MYISAM_CRASHED_ERROR
  eng "%s."

ER_WAITPID_FAILED
  eng "Unable to wait for process %lld."

ER_FAILED_TO_FIND_MYSQLD_STATUS
  eng "Unable to determine if daemon is running: %s (rc=%d)."

ER_INNODB_ERROR_LOGGER_MSG
  eng "%s"

ER_INNODB_ERROR_LOGGER_FATAL_MSG
  eng "[FATAL] InnoDB: %s"

ER_DEPRECATED_SYNTAX_WITH_REPLACEMENT
  eng "The syntax '%s' is deprecated and will be removed in a future release. Please use %s instead."

ER_DEPRECATED_SYNTAX_NO_REPLACEMENT
  eng "The syntax '%s' is deprecated and will be removed in a future release."

ER_DEPRECATE_MSG_NO_REPLACEMENT
  eng "'%s' is deprecated and will be removed in a future release."

ER_LOG_PRINTF_MSG
  eng "%s"

ER_BINLOG_LOGGING_NOT_POSSIBLE
  eng "Binary logging not possible. Message: %s."

ER_FAILED_TO_SET_PERSISTED_OPTIONS
  eng "Failed to set persisted options."

ER_COMPONENTS_FAILED_TO_ACQUIRE_SERVICE_IMPLEMENTATION
  eng "Cannot acquire specified service implementation: '%.192s'."

ER_RES_GRP_INVALID_VCPU_RANGE
  eng "Invalid VCPU range %u-%u."

ER_RES_GRP_INVALID_VCPU_ID
  eng "Invalid cpu id %u."

ER_ERROR_DURING_FLUSH_LOG_COMMIT_PHASE
  eng "Got error %d during FLUSH_LOGS."

ER_DROP_DATABASE_FAILED_RMDIR_MANUALLY
  eng "Problem while dropping database. Can't remove database directory (%s). Please remove it manually."

ER_EXPIRE_LOGS_DAYS_IGNORED
  eng "The option expire_logs_days cannot be used together with option binlog_expire_logs_seconds. Therefore, value of expire_logs_days is ignored."

ER_BINLOG_MALFORMED_OR_OLD_RELAY_LOG
  eng "malformed or very old relay log which does not have FormatDescriptor."

ER_DD_UPGRADE_VIEW_COLUMN_NAME_TOO_LONG
  eng "Upgrade of view '%s.%s' failed. Re-create the view with the explicit column name lesser than 64 characters."

ER_TABLE_NEEDS_DUMP_UPGRADE
  eng "Table upgrade required for `%-.64s`.`%-.64s`. Please dump/reload table to fix it!"

ER_DD_UPGRADE_FAILED_TO_UPDATE_VER_NO_IN_TABLESPACE
  eng "Error in updating version number in %s tablespace."

ER_KEYRING_MIGRATION_FAILED
  eng "Keyring migration failed."

ER_KEYRING_MIGRATION_SUCCESSFUL
  eng "Keyring migration successful."

ER_RESTART_RECEIVED_INFO
  eng "Received RESTART from user %s.  Restarting mysqld (Version: %s)."

ER_LCTN_CHANGED
  eng "Different lower_case_table_names settings for server ('%u') and data dictionary ('%u')."

ER_DD_INITIALIZE
  eng "Data dictionary initializing version '%u'."

ER_DD_RESTART
  eng "Data dictionary restarting version '%u'."

ER_DD_UPGRADE
  eng "Data dictionary upgrading from version '%u' to '%u'."

ER_DD_UPGRADE_OFF
  eng "Data dictionary upgrade prohibited by the command line option '--no_dd_upgrade'."

ER_DD_UPGRADE_VERSION_NOT_SUPPORTED
  eng "Upgrading the data dictionary from dictionary version '%u' is not supported."

ER_DD_UPGRADE_SCHEMA_UNAVAILABLE
  eng "Upgrading the data dictionary failed, temporary schema name '%-.192s' not available."

ER_DD_MINOR_DOWNGRADE
  eng "Data dictionary minor downgrade from version '%u' to '%u'."

ER_DD_MINOR_DOWNGRADE_VERSION_NOT_SUPPORTED
  eng "Minor downgrade of the Data dictionary from dictionary version '%u' is not supported."

ER_DD_NO_VERSION_FOUND
  eng "No data dictionary version number found."

ER_THREAD_POOL_NOT_SUPPORTED_ON_PLATFORM
  eng "Thread pool not supported, requires a minimum of %s."

ER_THREAD_POOL_SIZE_TOO_LOW
  eng "thread_pool_size=0 means thread pool disabled, Allowed range of thread_pool_size is %d-%d."

ER_THREAD_POOL_SIZE_TOO_HIGH
  eng "thread_pool_size=%lu is too high, %d is maximum, thread pool is disabled. Allowed range of thread_pool_size is %d-%d."

ER_THREAD_POOL_ALGORITHM_INVALID
  eng "thread_pool_algorithm can be set to 0 and 1, 0 indicates the default low concurrency algorithm, 1 means a high concurrency algorithm."

ER_THREAD_POOL_INVALID_STALL_LIMIT
  eng "thread_pool_stall_limit can be %d at minimum and %d at maximum, smaller values would render the thread pool fairly useless and higher values could make it possible to have undetected deadlock issues in the MySQL Server."

ER_THREAD_POOL_INVALID_PRIO_KICKUP_TIMER
  eng "Invalid value of thread_pool_prio_kickup_timer specified. Value of thread_pool_prio_kickup_timer should be in range 0-4294967294."

ER_THREAD_POOL_MAX_UNUSED_THREADS_INVALID
  eng "thread_pool_max_unused_threads cannot be set higher than %d."

ER_THREAD_POOL_CON_HANDLER_INIT_FAILED
  eng "Failed to instantiate the connection handler object."

ER_THREAD_POOL_INIT_FAILED
  eng "Failed to initialize thread pool plugin."

OBSOLETE_ER_THREAD_POOL_PLUGIN_STARTED
  eng "Thread pool plugin started successfully with parameters: thread_pool_size = %lu, thread_pool_algorithm = %s, thread_pool_stall_limit = %u, thread_pool_prio_kickup_timer = %u, thread_pool_max_unused_threads = %u, thread_pool_high_priority_connection = %d."

ER_THREAD_POOL_CANNOT_SET_THREAD_SPECIFIC_DATA
  eng "Can't setup connection teardown thread-specific data."

ER_THREAD_POOL_FAILED_TO_CREATE_CONNECT_HANDLER_THD
  eng "Creation of connect handler thread failed."

ER_THREAD_POOL_FAILED_TO_CREATE_THD_AND_AUTH_CONN
  eng "Failed to create thd and authenticate connection."

ER_THREAD_POOL_FAILED_PROCESS_CONNECT_EVENT
  eng "Failed to process connection event."

ER_THREAD_POOL_FAILED_TO_CREATE_POOL
  eng "Can't create pool thread (error %d, errno: %d)."

ER_THREAD_POOL_RATE_LIMITED_ERROR_MSGS
  eng "%.*s."

ER_TRHEAD_POOL_LOW_LEVEL_INIT_FAILED
  eng "tp_group_low_level_init() failed."

ER_THREAD_POOL_LOW_LEVEL_REARM_FAILED
  eng "Rearm failed even after 30 seconds, can't continue without notify socket."

ER_THREAD_POOL_BUFFER_TOO_SMALL
  eng "%s: %s buffer is too small"

ER_MECAB_NOT_SUPPORTED
  eng "Mecab v%s is not supported, the lowest version supported is v%s."

ER_MECAB_NOT_VERIFIED
  eng "Mecab v%s is not verified, the highest version supported is v%s."

ER_MECAB_CREATING_MODEL
  eng "Mecab: Trying createModel(%s)."

ER_MECAB_FAILED_TO_CREATE_MODEL
  eng "Mecab: createModel() failed: %s."

ER_MECAB_FAILED_TO_CREATE_TRIGGER
  eng "Mecab: createTagger() failed: %s."

ER_MECAB_UNSUPPORTED_CHARSET
  eng "Mecab: Unsupported dictionary charset %s."

ER_MECAB_CHARSET_LOADED
  eng "Mecab: Loaded dictionary charset is %s."

ER_MECAB_PARSE_FAILED
  eng "Mecab: parse() failed: %s."

ER_MECAB_OOM_WHILE_PARSING_TEXT
  eng "Mecab: parse() failed: out of memory."

ER_MECAB_CREATE_LATTICE_FAILED
  eng "Mecab: createLattice() failed: %s."

ER_SEMISYNC_TRACE_ENTER_FUNC
  eng "---> %s enter."

ER_SEMISYNC_TRACE_EXIT_WITH_INT_EXIT_CODE
  eng "<--- %s exit (%d)."

ER_SEMISYNC_TRACE_EXIT_WITH_BOOL_EXIT_CODE
  eng "<--- %s exit (%s)."

ER_SEMISYNC_TRACE_EXIT
  eng "<--- %s exit."

ER_SEMISYNC_RPL_INIT_FOR_TRX
  eng "Semi-sync replication initialized for transactions."

ER_SEMISYNC_FAILED_TO_ALLOCATE_TRX_NODE
  eng "%s: transaction node allocation failed for: (%s, %lu)."

ER_SEMISYNC_BINLOG_WRITE_OUT_OF_ORDER
  eng "%s: binlog write out-of-order, tail (%s, %lu), new node (%s, %lu)."

ER_SEMISYNC_INSERT_LOG_INFO_IN_ENTRY
  eng "%s: insert (%s, %lu) in entry(%u)."

ER_SEMISYNC_PROBE_LOG_INFO_IN_ENTRY
  eng "%s: probe (%s, %lu) in entry(%u)."

ER_SEMISYNC_CLEARED_ALL_ACTIVE_TRANSACTION_NODES
  eng "%s: cleared all nodes."

ER_SEMISYNC_CLEARED_ACTIVE_TRANSACTION_TILL_POS
  eng "%s: cleared %d nodes back until pos (%s, %lu)."

ER_SEMISYNC_REPLY_MAGIC_NO_ERROR
  eng "Read semi-sync reply magic number error."

ER_SEMISYNC_REPLY_PKT_LENGTH_TOO_SMALL
  eng "Read semi-sync reply length error: packet is too small."

ER_SEMISYNC_REPLY_BINLOG_FILE_TOO_LARGE
  eng "Read semi-sync reply binlog file length too large."

ER_SEMISYNC_SERVER_REPLY
  eng "%s: Got reply(%s, %lu) from server %u."

ER_SEMISYNC_FUNCTION_CALLED_TWICE
  eng "%s called twice."

ER_SEMISYNC_RPL_ENABLED_ON_SOURCE
  eng "Semi-sync replication enabled on the source."

ER_SEMISYNC_SOURCE_OOM
  eng "Cannot allocate memory to enable semi-sync on the source."

ER_SEMISYNC_DISABLED_ON_SOURCE
  eng "Semi-sync replication disabled on the source."

ER_SEMISYNC_FORCED_SHUTDOWN
  eng "SEMISYNC: Forced shutdown. Some updates might not be replicated."

ER_SEMISYNC_SOURCE_GOT_REPLY_AT_POS
  eng "%s: Got reply at (%s, %lu)."

ER_SEMISYNC_SOURCE_SIGNAL_ALL_WAITING_THREADS
  eng "%s: signal all waiting threads."

ER_SEMISYNC_SOURCE_TRX_WAIT_POS
  eng "%s: wait pos (%s, %lu), repl(%d)."

ER_SEMISYNC_BINLOG_REPLY_IS_AHEAD
  eng "%s: Binlog reply is ahead (%s, %lu)."

ER_SEMISYNC_MOVE_BACK_WAIT_POS
  eng "%s: move back wait position (%s, %lu)."

ER_SEMISYNC_INIT_WAIT_POS
  eng "%s: init wait position (%s, %lu)."

ER_SEMISYNC_WAIT_TIME_FOR_BINLOG_SENT
  eng "%s: wait %lu ms for binlog sent (%s, %lu)."

ER_SEMISYNC_WAIT_FOR_BINLOG_TIMEDOUT
  eng "Timeout waiting for reply of binlog (file: %s, pos: %lu), semi-sync up to file %s, position %lu."

ER_SEMISYNC_WAIT_TIME_ASSESSMENT_FOR_COMMIT_TRX_FAILED
  eng "Assessment of waiting time for commitTrx failed at wait position (%s, %lu)."

ER_SEMISYNC_RPL_SWITCHED_OFF
  eng "Semi-sync replication switched OFF."

ER_SEMISYNC_RPL_SWITCHED_ON
  eng "Semi-sync replication switched ON at (%s, %lu)."

ER_SEMISYNC_NO_SPACE_IN_THE_PKT
  eng "No enough space in the packet for semi-sync extra header, semi-sync replication disabled."

ER_SEMISYNC_SYNC_HEADER_UPDATE_INFO
  eng "%s: server(%d), (%s, %lu) sync(%d), repl(%d)."

ER_SEMISYNC_FAILED_TO_INSERT_TRX_NODE
  eng "Semi-sync failed to insert tranx_node for binlog file: %s, position: %lu."

ER_SEMISYNC_TRX_SKIPPED_AT_POS
  eng "%s: Transaction skipped at (%s, %lu)."

ER_SEMISYNC_SOURCE_FAILED_ON_NET_FLUSH
  eng "Semi-sync source failed on net_flush() before waiting for replica reply."

ER_SEMISYNC_RECEIVED_ACK_IS_SMALLER
  eng "The received ack is smaller than m_greatest_ack."

ER_SEMISYNC_ADD_ACK_TO_SLOT
  eng "Add the ack into slot %u."

ER_SEMISYNC_UPDATE_EXISTING_REPLICA_ACK
  eng "Update an exsiting ack in slot %u."

ER_SEMISYNC_FAILED_TO_START_ACK_RECEIVER_THD
  eng "Failed to start semi-sync ACK receiver thread,  could not create thread(errno:%d)."

ER_SEMISYNC_STARTING_ACK_RECEIVER_THD
  eng "Starting ack receiver thread."

ER_SEMISYNC_FAILED_TO_WAIT_ON_DUMP_SOCKET
  eng "Failed to wait on semi-sync dump sockets, error: errno=%d."

ER_SEMISYNC_STOPPING_ACK_RECEIVER_THREAD
  eng "Stopping ack receiver thread."

ER_SEMISYNC_FAILED_REGISTER_REPLICA_TO_RECEIVER
  eng "Failed to register replica to semi-sync ACK receiver thread."

ER_SEMISYNC_START_BINLOG_DUMP_TO_REPLICA
  eng "Start %s binlog_dump to replica (server_id: %d), pos(%s, %lu)."

ER_SEMISYNC_STOP_BINLOG_DUMP_TO_REPLICA
  eng "Stop %s binlog_dump to replica (server_id: %d)."

ER_SEMISYNC_UNREGISTER_TRX_OBSERVER_FAILED
  eng "unregister_trans_observer failed."

ER_SEMISYNC_UNREGISTER_BINLOG_STORAGE_OBSERVER_FAILED
  eng "unregister_binlog_storage_observer failed."

ER_SEMISYNC_UNREGISTER_BINLOG_TRANSMIT_OBSERVER_FAILED
  eng "unregister_binlog_transmit_observer failed."

ER_SEMISYNC_UNREGISTERED_REPLICATOR
  eng "unregister_replicator OK."

ER_SEMISYNC_SOCKET_FD_TOO_LARGE
  eng "Semisync replica socket fd is %u. select() cannot handle if the socket fd is bigger than %u (FD_SETSIZE)."

ER_SEMISYNC_REPLICA_REPLY
  eng "%s: reply - %d."

ER_SEMISYNC_MISSING_MAGIC_NO_FOR_SEMISYNC_PKT
  eng "Missing magic number for semi-sync packet, packet len: %lu."

ER_SEMISYNC_REPLICA_START
  eng "Replica I/O thread: Start %s replication to source '%s@%s:%d' in log '%s' at position %lu."

ER_SEMISYNC_REPLICA_REPLY_WITH_BINLOG_INFO
  eng "%s: reply (%s, %lu)."

ER_SEMISYNC_REPLICA_NET_FLUSH_REPLY_FAILED
  eng "Semi-sync replica net_flush() reply failed."

ER_SEMISYNC_REPLICA_SEND_REPLY_FAILED
  eng "Semi-sync replica send reply failed: %s (%d)."

ER_SEMISYNC_EXECUTION_FAILED_ON_SOURCE
  eng "Execution failed on source: %s; error %d"

ER_SEMISYNC_NOT_SUPPORTED_BY_SOURCE
  eng "Source server does not support semi-sync, fallback to asynchronous replication"

ER_SEMISYNC_REPLICA_SET_FAILED
  eng "Set 'rpl_semi_sync_replica=1' on source failed"

ER_SEMISYNC_FAILED_TO_STOP_ACK_RECEIVER_THD
  eng "Failed to stop ack receiver thread on my_thread_join, errno(%d)."

ER_FIREWALL_FAILED_TO_READ_FIREWALL_TABLES
  eng "Failed to read the firewall tables"

ER_FIREWALL_FAILED_TO_REG_DYNAMIC_PRIVILEGES
  eng "Failed to register dynamic privileges"

ER_FIREWALL_RECORDING_STMT_WAS_TRUNCATED
  eng "Statement was truncated and not recorded: %s"

ER_FIREWALL_RECORDING_STMT_WITHOUT_TEXT
  eng "Statement with no text was not recorded"

ER_FIREWALL_SUSPICIOUS_STMT
  eng "SUSPICIOUS STATEMENT from '%s'. Reason: %s Statement: %s"

ER_FIREWALL_ACCESS_DENIED
  eng "ACCESS DENIED for '%s'. Reason: %s Statement: %s"

ER_FIREWALL_SKIPPED_UNKNOWN_USER_MODE
  eng "Skipped unknown user mode '%s'"

ER_FIREWALL_RELOADING_CACHE
  eng "Reloading cache from disk"

ER_FIREWALL_RESET_FOR_USER
  eng "FIREWALL RESET for '%s'"

ER_FIREWALL_STATUS_FLUSHED
  eng "Counters are reset to zero"

ER_KEYRING_LOGGER_ERROR_MSG
  eng "%s"

ER_AUDIT_LOG_FILTER_IS_NOT_INSTALLED
  eng "Audit Log plugin supports filtering mode, which has not been installed yet. Audit Log plugin will run in the legacy mode, which will be removed in the next release."

ER_AUDIT_LOG_SWITCHING_TO_INCLUDE_LIST
  eng "Previously exclude list is used, now we start using include list, exclude list is set to NULL."

ER_AUDIT_LOG_CANNOT_SET_LOG_POLICY_WITH_OTHER_POLICIES
  eng "Cannot set audit_log_policy simultaneously with either audit_log_connection_policy or  audit_log_statement_policy, setting audit_log_connection_policy and audit_log_statement_policy based on audit_log_policy."

ER_AUDIT_LOG_ONLY_INCLUDE_LIST_USED
  eng "Both include and exclude lists provided, include list is preferred, exclude list is set to NULL."

ER_AUDIT_LOG_INDEX_MAP_CANNOT_ACCESS_DIR
  eng "Could not access '%s' directory."

ER_AUDIT_LOG_WRITER_RENAME_FILE_FAILED
  eng "Could not rename file from '%s' to '%s'."

ER_AUDIT_LOG_WRITER_DEST_FILE_ALREADY_EXISTS
  eng "File '%s' should not exist. It may be incomplete. The server crashed."

ER_AUDIT_LOG_WRITER_RENAME_FILE_FAILED_REMOVE_FILE_MANUALLY
  eng "Could not rename file from '%s' to '%s'. Remove the file manually."

ER_AUDIT_LOG_WRITER_INCOMPLETE_FILE_RENAMED
  eng "Incomplete file renamed from '%s' to '%s'."

ER_AUDIT_LOG_WRITER_FAILED_TO_WRITE_TO_FILE
  eng "Error writing file \'%-.200s\' (errno: %d - %s)."

ER_AUDIT_LOG_EC_WRITER_FAILED_TO_INIT_ENCRYPTION
  eng "Could not initialize audit log file encryption."

ER_AUDIT_LOG_EC_WRITER_FAILED_TO_INIT_COMPRESSION
  eng "Could not initialize audit log file compression."

ER_AUDIT_LOG_EC_WRITER_FAILED_TO_CREATE_FILE
  eng "Could not create '%s' file for audit logging."

ER_AUDIT_LOG_RENAME_LOG_FILE_BEFORE_FLUSH
  eng "Audit log file (%s) must be manually renamed before audit_log_flush is set to true."

ER_AUDIT_LOG_FILTER_RESULT_MSG
  eng "%s"

ER_AUDIT_LOG_JSON_READER_FAILED_TO_PARSE
  eng "Error parsing JSON event. Event not accessible."

ER_AUDIT_LOG_JSON_READER_BUF_TOO_SMALL
  eng "Buffer is too small to hold JSON event. Number of events skipped: %zu."

ER_AUDIT_LOG_JSON_READER_FAILED_TO_OPEN_FILE
  eng "Could not open JSON file for reading. Reading next file if exists."

ER_AUDIT_LOG_JSON_READER_FILE_PARSING_ERROR
  eng "JSON file parsing error. Reading next file if exists"

OBSOLETE_ER_AUDIT_LOG_FILTER_INVALID_COLUMN_COUNT
  eng "Invalid column count in the '%s.%s' table."

OBSOLETE_ER_AUDIT_LOG_FILTER_INVALID_COLUMN_DEFINITION
  eng "Invalid column definition of the '%s.%s' table."

ER_AUDIT_LOG_FILTER_FAILED_TO_STORE_TABLE_FLDS
  eng "Could not store field of the %s table."

ER_AUDIT_LOG_FILTER_FAILED_TO_UPDATE_TABLE
  eng "Could not update %s table."

ER_AUDIT_LOG_FILTER_FAILED_TO_INSERT_INTO_TABLE
  eng "Could not insert into %s table."

ER_AUDIT_LOG_FILTER_FAILED_TO_DELETE_FROM_TABLE
  eng "Could not delete from %s table."

ER_AUDIT_LOG_FILTER_FAILED_TO_INIT_TABLE_FOR_READ
  eng "Could not initialize %s table for reading."

ER_AUDIT_LOG_FILTER_FAILED_TO_READ_TABLE
  eng "Could not read %s table."

ER_AUDIT_LOG_FILTER_FAILED_TO_CLOSE_TABLE_AFTER_READING
  eng "Could not close %s table reading."

ER_AUDIT_LOG_FILTER_USER_AND_HOST_CANNOT_BE_EMPTY
  eng "Both user and host columns of %s table cannot be empty."

ER_AUDIT_LOG_FILTER_FLD_FILTERNAME_CANNOT_BE_EMPTY
  eng "Filtername column of %s table cannot be empty."

ER_VALIDATE_PWD_DICT_FILE_NOT_SPECIFIED
  eng "Dictionary file not specified"

ER_VALIDATE_PWD_DICT_FILE_NOT_LOADED
  eng "Dictionary file not loaded"

ER_VALIDATE_PWD_DICT_FILE_TOO_BIG
  eng "Dictionary file size exceeded MAX_DICTIONARY_FILE_LENGTH, not loaded"

ER_VALIDATE_PWD_FAILED_TO_READ_DICT_FILE
  eng "Exception while reading the dictionary file"

ER_VALIDATE_PWD_FAILED_TO_GET_FLD_FROM_SECURITY_CTX
  eng "Can't retrieve the %s from the security context"

ER_VALIDATE_PWD_FAILED_TO_GET_SECURITY_CTX
  eng "Can't retrieve the security context"

ER_VALIDATE_PWD_LENGTH_CHANGED
  eng "Effective value of validate_password_length is changed. New value is %d"

ER_REWRITER_QUERY_ERROR_MSG
  eng "%s"

ER_REWRITER_QUERY_FAILED
  eng "Rewritten query failed to parse:%s"

ER_XPLUGIN_STARTUP_FAILED
  eng "Startup failed with error \"%s\""

OBSOLETE_ER_XPLUGIN_SERVER_EXITING
  eng "Exiting"

OBSOLETE_ER_XPLUGIN_SERVER_EXITED
  eng "Exit done"

ER_XPLUGIN_USING_SSL_CONF_FROM_SERVER
  eng "Using SSL configuration from MySQL Server"

ER_XPLUGIN_USING_SSL_CONF_FROM_MYSQLX
  eng "Using SSL configuration from Mysqlx Plugin"

ER_XPLUGIN_FAILED_TO_USE_SSL_CONF
  eng "Neither MySQL Server nor Mysqlx Plugin has valid SSL configuration"

ER_XPLUGIN_USING_SSL_FOR_TLS_CONNECTION
  eng "Using %s for TLS connections"

ER_XPLUGIN_REFERENCE_TO_SECURE_CONN_WITH_XPLUGIN
  eng "For more information, please see the Using Secure Connections with X Plugin section in the MySQL documentation"

ER_XPLUGIN_ERROR_MSG
  eng "%s"

ER_SHA_PWD_FAILED_TO_PARSE_AUTH_STRING
  eng "Failed to parse stored authentication string for %s. Please check if mysql.user table not corrupted"

ER_SHA_PWD_FAILED_TO_GENERATE_MULTI_ROUND_HASH
  eng "Error in generating multi-round hash for %s. Plugin can not perform authentication without it. This may be a transient problem"

ER_SHA_PWD_AUTH_REQUIRES_RSA_OR_SSL
  eng "Authentication requires either RSA keys or SSL encryption"

ER_SHA_PWD_RSA_KEY_TOO_LONG
  eng "RSA key cipher length of %u is too long. Max value is %u"

ER_PLUGIN_COMMON_FAILED_TO_OPEN_FILTER_TABLES
  eng "Failed to open the %s filter tables"

ER_PLUGIN_COMMON_FAILED_TO_OPEN_TABLE
  eng "Failed to open '%s.%s' %s table"

ER_AUTH_LDAP_ERROR_LOGGER_ERROR_MSG
  eng "%s"

ER_CONN_CONTROL_ERROR_MSG
  eng "%s"

ER_GRP_RPL_ERROR_MSG
  eng "%s"

ER_SHA_PWD_SALT_FOR_USER_CORRUPT
  eng "Password salt for user '%s' is corrupt"

ER_SYS_VAR_COMPONENT_OOM
  eng "Out of memory for component system variable '%s'."

ER_SYS_VAR_COMPONENT_VARIABLE_SET_READ_ONLY
  eng "variable %s of component %s was forced to be read-only: string variable without update_func and PLUGIN_VAR_MEMALLOC flag."

ER_SYS_VAR_COMPONENT_UNKNOWN_VARIABLE_TYPE
  eng "Unknown variable type code 0x%x in component '%s'."

ER_SYS_VAR_COMPONENT_FAILED_TO_PARSE_VARIABLE_OPTIONS
  eng "Parsing options for variable '%s' failed."

ER_SYS_VAR_COMPONENT_FAILED_TO_MAKE_VARIABLE_PERSISTENT
  eng "Setting persistent options for component variable '%s' failed."

ER_COMPONENT_FILTER_CONFUSED
  eng "The log-filter component \"%s\" got confused at \"%s\" (state: %s) ..."

ER_STOP_REPLICA_IO_THREAD_DISK_SPACE
  eng "Waiting until I/O thread for channel '%s' finish writing to disk before stopping. Free some disk space or use 'KILL' to abort I/O thread operation. Notice that aborting the I/O thread while rotating the relay log might corrupt the relay logs, requiring a server restart to fix it."

ER_LOG_FILE_CANNOT_OPEN
  eng "Could not use %s for logging (error %d - %s). Turning logging off for the server process. To turn it on again: fix the cause, then%s restart the MySQL server."

OBSOLETE_ER_UNABLE_TO_COLLECT_LOG_STATUS
  eng "Unable to collect information for column '%-.192s': %-.192s."

OBSOLETE_ER_DEPRECATED_UTF8_ALIAS
  eng "'utf8' is currently an alias for the character set UTF8MB3, which will be replaced by UTF8MB4 in a future release. Please consider using UTF8MB4 in order to be unambiguous."

OBSOLETE_ER_DEPRECATED_NATIONAL
  eng "NATIONAL/NCHAR/NVARCHAR implies the character set UTF8MB3, which will be replaced by UTF8MB4 in a future release. Please consider using CHAR(x) CHARACTER SET UTF8MB4 in order to be unambiguous."

OBSOLETE_ER_SLAVE_POSSIBLY_DIVERGED_AFTER_DDL
  eng "A commit for an atomic DDL statement was unsuccessful on the source and the replica. The replica supports atomic DDL statements but the source does not, so the action taken by the replica and source might differ. Check that their states have not diverged before proceeding."

ER_PERSIST_OPTION_STATUS
  eng "Configuring persisted options failed: \"%s\"."

ER_NOT_IMPLEMENTED_GET_TABLESPACE_STATISTICS
  eng "The storage engine '%-.192s' does not provide dynamic table statistics"

OBSOLETE_ER_UNABLE_TO_SET_OPTION
  eng "This option cannot be set %s."

OBSOLETE_ER_RESERVED_TABLESPACE_NAME
  eng "The table '%-.192s' may not be created in the reserved tablespace '%-.192s'."

ER_SSL_FIPS_MODE_ERROR
   eng "SSL fips mode error: %s"

ER_CONN_INIT_CONNECT_IGNORED
  eng "init_connect variable is ignored for user: %s host: %s due to expired password."

OBSOLETE_ER_UNSUPPORTED_SQL_MODE
  eng "sql_mode=0x%08x is not supported"

ER_REWRITER_OOM
  eng "Out of memory."

ER_REWRITER_TABLE_MALFORMED_ERROR
  eng "Wrong column count or names when loading rules."

ER_REWRITER_LOAD_FAILED
  eng "Some rules failed to load."

ER_REWRITER_READ_FAILED
  eng "Got error from storage engine while refreshing rewrite rules."

ER_CONN_CONTROL_EVENT_COORDINATOR_INIT_FAILED
  eng "Failed to initialize Connection_event_coordinator"

ER_CONN_CONTROL_STAT_CONN_DELAY_TRIGGERED_UPDATE_FAILED
  eng "Failed to update connection delay triggered stats"

ER_CONN_CONTROL_STAT_CONN_DELAY_TRIGGERED_RESET_FAILED
  eng "Failed to reset connection delay triggered stats"

ER_CONN_CONTROL_INVALID_CONN_DELAY_TYPE
  eng "Unexpected option type for connection delay."

ER_CONN_CONTROL_DELAY_ACTION_INIT_FAILED
  eng "Failed to initialize Connection_delay_action"

ER_CONN_CONTROL_FAILED_TO_SET_CONN_DELAY
  eng "Could not set %s delay for connection delay."

ER_CONN_CONTROL_FAILED_TO_UPDATE_CONN_DELAY_HASH
  eng "Failed to update connection delay hash for account : %s"

ER_XPLUGIN_FORCE_STOP_CLIENT
  eng "%s: Force stopping client because exception occurred: %s"

ER_XPLUGIN_MAX_AUTH_ATTEMPTS_REACHED
  eng "%s.%u: Maximum number of authentication attempts reached, login failed."

ER_XPLUGIN_BUFFER_PAGE_ALLOC_FAILED
  eng "Error allocating Buffer_page: %s"

ER_XPLUGIN_DETECTED_HANGING_CLIENTS
  eng "Detected %u hanging client(s)"

ER_XPLUGIN_FAILED_TO_ACCEPT_CLIENT
  eng "Error accepting client"

ER_XPLUGIN_FAILED_TO_SCHEDULE_CLIENT
  eng "Internal error scheduling client for execution"

ER_XPLUGIN_FAILED_TO_PREPARE_IO_INTERFACES
  eng "Preparation of I/O interfaces failed, X Protocol won't be accessible"

ER_XPLUGIN_SRV_SESSION_INIT_THREAD_FAILED
  eng "srv_session_init_thread returned error"

ER_XPLUGIN_UNABLE_TO_USE_USER_SESSION_ACCOUNT
  eng "Unable to use user mysql.session account when connecting the server for internal plugin requests."

ER_XPLUGIN_REFERENCE_TO_USER_ACCOUNT_DOC_SECTION
  eng "For more information, please see the X Plugin User Account section in the MySQL documentation"

ER_XPLUGIN_UNEXPECTED_EXCEPTION_DISPATCHING_CMD
  eng "%s: Unexpected exception dispatching command: %s"

ER_XPLUGIN_EXCEPTION_IN_TASK_SCHEDULER
  eng "Exception in post: %s"

ER_XPLUGIN_TASK_SCHEDULING_FAILED
  eng "Internal error scheduling task"

ER_XPLUGIN_EXCEPTION_IN_EVENT_LOOP
  eng "Exception in event loop: \"%s\": %s"

ER_XPLUGIN_LISTENER_SETUP_FAILED
  eng "Setup of %s failed, %s"

ER_XPLUING_NET_STARTUP_FAILED
  eng "%s"

ER_XPLUGIN_FAILED_AT_SSL_CONF
  eng "Failed at SSL configuration: \"%s\""

OBSOLETE_ER_XPLUGIN_CLIENT_SSL_HANDSHAKE_FAILED
  eng "Error during SSL handshake for client connection (%i)"

OBSOLETE_ER_XPLUGIN_SSL_HANDSHAKE_WITH_SERVER_FAILED
  eng "%s: Error during SSL handshake"

ER_XPLUGIN_FAILED_TO_CREATE_SESSION_FOR_CONN
  eng "%s: Error creating session for connection from %s"

ER_XPLUGIN_FAILED_TO_INITIALIZE_SESSION
  eng "%s: Error initializing session for connection: %s"

ER_XPLUGIN_MESSAGE_TOO_LONG
  eng "%s: Message of size %u received, exceeding the limit of %i"

ER_XPLUGIN_UNINITIALIZED_MESSAGE
  eng "Message is not properly initialized: %s"

ER_XPLUGIN_FAILED_TO_SET_MIN_NUMBER_OF_WORKERS
  eng "Unable to set minimal number of workers to %u; actual value is %i"

ER_XPLUGIN_UNABLE_TO_ACCEPT_CONNECTION
  eng "Unable to accept connection, disconnecting client"

ER_XPLUGIN_ALL_IO_INTERFACES_DISABLED
  eng "All I/O interfaces are disabled, X Protocol won't be accessible"

OBSOLETE_ER_XPLUGIN_INVALID_MSG_DURING_CLIENT_INIT
  eng "%s: Invalid message %i received during client initialization"

OBSOLETE_ER_XPLUGIN_CLOSING_CLIENTS_ON_SHUTDOWN
  eng "%s: closing client because of shutdown (state: %i)"

ER_XPLUGIN_ERROR_READING_SOCKET
  eng "%s: Error reading from socket %s (%i)"

ER_XPLUGIN_PEER_DISCONNECTED_WHILE_READING_MSG_BODY
  eng "%s: peer disconnected while reading message body"

ER_XPLUGIN_READ_FAILED_CLOSING_CONNECTION
  eng "client_id:%s - %s while reading from socket, closing connection"

OBSOLETE_ER_XPLUGIN_INVALID_AUTH_METHOD
  eng "%s.%u: Invalid authentication method %s"

OBSOLETE_ER_XPLUGIN_UNEXPECTED_MSG_DURING_AUTHENTICATION
  eng "%s: Unexpected message of type %i received during authentication"

OBSOLETE_ER_XPLUGIN_ERROR_WRITING_TO_CLIENT
  eng "Error writing to client: %s (%i)"

OBSOLETE_ER_XPLUGIN_SCHEDULER_STARTED
  eng "Scheduler \"%s\" started."

OBSOLETE_ER_XPLUGIN_SCHEDULER_STOPPED
  eng "Scheduler \"%s\" stopped."

ER_XPLUGIN_LISTENER_SYS_VARIABLE_ERROR
  eng "Please see the MySQL documentation for '%s' system variables to fix the error"

ER_XPLUGIN_LISTENER_STATUS_MSG
  eng "X Plugin ready for connections. %s"

ER_XPLUGIN_RETRYING_BIND_ON_PORT
  eng "Retrying `bind()` on TCP/IP port %i"

OBSOLETE_ER_XPLUGIN_SHUTDOWN_TRIGGERED
  eng "Shutdown triggered by mysqld abort flag"

OBSOLETE_ER_XPLUGIN_USER_ACCOUNT_WITH_ALL_PERMISSIONS
  eng "Using %s account for authentication which has all required permissions"

ER_XPLUGIN_EXISTING_USER_ACCOUNT_WITH_INCOMPLETE_GRANTS
  eng "Using existing %s account for authentication. Incomplete grants will be fixed"

OBSOLETE_ER_XPLUGIN_SERVER_STARTS_HANDLING_CONNECTIONS
  eng "Server starts handling incoming connections"

OBSOLETE_ER_XPLUGIN_SERVER_STOPPED_HANDLING_CONNECTIONS
  eng "Stopped handling incoming connections"

OBSOLETE_ER_XPLUGIN_FAILED_TO_INTERRUPT_SESSION
  eng "%s: Could not interrupt client session"

OBSOLETE_ER_XPLUGIN_CLIENT_RELEASE_TRIGGERED
  eng "%s: release triggered by timeout in state:%i"

ER_XPLUGIN_IPv6_AVAILABLE
  eng "IPv6 is available"

OBSOLETE_ER_XPLUGIN_UNIX_SOCKET_NOT_CONFIGURED
  eng "UNIX socket not configured"

ER_XPLUGIN_CLIENT_KILL_MSG
  eng "Kill client: %i %s"

ER_XPLUGIN_FAILED_TO_GET_SECURITY_CTX
  eng "Could not get security context for session"

OBSOLETE_ER_XPLUGIN_FAILED_TO_SWITCH_SECURITY_CTX_TO_ROOT
  eng "Unable to switch security context to root"

ER_XPLUGIN_FAILED_TO_CLOSE_SQL_SESSION
  eng "Error closing SQL session"

ER_XPLUGIN_FAILED_TO_EXECUTE_ADMIN_CMD
  eng "Error executing admin command %s: %s"

ER_XPLUGIN_EMPTY_ADMIN_CMD
  eng "Error executing empty admin command"

ER_XPLUGIN_FAILED_TO_GET_SYS_VAR
  eng "Unable to retrieve system variable \'%s\'"

ER_XPLUGIN_FAILED_TO_GET_CREATION_STMT
  eng "Unable to get creation stmt for collection \'%s\'; query result size: %lu"

ER_XPLUGIN_FAILED_TO_GET_ENGINE_INFO
  eng "Unable to get engine info for collection \'%s\'; creation stmt: %s"

OBSOLETE_ER_XPLUGIN_FAIL_TO_GET_RESULT_DATA
  eng "Error getting result data: %s"

OBSOLETE_ER_XPLUGIN_CAPABILITY_EXPIRED_PASSWORD
  eng "Capability expired password failed with error: %s"

ER_XPLUGIN_FAILED_TO_SET_SO_REUSEADDR_FLAG
  eng "Failed to set SO_REUSEADDR flag (error: %d)."

ER_XPLUGIN_FAILED_TO_OPEN_INTERNAL_SESSION
  eng "Could not open internal MySQL session"

ER_XPLUGIN_FAILED_TO_SWITCH_CONTEXT
  eng "Unable to switch context to user %s"

ER_XPLUGIN_FAILED_TO_UNREGISTER_UDF
  eng "Can\'t unregister \'%s\' user defined function"

OBSOLETE_ER_XPLUGIN_GET_PEER_ADDRESS_FAILED
  eng "%s: get peer address failed, can't resolve IP to hostname"

OBSOLETE_ER_XPLUGIN_CAPABILITY_CLIENT_INTERACTIVE_FAILED
  eng "Capability client interactive failed with error: %s"

ER_XPLUGIN_FAILED_TO_RESET_IPV6_V6ONLY_FLAG
  eng "Failed to reset IPV6_V6ONLY flag (error: %d). The server will listen to IPv6 addresses only."

ER_KEYRING_INVALID_KEY_TYPE
  eng "Invalid key type"

ER_KEYRING_INVALID_KEY_LENGTH
  eng "Invalid key length for given block cipher"

ER_KEYRING_FAILED_TO_CREATE_KEYRING_DIR
  eng "Could not create keyring directory. The keyring_file will stay unusable until correct path to the keyring directory gets provided"

ER_KEYRING_FILE_INIT_FAILED
  eng "keyring_file initialization failure. Please check if the keyring_file_data points to readable keyring file or keyring file can be created in the specified location. The keyring_file will stay unusable until correct path to the keyring file gets provided"

ER_KEYRING_INTERNAL_EXCEPTION_FAILED_FILE_INIT
  eng "keyring_file initialization failure due to internal exception inside the plugin."

ER_KEYRING_FAILED_TO_GENERATE_KEY
  eng "Failed to generate a key due to internal exception inside keyring_file plugin"

ER_KEYRING_CHECK_KEY_FAILED_DUE_TO_INVALID_KEY
  eng "Error while %s key: invalid key_type"

ER_KEYRING_CHECK_KEY_FAILED_DUE_TO_EMPTY_KEY_ID
  eng "Error while %s key: key_id cannot be empty"

ER_KEYRING_OPERATION_FAILED_DUE_TO_INTERNAL_ERROR
  eng "Failed to %s due to internal exception inside %s plugin"

ER_KEYRING_INCORRECT_FILE
  eng "Incorrect Keyring file"

ER_KEYRING_FOUND_MALFORMED_BACKUP_FILE
  eng "Found malformed keyring backup file - removing it"

ER_KEYRING_FAILED_TO_RESTORE_FROM_BACKUP_FILE
  eng "Error while restoring keyring from backup file cannot overwrite keyring with backup"

ER_KEYRING_FAILED_TO_FLUSH_KEYRING_TO_FILE
  eng "Error while flushing in-memory keyring into keyring file"

ER_KEYRING_FAILED_TO_GET_FILE_STAT
  eng "Error while reading stat for %s.Please check if file %s was not removed. OS returned this error: %s"

ER_KEYRING_FAILED_TO_REMOVE_FILE
  eng "Could not remove file %s OS retuned this error: %s"

ER_KEYRING_FAILED_TO_TRUNCATE_FILE
  eng "Could not truncate file %s. OS retuned this error: %s"

ER_KEYRING_UNKNOWN_ERROR
  eng "Unknown error %d"

ER_KEYRING_FAILED_TO_SET_KEYRING_FILE_DATA
  eng "keyring_file_data cannot be set to new value as the keyring file cannot be created/accessed in the provided path"

ER_KEYRING_FILE_IO_ERROR
  eng "%s"

ER_KEYRING_FAILED_TO_LOAD_KEYRING_CONTENT
  eng "Error while loading keyring content. The keyring might be malformed"

ER_KEYRING_FAILED_TO_FLUSH_KEYS_TO_KEYRING
  eng "Could not flush keys to keyring"

ER_KEYRING_FAILED_TO_FLUSH_KEYS_TO_KEYRING_BACKUP
  eng "Could not flush keys to keyring's backup"

ER_KEYRING_KEY_FETCH_FAILED_DUE_TO_EMPTY_KEY_ID
  eng "Error while fetching key: key_id cannot be empty"

ER_KEYRING_FAILED_TO_REMOVE_KEY_DUE_TO_EMPTY_ID
  eng "Error while removing key: key_id cannot be empty"

ER_KEYRING_OKV_INCORRECT_KEY_VAULT_CONFIGURED
  eng "For keyring_okv to be initialized, please point keyring_okv_conf_dir variable to a directory with Oracle Key Vault configuration file and ssl materials"

ER_KEYRING_OKV_INIT_FAILED_DUE_TO_INCORRECT_CONF
  eng "keyring_okv initialization failure. Please check that the keyring_okv_conf_dir points to a readable directory and that the directory contains Oracle Key Vault configuration file and ssl materials. Please also check that Oracle Key Vault is up and running."

ER_KEYRING_OKV_INIT_FAILED_DUE_TO_INTERNAL_ERROR
  eng "keyring_okv initialization failure due to internal exception inside the plugin"

ER_KEYRING_OKV_INVALID_KEY_TYPE
  eng "Invalid key type"

ER_KEYRING_OKV_INVALID_KEY_LENGTH_FOR_CIPHER
  eng "Invalid key length for given block cipher"

ER_KEYRING_OKV_FAILED_TO_GENERATE_KEY_DUE_TO_INTERNAL_ERROR
  eng "Failed to generate a key due to internal exception inside keyring_okv plugin"

ER_KEYRING_OKV_FAILED_TO_FIND_SERVER_ENTRY
  eng "Could not find entry for server in configuration file %s"

ER_KEYRING_OKV_FAILED_TO_FIND_STANDBY_SERVER_ENTRY
  eng "Could not find entry for standby server in configuration file %s"

ER_KEYRING_OKV_FAILED_TO_PARSE_CONF_FILE
  eng "Could not parse the %s file provided"

ER_KEYRING_OKV_FAILED_TO_LOAD_KEY_UID
  eng "Could not load keys' uids from the OKV server"

ER_KEYRING_OKV_FAILED_TO_INIT_SSL_LAYER
  eng "Could not initialize ssl layer"

ER_KEYRING_OKV_FAILED_TO_INIT_CLIENT
  eng "Could not initialize OKV client"

ER_KEYRING_OKV_CONNECTION_TO_SERVER_FAILED
  eng "Could not connect to the OKV server"

ER_KEYRING_OKV_FAILED_TO_REMOVE_KEY
  eng "Could not remove the key"

ER_KEYRING_OKV_FAILED_TO_ADD_ATTRIBUTE
  eng "Could not add attribute, attribute_name=%s attribute value=%s"

ER_KEYRING_OKV_FAILED_TO_GENERATE_KEY
  eng "Could not generate the key."

ER_KEYRING_OKV_FAILED_TO_STORE_KEY
  eng "Could not store the key."

ER_KEYRING_OKV_FAILED_TO_ACTIVATE_KEYS
  eng "Could not activate the key."

ER_KEYRING_OKV_FAILED_TO_FETCH_KEY
  eng "Could not fetch generated key"

ER_KEYRING_OKV_FAILED_TO_STORE_OR_GENERATE_KEY
  eng "Could not store/generate the key - failed to set key attribute x-key-ready"

ER_KEYRING_OKV_FAILED_TO_RETRIEVE_KEY_SIGNATURE
  eng "Could not retrieve key signature from custom attributes"

ER_KEYRING_OKV_FAILED_TO_RETRIEVE_KEY
  eng "Could not retrieve key from OKV"

ER_KEYRING_OKV_FAILED_TO_LOAD_SSL_TRUST_STORE
  eng "Error loading trust store"

ER_KEYRING_OKV_FAILED_TO_SET_CERTIFICATE_FILE
  eng "Error setting the certificate file."

ER_KEYRING_OKV_FAILED_TO_SET_KEY_FILE
  eng "Error setting the key file."

ER_KEYRING_OKV_KEY_MISMATCH
  eng "Private key does not match the certificate public key"

ER_KEYRING_ENCRYPTED_FILE_INCORRECT_KEYRING_FILE
  eng "Incorrect Keyring file"

ER_KEYRING_ENCRYPTED_FILE_DECRYPTION_FAILED
  eng "Keyring_encrypted_file decryption failed. Please verify --keyring-encrypted-file-password option value."

ER_KEYRING_ENCRYPTED_FILE_FOUND_MALFORMED_BACKUP_FILE
  eng "Found malformed keyring backup file - removing it"

ER_KEYRING_ENCRYPTED_FILE_FAILED_TO_RESTORE_KEYRING
  eng "Error while restoring keyring from backup file cannot overwrite keyring with backup"

ER_KEYRING_ENCRYPTED_FILE_FAILED_TO_FLUSH_KEYRING
  eng "Error while flushing in-memory keyring into keyring file"

ER_KEYRING_ENCRYPTED_FILE_ENCRYPTION_FAILED
  eng "Keyring_encrypted_file encryption failed. Please verify --keyring-encrypted-file-password option value."

ER_KEYRING_ENCRYPTED_FILE_INVALID_KEYRING_DIR
  eng "keyring_encrypted_file_data cannot be set to new value as the keyring file cannot be created/accessed in the provided path"

ER_KEYRING_ENCRYPTED_FILE_FAILED_TO_CREATE_KEYRING_DIR
  eng "Could not create keyring directory The keyring_encrypted_file will stay unusable until correct path to the keyring directory gets provided"

ER_KEYRING_ENCRYPTED_FILE_PASSWORD_IS_INVALID
  eng "The keyring_encrypted_file_password must be set to a valid value."

ER_KEYRING_ENCRYPTED_FILE_PASSWORD_IS_TOO_LONG
  eng "Too long keyring_encrypted_file_password value."

ER_KEYRING_ENCRYPTED_FILE_INIT_FAILURE
  eng "keyring_encrypted_file initialization failure. Please check if the keyring_encrypted_file_data points to readable keyring file or keyring file can be created in the specified location or password to decrypt keyring file is correct."

ER_KEYRING_ENCRYPTED_FILE_INIT_FAILED_DUE_TO_INTERNAL_ERROR
  eng "keyring_encrypted_file initialization failure due to internal exception inside the plugin"

ER_KEYRING_ENCRYPTED_FILE_GEN_KEY_FAILED_DUE_TO_INTERNAL_ERROR
  eng "Failed to generate a key due to internal exception inside keyring_encrypted_file plugin"

ER_KEYRING_AWS_FAILED_TO_SET_CMK_ID
  eng "keyring_aws_cmk_id cannot be set to the new value as AWS KMS seems to not understand the id provided. Please check that CMK id provided is correct."

ER_KEYRING_AWS_FAILED_TO_SET_REGION
  eng "keyring_aws_region cannot be set to the new value as AWS KMS seems to not understand the region provided. Please check that region provided is correct."

ER_KEYRING_AWS_FAILED_TO_OPEN_CONF_FILE
  eng "Could not open keyring_aws configuration file: %s. OS returned this error: %s"

ER_KEYRING_AWS_FAILED_TO_ACCESS_KEY_ID_FROM_CONF_FILE
  eng "Could not read AWS access key id from keyring_aws configuration file: %s. OS returned this error: %s"

ER_KEYRING_AWS_FAILED_TO_ACCESS_KEY_FROM_CONF_FILE
  eng "Could not read AWS access key from keyring_aws configuration file: %s. OS returned this error: %s"

ER_KEYRING_AWS_INVALID_CONF_FILE_PATH
  eng "Path to keyring aws configuration file cannot be empty"

ER_KEYRING_AWS_INVALID_DATA_FILE_PATH
  eng "Path to keyring_aws storage file cannot be empty."

ER_KEYRING_AWS_FAILED_TO_ACCESS_OR_CREATE_KEYRING_DIR
  eng "Unable to create/access keyring directory."

ER_KEYRING_AWS_FAILED_TO_ACCESS_OR_CREATE_KEYRING_DATA_FILE
  eng "Unable to create/access keyring_aws storage file. Please check if keyring_aws_data_file points to location where keyring file can be created/accessed. Please also make sure that MySQL server's user has high enough privileges to access this location."

ER_KEYRING_AWS_FAILED_TO_INIT_DUE_TO_INTERNAL_ERROR
  eng "keyring_aws initialization failed due to internal error when initializing synchronization primitive - %s. OS returned this error: %s:"

ER_KEYRING_AWS_FAILED_TO_ACCESS_DATA_FILE
  eng "Could not access keyring_aws storage file in the path provided. Please check if the keyring_aws directory can be accessed by MySQL Server"

ER_KEYRING_AWS_CMK_ID_NOT_SET
  eng "keyring_aws_cmk_id has to be set"

ER_KEYRING_AWS_FAILED_TO_GET_KMS_CREDENTIAL_FROM_CONF_FILE
  eng "Could not get AWS KMS credentials from the configuration file"

ER_KEYRING_AWS_INIT_FAILURE
  eng "keyring_aws initialization failure."

ER_KEYRING_AWS_FAILED_TO_INIT_DUE_TO_PLUGIN_INTERNAL_ERROR
  eng "keyring_aws initialization failure due to internal exception inside the plugin"

ER_KEYRING_AWS_INVALID_KEY_LENGTH_FOR_CIPHER
  eng "Invalid key length for given block cipher"

ER_KEYRING_AWS_FAILED_TO_GENERATE_KEY_DUE_TO_INTERNAL_ERROR
  eng "Failed to generate a key due to internal exception inside keyring_file plugin"

ER_KEYRING_AWS_INCORRECT_FILE
  eng "Incorrect Keyring file"

ER_KEYRING_AWS_FOUND_MALFORMED_BACKUP_FILE
  eng "Found malformed keyring backup file - removing it"

ER_KEYRING_AWS_FAILED_TO_RESTORE_FROM_BACKUP_FILE
  eng "Error while restoring keyring from backup file cannot overwrite keyring with backup"

ER_KEYRING_AWS_FAILED_TO_FLUSH_KEYRING_TO_FILE
  eng "Error while flushing in-memory keyring into keyring file"

ER_KEYRING_AWS_INCORRECT_REGION
  eng "Wrong region"

ER_KEYRING_AWS_FAILED_TO_CONNECT_KMS
  eng "Could not connect to AWS KMS with the credentials provided. Please make sure they are correct. AWS KMS returned this error: %s"

ER_KEYRING_AWS_FAILED_TO_GENERATE_NEW_KEY
  eng "Could not generate a new key. AWS KMS returned this error: %s"

ER_KEYRING_AWS_FAILED_TO_ENCRYPT_KEY
  eng "Could not encrypt key. AWS KMS returned this error: %s"

ER_KEYRING_AWS_FAILED_TO_RE_ENCRYPT_KEY
  eng "Could not re-encrypt key. AWS KMS returned this error: %s"

ER_KEYRING_AWS_FAILED_TO_DECRYPT_KEY
  eng "Could not decrypt key. AWS KMS returned this error: %s"

ER_KEYRING_AWS_FAILED_TO_ROTATE_CMK
  eng "Could not rotate the CMK. AWS KMS returned this error: %s"

ER_GRP_RPL_GTID_ALREADY_USED
  eng "The requested GTID '%s:%lld' was already used, the transaction will rollback."

ER_GRP_RPL_APPLIER_THD_KILLED
  eng "The group replication applier thread was killed."

ER_GRP_RPL_EVENT_HANDLING_ERROR
  eng "Error at event handling! Got error: %d."

ER_GRP_RPL_ERROR_GTID_EXECUTION_INFO
  eng "Error when extracting group GTID execution information, some recovery operations may face future issues."

ER_GRP_RPL_CERTIFICATE_SIZE_ERROR
  eng "An error occurred when trying to reduce the Certification information size for transmission."

ER_GRP_RPL_CREATE_APPLIER_CACHE_ERROR
  eng "Failed to create group replication pipeline applier cache!"

ER_GRP_RPL_UNBLOCK_WAITING_THD
  eng "Unblocking the group replication thread waiting for applier to start, as the start group replication was killed."

ER_GRP_RPL_APPLIER_PIPELINE_NOT_DISPOSED
  eng "The group replication applier pipeline was not properly disposed. Check the error log for further info."

ER_GRP_RPL_APPLIER_THD_EXECUTION_ABORTED
  eng "The applier thread execution was aborted. Unable to process more transactions, this member will now leave the group."

ER_GRP_RPL_APPLIER_EXECUTION_FATAL_ERROR
  eng "Fatal error during execution on the Applier process of Group Replication. The server will now leave the group."

ER_GRP_RPL_ERROR_STOPPING_CHANNELS
  eng "Error stopping all replication channels while server was leaving the group. %s"

ER_GRP_RPL_ERROR_SENDING_SINGLE_PRIMARY_MSSG
  eng "Error sending single primary message informing that primary did apply relay logs."

ER_GRP_RPL_UPDATE_TRANS_SNAPSHOT_VER_ERROR
  eng "Error updating transaction snapshot version after transaction being positively certified."

ER_GRP_RPL_SIDNO_FETCH_ERROR
  eng "Error fetching transaction sidno after transaction being positively certified."

ER_GRP_RPL_BROADCAST_COMMIT_TRANS_MSSG_FAILED
  eng "Broadcast of committed transactions message failed."

ER_GRP_RPL_GROUP_NAME_PARSE_ERROR
  eng "Unable to parse the group_replication_group_name during the Certification module initialization."

ER_GRP_RPL_ADD_GRPSID_TO_GRPGTIDSID_MAP_ERROR
  eng "Unable to add the group_sid in the group_gtid_sid_map during the Certification module initialization."

ER_GRP_RPL_UPDATE_GRPGTID_EXECUTED_ERROR
  eng "Error updating group_gtid_executed GITD set during the Certification module initialization."

ER_GRP_RPL_DONOR_TRANS_INFO_ERROR
  eng "Unable to handle the donor's transaction information when initializing the conflict detection component. Possible out of memory error."

ER_GRP_RPL_SERVER_CONN_ERROR
  eng "Error when establishing a server connection during the Certification module initialization."

ER_GRP_RPL_ERROR_FETCHING_GTID_EXECUTED_SET
  eng "Error when extracting this member GTID executed set. Certification module can't be properly initialized."

ER_GRP_RPL_ADD_GTID_TO_GRPGTID_EXECUTED_ERROR
  eng "Error while adding the server GTID EXECUTED set to the group_gtid_execute during the Certification module initialization."

ER_GRP_RPL_ERROR_FETCHING_GTID_SET
  eng "Error when extracting this member retrieved set for its applier. Certification module can't be properly initialized."

ER_GRP_RPL_ADD_RETRIEVED_SET_TO_GRP_GTID_EXECUTED_ERROR
  eng "Error while adding the member retrieved set to the group_gtid_executed during the Certification module initialization."

ER_GRP_RPL_CERTIFICATION_INITIALIZATION_FAILURE
  eng "Error during Certification module initialization."

ER_GRP_RPL_UPDATE_LAST_CONFLICT_FREE_TRANS_ERROR
  eng "Unable to update last conflict free transaction, this transaction will not be tracked on performance_schema.replication_group_member_stats.last_conflict_free_transaction."

ER_GRP_RPL_UPDATE_TRANS_SNAPSHOT_REF_VER_ERROR
  eng "Error updating transaction snapshot version reference for internal storage."

ER_GRP_RPL_FETCH_TRANS_SIDNO_ERROR
  eng "Error fetching transaction sidno while adding to the group_gtid_executed set."

ER_GRP_RPL_ERROR_VERIFYING_SIDNO
  eng "Error while ensuring the sidno be present in the group_gtid_executed."

ER_GRP_RPL_CANT_GENERATE_GTID
  eng "Impossible to generate Global Transaction Identifier: the integer component reached the maximal value. Restart the group with a new group_replication_group_name."

ER_GRP_RPL_INVALID_GTID_SET
  eng "Invalid stable transactions set."

ER_GRP_RPL_UPDATE_GTID_SET_ERROR
  eng "Error updating stable transactions set."

ER_GRP_RPL_RECEIVED_SET_MISSING_GTIDS
  eng "There was an error when filling the missing GTIDs on the applier channel received set. Despite not critical, on the long run this may cause performance issues."

OBSOLETE_ER_GRP_RPL_SKIP_COMPUTATION_TRANS_COMMITTED
  eng "Skipping the computation of the Transactions_committed_all_members field as an older instance of this computation is still ongoing."

ER_GRP_RPL_NULL_PACKET
  eng "Null packet on certifier's queue."

ER_GRP_RPL_CANT_READ_GTID
  eng "Error reading GTIDs from the message."

ER_GRP_RPL_PROCESS_GTID_SET_ERROR
  eng "Error processing stable transactions set."

ER_GRP_RPL_PROCESS_INTERSECTION_GTID_SET_ERROR
  eng "Error processing intersection of stable transactions set."

ER_GRP_RPL_SET_STABLE_TRANS_ERROR
  eng "Error setting stable transactions set."

ER_GRP_RPL_CANT_READ_GRP_GTID_EXTRACTED
  eng "Error reading group_gtid_extracted from the View_change_log_event."

ER_GRP_RPL_CANT_READ_WRITE_SET_ITEM
  eng "Error reading the write set item '%s' from the View_change_log_event."

ER_GRP_RPL_INIT_CERTIFICATION_INFO_FAILURE
  eng "Error during certification_info initialization."

ER_GRP_RPL_CONFLICT_DETECTION_DISABLED
  eng "Primary had applied all relay logs, disabled conflict detection."

ER_GRP_RPL_MSG_DISCARDED
  eng "Message received while the plugin is not ready, message discarded."

ER_GRP_RPL_MISSING_GRP_RPL_APPLIER
  eng "Message received without a proper group replication applier."

ER_GRP_RPL_CERTIFIER_MSSG_PROCESS_ERROR
  eng "Error processing message in Certifier."

ER_GRP_RPL_SRV_NOT_ONLINE
  eng "This server was not declared online since it is on status %s."

ER_GRP_RPL_SRV_ONLINE
  eng "This server was declared online within the replication group."

ER_GRP_RPL_DISABLE_SRV_READ_MODE_RESTRICTED
  eng "When declaring the plugin online it was not possible to disable the server read mode settings. Try to disable it manually."

ER_GRP_RPL_MEM_ONLINE
  eng "The member with address %s:%u was declared online within the replication group."

ER_GRP_RPL_MEM_UNREACHABLE
  eng "Member with address %s:%u has become unreachable."

ER_GRP_RPL_MEM_REACHABLE
  eng "Member with address %s:%u is reachable again."

ER_GRP_RPL_SRV_BLOCKED
  eng "This server is not able to reach a majority of members in the group. This server will now block all updates. The server will remain blocked until contact with the majority is restored. It is possible to use group_replication_force_members to force a new group membership."

ER_GRP_RPL_SRV_BLOCKED_FOR_SECS
  eng "This server is not able to reach a majority of members in the group. This server will now block all updates. The server will remain blocked for the next %lu seconds. Unless contact with the majority is restored, after this time the member will error out and leave the group. It is possible to use group_replication_force_members to force a new group membership."

ER_GRP_RPL_CHANGE_GRP_MEM_NOT_PROCESSED
  eng "A group membership change was received but the plugin is already leaving due to the configured timeout on group_replication_unreachable_majority_timeout option."

ER_GRP_RPL_MEMBER_CONTACT_RESTORED
  eng "The member has resumed contact with a majority of the members in the group. Regular operation is restored and transactions are unblocked."

ER_GRP_RPL_MEMBER_REMOVED
  eng "Members removed from the group: %s"

ER_GRP_RPL_PRIMARY_MEMBER_LEFT_GRP
  eng "Primary server with address %s left the group. Electing new Primary."

ER_GRP_RPL_MEMBER_ADDED
  eng "Members joined the group: %s"

ER_GRP_RPL_MEMBER_EXIT_PLUGIN_ERROR
  eng "There was a previous plugin error while the member joined the group. The member will now exit the group."

ER_GRP_RPL_MEMBER_CHANGE
  eng "Group membership changed to %s on view %s."

ER_GRP_RPL_MEMBER_LEFT_GRP
  eng "Group membership changed: This member has left the group."

ER_GRP_RPL_MEMBER_EXPELLED
  eng "Member was expelled from the group due to network failures, changing member status to ERROR."

ER_GRP_RPL_SESSION_OPEN_FAILED
  eng "Unable to open session to (re)set read only mode. Skipping."

ER_GRP_RPL_NEW_PRIMARY_ELECTED
  eng "A new primary with address %s:%u was elected. %s"

ER_GRP_RPL_DISABLE_READ_ONLY_FAILED
  eng "Unable to disable super read only flag. Try to disable it manually"

ER_GRP_RPL_ENABLE_READ_ONLY_FAILED
  eng "Unable to set super read only flag. Try to set it manually."

ER_GRP_RPL_SRV_PRIMARY_MEM
  eng "This server is working as primary member."

ER_GRP_RPL_SRV_SECONDARY_MEM
  eng "This server is working as secondary member with primary member address %s:%u."

ER_GRP_RPL_NO_SUITABLE_PRIMARY_MEM
  eng "Unable to set any member as primary. No suitable candidate."

ER_GRP_RPL_SUPER_READ_ONLY_ACTIVATE_ERROR
  eng "Error when activating super_read_only mode on start. The member will now exit the group."

ER_GRP_RPL_EXCEEDS_AUTO_INC_VALUE
  eng "Group contains %lu members which is greater than group_replication_auto_increment_increment value of %lu. This can lead to a higher transactional abort rate."

ER_GRP_RPL_DATA_NOT_PROVIDED_BY_MEM
  eng "Member with address '%s:%u' didn't provide any data during the last group change. Group information can be outdated and lead to errors on recovery."

ER_GRP_RPL_MEMBER_ALREADY_EXISTS
  eng "There is already a member with server_uuid %s. The member will now exit the group."

OBSOLETE_ER_GRP_RPL_GRP_CHANGE_INFO_EXTRACT_ERROR
  eng "Error when extracting information for group change. Operations and checks made to group joiners may be incomplete."

ER_GRP_RPL_GTID_EXECUTED_EXTRACT_ERROR
  eng "Error when extracting this member GTID executed set. Operations and checks made to group joiners may be incomplete."

ER_GRP_RPL_GTID_SET_EXTRACT_ERROR
  eng "Error when extracting this member retrieved set for its applier. Operations and checks made to group joiners may be incomplete."

ER_GRP_RPL_START_FAILED
  eng "The START GROUP_REPLICATION command failed since the group already has 9 members."

ER_GRP_RPL_MEMBER_VER_INCOMPATIBLE
  eng "Member version is incompatible with the group."

ER_GRP_RPL_TRANS_NOT_PRESENT_IN_GRP
  eng "The member contains transactions not present in the group. The member will now exit the group."

ER_GRP_RPL_TRANS_GREATER_THAN_GRP
  eng "It was not possible to assess if the member has more transactions than the group. The member will now exit the group."

ER_GRP_RPL_MEMBER_VERSION_LOWER_THAN_GRP
  eng "Member version is lower than some group member, but since option 'group_replication_allow_local_lower_version_join is enabled, member will be allowed to join."

ER_GRP_RPL_LOCAL_GTID_SETS_PROCESS_ERROR
  eng "Error processing local GTID sets when comparing this member transactions against the group."

ER_GRP_RPL_MEMBER_TRANS_GREATER_THAN_GRP
  eng "This member has more executed transactions than those present in the group. Local transactions: %s > Group transactions: %s"

ER_GRP_RPL_BLOCK_SIZE_DIFF_FROM_GRP
  eng "The member is configured with a group_replication_gtid_assignment_block_size option value '%llu' different from the group '%llu'. The member will now exit the group."

ER_GRP_RPL_TRANS_WRITE_SET_EXTRACT_DIFF_FROM_GRP
  eng "The member is configured with a transaction-write-set-extraction option value '%s' different from the group '%s'. The member will now exit the group."

ER_GRP_RPL_MEMBER_CFG_INCOMPATIBLE_WITH_GRP_CFG
  eng "The member configuration is not compatible with the group configuration. Variables such as group_replication_single_primary_mode or group_replication_enforce_update_everywhere_checks must have the same value on every server in the group. (member configuration option: [%s], group configuration option: [%s])."

ER_GRP_RPL_MEMBER_STOP_RPL_CHANNELS_ERROR
  eng "Error stopping all replication channels while server was leaving the group. %s"

ER_GRP_RPL_PURGE_APPLIER_LOGS
  eng "Detected previous RESET SOURCE invocation or an issue exists in the group replication applier relay log. Purging existing applier logs."

ER_GRP_RPL_RESET_APPLIER_MODULE_LOGS_ERROR
  eng "Unknown error occurred while resetting applier's module logs."

ER_GRP_RPL_APPLIER_THD_SETUP_ERROR
  eng "Failed to setup the group replication applier thread."

ER_GRP_RPL_APPLIER_THD_START_ERROR
  eng "Error while starting the group replication applier thread"

ER_GRP_RPL_APPLIER_THD_STOP_ERROR
  eng "Failed to stop the group replication applier thread."

ER_GRP_RPL_FETCH_TRANS_DATA_FAILED
  eng "Failed to fetch transaction data containing required transaction info for applier"

ER_GRP_RPL_REPLICA_IO_THD_PRIMARY_UNKNOWN
  eng "Can't start replica IO THREAD of channel '%s' when group replication is running with single-primary mode and the primary member is not known."

ER_GRP_RPL_SALVE_IO_THD_ON_SECONDARY_MEMBER
  eng "Can't start replica IO THREAD of channel '%s' when group replication is running with single-primary mode on a secondary member."

ER_GRP_RPL_REPLICA_SQL_THD_PRIMARY_UNKNOWN
  eng "Can't start replica SQL THREAD of channel '%s' when group replication is running with single-primary mode and the primary member is not known."

ER_GRP_RPL_REPLICA_SQL_THD_ON_SECONDARY_MEMBER
  eng "Can't start replica SQL THREAD of channel '%s' when group replication is running with single-primary mode on a secondary member."

ER_GRP_RPL_NEEDS_INNODB_TABLE
  eng "Table %s does not use the InnoDB storage engine. This is not compatible with Group Replication."

ER_GRP_RPL_PRIMARY_KEY_NOT_DEFINED
  eng "Table %s does not have any PRIMARY KEY. This is not compatible with Group Replication."

ER_GRP_RPL_FK_WITH_CASCADE_UNSUPPORTED
  eng "Table %s has a foreign key with 'CASCADE', 'SET NULL' or 'SET DEFAULT' clause. This is not compatible with Group Replication."

ER_GRP_RPL_AUTO_INC_RESET
  eng "group_replication_auto_increment_increment is reset to %lu"

ER_GRP_RPL_AUTO_INC_OFFSET_RESET
  eng "auto_increment_offset is reset to %lu"

ER_GRP_RPL_AUTO_INC_SET
  eng "group_replication_auto_increment_increment is set to %lu"

ER_GRP_RPL_AUTO_INC_OFFSET_SET
  eng "auto_increment_offset is set to %lu"

ER_GRP_RPL_FETCH_TRANS_CONTEXT_FAILED
  eng "Failed to fetch transaction context containing required transaction info for certification"

ER_GRP_RPL_FETCH_FORMAT_DESC_LOG_EVENT_FAILED
  eng "Failed to fetch Format_description_log_event containing required server info for applier"

ER_GRP_RPL_FETCH_TRANS_CONTEXT_LOG_EVENT_FAILED
  eng "Failed to fetch Transaction_context_log_event containing required transaction info for certification"

ER_GRP_RPL_FETCH_SNAPSHOT_VERSION_FAILED
  eng "Failed to read snapshot version from transaction context event required for certification"

ER_GRP_RPL_FETCH_GTID_LOG_EVENT_FAILED
  eng "Failed to fetch Gtid_log_event containing required transaction info for certification"

ER_GRP_RPL_UPDATE_SERV_CERTIFICATE_FAILED
  eng "Unable to update certification result on server side, thread_id: %lu"

ER_GRP_RPL_ADD_GTID_INFO_WITH_LOCAL_GTID_FAILED
  eng "Unable to add gtid information to the group_gtid_executed set when gtid was provided for local transactions"

ER_GRP_RPL_ADD_GTID_INFO_WITHOUT_LOCAL_GTID_FAILED
  eng "Unable to add gtid information to the group_gtid_executed set when no gtid was provided for local transactions"

ER_GRP_RPL_NOTIFY_CERTIFICATION_OUTCOME_FAILED
  eng "Failed to notify certification outcome"

ER_GRP_RPL_ADD_GTID_INFO_WITH_REMOTE_GTID_FAILED
  eng "Unable to add gtid information to the group_gtid_executed set when gtid was provided for remote transactions"

ER_GRP_RPL_ADD_GTID_INFO_WITHOUT_REMOTE_GTID_FAILED
  eng "Unable to add gtid information to the group_gtid_executed set when gtid was not provided for remote transactions"

ER_GRP_RPL_FETCH_VIEW_CHANGE_LOG_EVENT_FAILED
  eng "Failed to fetch View_change_log_event containing required info for certification"

OBSOLETE_ER_GRP_RPL_CONTACT_WITH_SRV_FAILED
  eng "Error when contacting the server to ensure the proper logging of a group change in the binlog"

OBSOLETE_ER_GRP_RPL_SRV_WAIT_TIME_OUT
  eng "Timeout when waiting for the server to execute local transactions in order assure the group change proper logging"

ER_GRP_RPL_FETCH_LOG_EVENT_FAILED
  eng "Failed to fetch Log_event containing required server info for applier"

ER_GRP_RPL_START_GRP_RPL_FAILED
  eng "Unable to start Group Replication. Replication applier infrastructure is not initialized since the server was started with --initialize, --initialize-insecure or --upgrade=MINIMAL on a server upgrade."

ER_GRP_RPL_CONN_INTERNAL_PLUGIN_FAIL
  eng "Failed to establish an internal server connection to execute plugin operations"

ER_GRP_RPL_SUPER_READ_ON
  eng "Setting super_read_only=ON."

ER_GRP_RPL_SUPER_READ_OFF
  eng "Setting super_read_only=OFF."

ER_GRP_RPL_KILLED_SESSION_ID
  eng "killed session id: %d status: %d"

ER_GRP_RPL_KILLED_FAILED_ID
  eng "killed failed id: %d failed: %d"

ER_GRP_RPL_INTERNAL_QUERY
  eng "Internal query: %s result in error. Error number: %ld"

ER_GRP_RPL_COPY_FROM_EMPTY_STRING
  eng "Error copying from empty string "

ER_GRP_RPL_QUERY_FAIL
  eng "Query execution resulted in failure. errno: %d"

ER_GRP_RPL_CREATE_SESSION_UNABLE
  eng "Unable to create a session for executing the queries on the server"

ER_GRP_RPL_MEMBER_NOT_FOUND
  eng "The member with address %s:%u has unexpectedly disappeared, killing the current group replication recovery connection"

ER_GRP_RPL_MAXIMUM_CONNECTION_RETRIES_REACHED
  eng "Maximum number of retries when trying to connect to a donor reached. Aborting group replication incremental recovery."

ER_GRP_RPL_ALL_DONORS_LEFT_ABORT_RECOVERY
  eng "All donors left. Aborting group replication incremental recovery."

ER_GRP_RPL_ESTABLISH_RECOVERY_WITH_DONOR
  eng "Establishing group recovery connection with a possible donor. Attempt %d/%d"

ER_GRP_RPL_ESTABLISH_RECOVERY_WITH_ANOTHER_DONOR
  eng "Retrying group recovery connection with another donor. Attempt %d/%d"

ER_GRP_RPL_NO_VALID_DONOR
  eng "No valid donors exist in the group, retrying"

ER_GRP_RPL_CONFIG_RECOVERY
  eng "Error when configuring the asynchronous recovery channel connection to the donor."

ER_GRP_RPL_ESTABLISHING_CONN_GRP_REC_DONOR
  eng "Establishing connection to a group replication recovery donor %s at %s port: %d."

ER_GRP_RPL_CREATE_GRP_RPL_REC_CHANNEL
  eng "Error while creating the group replication recovery channel with donor %s at %s port: %d."

ER_GRP_RPL_DONOR_SERVER_CONN
  eng "There was an error when connecting to the donor server. Please check that group_replication_recovery channel credentials and all MEMBER_HOST column values of performance_schema.replication_group_members table are correct and DNS resolvable."

ER_GRP_RPL_CHECK_STATUS_TABLE
  eng "For details please check performance_schema.replication_connection_status table and error log messages of Replica I/O for channel group_replication_recovery."

ER_GRP_RPL_STARTING_GRP_REC
  eng "Error while starting the group replication incremental recovery receiver/applier threads"

ER_GRP_RPL_DONOR_CONN_TERMINATION
  eng "Terminating existing group replication donor connection and purging the corresponding logs."

ER_GRP_RPL_STOPPING_GRP_REC
  eng "Error when stopping the group replication incremental recovery's donor connection"

ER_GRP_RPL_PURGE_REC
  eng "Error when purging the group replication recovery's relay logs"

ER_GRP_RPL_UNABLE_TO_KILL_CONN_REC_DONOR_APPLIER
  eng "Unable to kill the current group replication recovery donor connection after an applier error. Incremental recovery will shutdown."

ER_GRP_RPL_UNABLE_TO_KILL_CONN_REC_DONOR_FAILOVER
  eng "Unable to kill the current group replication recovery donor connection during failover. Incremental recovery will shutdown."

ER_GRP_RPL_FAILED_TO_NOTIFY_GRP_MEMBERSHIP_EVENT
  eng "Unexpected error when notifying an internal component named %s regarding a group membership event."

ER_GRP_RPL_FAILED_TO_BROADCAST_GRP_MEMBERSHIP_NOTIFICATION
  eng "An undefined error was found while broadcasting an internal group membership notification! This is likely to happen if your components or plugins are not properly loaded or are malfunctioning!"

ER_GRP_RPL_FAILED_TO_BROADCAST_MEMBER_STATUS_NOTIFICATION
  eng "An undefined error was found while broadcasting an internal group member status notification! This is likely to happen if your components or plugins are not properly loaded or are malfunctioning!"

ER_GRP_RPL_OOM_FAILED_TO_GENERATE_IDENTIFICATION_HASH
  eng "No memory to generate write identification hash"

ER_GRP_RPL_WRITE_IDENT_HASH_BASE64_ENCODING_FAILED
  eng "Base 64 encoding of the write identification hash failed"

ER_GRP_RPL_INVALID_BINLOG_FORMAT
  eng "Binlog format should be ROW for Group Replication"

OBSOLETE_ER_GRP_RPL_BINLOG_CHECKSUM_SET
  eng "binlog_checksum should be NONE for Group Replication"

ER_GRP_RPL_TRANS_WRITE_SET_EXTRACTION_NOT_SET
  eng "A transaction_write_set_extraction algorithm should be selected when running Group Replication"

ER_GRP_RPL_UNSUPPORTED_TRANS_ISOLATION
  eng "Transaction isolation level (tx_isolation) is set to SERIALIZABLE, which is not compatible with Group Replication"

ER_GRP_RPL_CANNOT_EXECUTE_TRANS_WHILE_STOPPING
  eng "Transaction cannot be executed while Group Replication is stopping."

ER_GRP_RPL_CANNOT_EXECUTE_TRANS_WHILE_RECOVERING
  eng "Transaction cannot be executed while Group Replication is recovering. Try again when the server is ONLINE."

ER_GRP_RPL_CANNOT_EXECUTE_TRANS_IN_ERROR_STATE
  eng "Transaction cannot be executed while Group Replication is on ERROR state. Check for errors and restart the plugin"

ER_GRP_RPL_CANNOT_EXECUTE_TRANS_IN_OFFLINE_MODE
  eng "Transaction cannot be executed while Group Replication is OFFLINE. Check for errors and restart the plugin"

ER_GRP_RPL_MULTIPLE_CACHE_TYPE_NOT_SUPPORTED_FOR_SESSION
  eng "We can only use one cache type at a time on session %u"

ER_GRP_RPL_FAILED_TO_REINIT_BINLOG_CACHE_FOR_READ
  eng "Failed to reinit binlog cache log for read on session %u"

ER_GRP_RPL_FAILED_TO_CREATE_TRANS_CONTEXT
  eng "Failed to create the context of the current transaction on session %u"

ER_GRP_RPL_FAILED_TO_EXTRACT_TRANS_WRITE_SET
  eng "Failed to extract the set of items written during the execution of the current transaction on session %u"

ER_GRP_RPL_FAILED_TO_GATHER_TRANS_WRITE_SET
  eng "Failed to gather the set of items written during the execution of the current transaction on session %u"

ER_GRP_RPL_TRANS_SIZE_EXCEEDS_LIMIT
  eng "Error on session %u. Transaction of size %llu exceeds specified limit %lu. To increase the limit please adjust group_replication_transaction_size_limit option."

OBSOLETE_ER_GRP_RPL_REINIT_OF_INTERNAL_CACHE_FOR_READ_FAILED
  eng "Error while re-initializing an internal cache, for read operations, on session %u"

OBSOLETE_ER_GRP_RPL_APPENDING_DATA_TO_INTERNAL_CACHE_FAILED
  eng "Error while appending data to an internal cache on session %u"

ER_GRP_RPL_WRITE_TO_TRANSACTION_MESSAGE_FAILED
  eng "Error while writing to transaction message on session %u"

ER_GRP_RPL_FAILED_TO_REGISTER_TRANS_OUTCOME_NOTIFICTION
  eng "Unable to register for getting notifications regarding the outcome of the transaction on session %u"

ER_GRP_RPL_MSG_TOO_LONG_BROADCASTING_TRANS_FAILED
  eng "Error broadcasting transaction to the group on session %u. Message is too big."

ER_GRP_RPL_BROADCASTING_TRANS_TO_GRP_FAILED
  eng "Error while broadcasting the transaction to the group on session %u"

ER_GRP_RPL_ERROR_WHILE_WAITING_FOR_CONFLICT_DETECTION
  eng "Error while waiting for conflict detection procedure to finish on session %u"

OBSOLETE_ER_GRP_RPL_REINIT_OF_INTERNAL_CACHE_FOR_WRITE_FAILED
  eng "Error while re-initializing an internal cache, for write operations, on session %u"

OBSOLETE_ER_GRP_RPL_FAILED_TO_CREATE_COMMIT_CACHE
  eng "Failed to create group replication commit cache on session %u"

OBSOLETE_ER_GRP_RPL_REINIT_OF_COMMIT_CACHE_FOR_WRITE_FAILED
  eng "Failed to reinit group replication commit cache for write on session %u"

OBSOLETE_ER_GRP_RPL_PREV_REC_SESSION_RUNNING
  eng "A previous recovery session is still running. Please stop the group replication plugin and wait for it to stop"

ER_GRP_RPL_FATAL_REC_PROCESS
  eng "Fatal error during the incremental recovery process of Group Replication. The server will leave the group."

OBSOLETE_ER_GRP_RPL_WHILE_STOPPING_REP_CHANNEL
  eng "Error stopping all replication channels while server was leaving the group. %s"

ER_GRP_RPL_UNABLE_TO_EVALUATE_APPLIER_STATUS
  eng "Unable to evaluate the group replication applier execution status. Group replication recovery will shutdown to avoid data corruption."

ER_GRP_RPL_ONLY_ONE_SERVER_ALIVE
  eng "Only one server alive. Declaring this server as online within the replication group"

ER_GRP_RPL_CERTIFICATION_REC_PROCESS
  eng "Error when processing certification information in the incremental recovery process"

ER_GRP_RPL_UNABLE_TO_ENSURE_EXECUTION_REC
  eng "Unable to ensure the execution of group transactions received during recovery."

ER_GRP_RPL_WHILE_SENDING_MSG_REC
  eng "Error while sending message in the group replication incremental recovery process."

OBSOLETE_ER_GRP_RPL_READ_UNABLE_FOR_SUPER_READ_ONLY
  eng "Unable to read the server value for the super_read_only variable."

ER_GRP_RPL_READ_UNABLE_FOR_READ_ONLY_SUPER_READ_ONLY
  eng "Unable to read the server values for the read_only and super_read_only variables."

ER_GRP_RPL_UNABLE_TO_RESET_SERVER_READ_MODE
  eng "Unable to reset the server read mode settings. Try to reset them manually."

ER_GRP_RPL_UNABLE_TO_CERTIFY_PLUGIN_TRANS
  eng "Due to a plugin error, some transactions were unable to be certified and will now rollback."

ER_GRP_RPL_UNBLOCK_CERTIFIED_TRANS
  eng "Error when trying to unblock non certified or consistent transactions. Check for consistency errors when restarting the service"

OBSOLETE_ER_GRP_RPL_SERVER_WORKING_AS_SECONDARY
  eng "This server is working as secondary member with primary member address %s:%u."

ER_GRP_RPL_FAILED_TO_START_WITH_INVALID_SERVER_ID
  eng "Unable to start Group Replication. Replication applier infrastructure is not initialized since the server was started with server_id=0. Please, restart the server with server_id larger than 0."

ER_GRP_RPL_FORCE_MEMBERS_MUST_BE_EMPTY
  eng "group_replication_force_members must be empty on group start. Current value: '%s'"

ER_GRP_RPL_PLUGIN_STRUCT_INIT_NOT_POSSIBLE_ON_SERVER_START
  eng "It was not possible to guarantee the initialization of plugin structures on server start"

ER_GRP_RPL_FAILED_TO_ENABLE_SUPER_READ_ONLY_MODE
  eng "Could not enable the server read only mode and guarantee a safe recovery execution"

ER_GRP_RPL_FAILED_TO_INIT_COMMUNICATION_ENGINE
  eng "Error on group communication engine initialization"

ER_GRP_RPL_FAILED_TO_START_ON_SECONDARY_WITH_ASYNC_CHANNELS
  eng "Can't start group replication on secondary member with single-primary mode while asynchronous replication channels are running."

ER_GRP_RPL_FAILED_TO_START_COMMUNICATION_ENGINE
  eng "Error on group communication engine start"

ER_GRP_RPL_TIMEOUT_ON_VIEW_AFTER_JOINING_GRP
  eng "Timeout on wait for view after joining group"

ER_GRP_RPL_FAILED_TO_CALL_GRP_COMMUNICATION_INTERFACE
  eng "Error calling group communication interfaces"

ER_GRP_RPL_MEMBER_SERVER_UUID_IS_INCOMPATIBLE_WITH_GRP
  eng "Member server_uuid is incompatible with the group. Server_uuid %s matches group_replication_group_name %s."

ER_GRP_RPL_MEMBER_CONF_INFO
  eng "Member configuration: member_id: %lu; member_uuid: \"%s\"; single-primary mode: \"%s\"; group_replication_auto_increment_increment: %lu; group_replication_view_change_uuid: \"%s\";"

ER_GRP_RPL_FAILED_TO_CONFIRM_IF_SERVER_LEFT_GRP
  eng "Unable to confirm whether the server has left the group or not. Check performance_schema.replication_group_members to check group membership information."

ER_GRP_RPL_SERVER_IS_ALREADY_LEAVING
  eng "Skipping leave operation: concurrent attempt to leave the group is on-going."

ER_GRP_RPL_SERVER_ALREADY_LEFT
  eng "Skipping leave operation: member already left the group."

ER_GRP_RPL_WAITING_FOR_VIEW_UPDATE
  eng "Going to wait for view modification"

ER_GRP_RPL_TIMEOUT_RECEIVING_VIEW_CHANGE_ON_SHUTDOWN
  eng "While leaving the group due to a stop, shutdown or failure there was a timeout receiving a view change. This can lead to a possible inconsistent state. Check the log for more details"

ER_GRP_RPL_REQUESTING_NON_MEMBER_SERVER_TO_LEAVE
  eng "Requesting to leave the group despite of not being a member"

ER_GRP_RPL_IS_STOPPING
  eng "Plugin 'group_replication' is stopping."

ER_GRP_RPL_IS_STOPPED
  eng "Plugin 'group_replication' has been stopped."

ER_GRP_RPL_FAILED_TO_ENABLE_READ_ONLY_MODE_ON_SHUTDOWN
  eng "On plugin shutdown it was not possible to enable the server read only mode. Local transactions will be accepted and committed."

ER_GRP_RPL_RECOVERY_MODULE_TERMINATION_TIMED_OUT_ON_SHUTDOWN
  eng "On shutdown there was a timeout on the Group Replication recovery module termination. Check the log for more details"

ER_GRP_RPL_APPLIER_TERMINATION_TIMED_OUT_ON_SHUTDOWN
  eng "On shutdown there was a timeout on the Group Replication applier termination."

ER_GRP_RPL_FAILED_TO_SHUTDOWN_REGISTRY_MODULE
  eng "Unexpected failure while shutting down registry module!"

ER_GRP_RPL_FAILED_TO_INIT_HANDLER
  eng "Failure during Group Replication handler initialization"

ER_GRP_RPL_FAILED_TO_REGISTER_SERVER_STATE_OBSERVER
  eng "Failure when registering the server state observers"

ER_GRP_RPL_FAILED_TO_REGISTER_TRANS_STATE_OBSERVER
  eng "Failure when registering the transactions state observers"

ER_GRP_RPL_FAILED_TO_REGISTER_BINLOG_STATE_OBSERVER
  eng "Failure when registering the binlog state observers"

ER_GRP_RPL_FAILED_TO_START_ON_BOOT
  eng "Unable to start Group Replication on boot"

ER_GRP_RPL_FAILED_TO_STOP_ON_PLUGIN_UNINSTALL
  eng "Failure when stopping Group Replication on plugin uninstall"

ER_GRP_RPL_FAILED_TO_UNREGISTER_SERVER_STATE_OBSERVER
  eng "Failure when unregistering the server state observers"

ER_GRP_RPL_FAILED_TO_UNREGISTER_TRANS_STATE_OBSERVER
  eng "Failure when unregistering the transactions state observers"

ER_GRP_RPL_FAILED_TO_UNREGISTER_BINLOG_STATE_OBSERVER
  eng "Failure when unregistering the binlog state observers"

ER_GRP_RPL_ALL_OBSERVERS_UNREGISTERED
  eng "All Group Replication server observers have been successfully unregistered"

ER_GRP_RPL_FAILED_TO_PARSE_THE_GRP_NAME
  eng "Unable to parse the group_replication_group_name."

ER_GRP_RPL_FAILED_TO_GENERATE_SIDNO_FOR_GRP
  eng "Unable to parse the group_replication_group_name."

ER_GRP_RPL_APPLIER_NOT_STARTED_DUE_TO_RUNNING_PREV_SHUTDOWN
  eng "Cannot start the Group Replication applier as a previous shutdown is still running: The thread will stop once its task is complete."

ER_GRP_RPL_FAILED_TO_INIT_APPLIER_MODULE
  eng "Unable to initialize the Group Replication applier module."

ER_GRP_RPL_APPLIER_INITIALIZED
  eng "Group Replication applier module successfully initialized!"

ER_GRP_RPL_COMMUNICATION_SSL_CONF_INFO
  eng "Group communication SSL configuration: group_replication_ssl_mode: \"%s\"; server_key_file: \"%s\"; server_cert_file: \"%s\"; client_key_file: \"%s\"; client_cert_file: \"%s\"; ca_file: \"%s\"; ca_path: \"%s\"; cipher: \"%s\"; tls_version: \"%s\"; tls_ciphersuites: \"%s\"; crl_file: \"%s\"; crl_path: \"%s\"; ssl_fips_mode: \"%s\""

ER_GRP_RPL_ABORTS_AS_SSL_NOT_SUPPORTED_BY_MYSQLD
  eng "MySQL server does not have SSL support and group_replication_ssl_mode is \"%s\", START GROUP_REPLICATION will abort"

ER_GRP_RPL_SSL_DISABLED
  eng "Group communication SSL configuration: group_replication_ssl_mode: \"%s\""

ER_GRP_RPL_UNABLE_TO_INIT_COMMUNICATION_ENGINE
  eng "Unable to initialize the group communication engine"

ER_GRP_RPL_BINLOG_DISABLED
  eng "Binlog must be enabled for Group Replication"

ER_GRP_RPL_GTID_MODE_OFF
  eng "Gtid mode should be ON for Group Replication"

ER_GRP_RPL_LOG_REPLICA_UPDATES_NOT_SET
  eng "LOG_REPLICA_UPDATES should be ON for Group Replication"

ER_GRP_RPL_INVALID_TRANS_WRITE_SET_EXTRACTION_VALUE
  eng "Extraction of transaction write sets requires an hash algorithm configuration. Please, double check that the parameter transaction-write-set-extraction is set to a valid algorithm."

ER_GRP_RPL_APPLIER_METADATA_REPO_MUST_BE_TABLE
  eng "Applier metadata repository must be set to TABLE"

ER_GRP_RPL_CONNECTION_METADATA_REPO_MUST_BE_TABLE
  eng "Connection metadata repository must be set to TABLE."

ER_GRP_RPL_INCORRECT_TYPE_SET_FOR_PARALLEL_APPLIER
  eng "In order to use parallel applier on Group Replication, parameter replica-parallel-type must be set to 'LOGICAL_CLOCK'."

ER_GRP_RPL_REPLICA_PRESERVE_COMMIT_ORDER_NOT_SET
  eng "Group Replication requires replica-preserve-commit-order to be set to ON when using more than 1 applier threads."

ER_GRP_RPL_SINGLE_PRIM_MODE_NOT_ALLOWED_WITH_UPDATE_EVERYWHERE
  eng "It is not allowed to run single primary mode with 'group_replication_enforce_update_everywhere_checks' enabled."

ER_GRP_RPL_MODULE_TERMINATE_ERROR
  eng "error_message: %s"

ER_GRP_RPL_GRP_NAME_OPTION_MANDATORY
  eng "The group_replication_group_name option is mandatory"

ER_GRP_RPL_GRP_NAME_IS_TOO_LONG
  eng "The group_replication_group_name '%s' is not a valid UUID, its length is too big"

ER_GRP_RPL_GRP_NAME_IS_NOT_VALID_UUID
  eng "The group_replication_group_name '%s' is not a valid UUID"

ER_GRP_RPL_FLOW_CTRL_MIN_QUOTA_GREATER_THAN_MAX_QUOTA
  eng "group_replication_flow_control_min_quota cannot be larger than group_replication_flow_control_max_quota"

ER_GRP_RPL_FLOW_CTRL_MIN_RECOVERY_QUOTA_GREATER_THAN_MAX_QUOTA
  eng "group_replication_flow_control_min_recovery_quota cannot be larger than group_replication_flow_control_max_quota"

ER_GRP_RPL_FLOW_CTRL_MAX_QUOTA_SMALLER_THAN_MIN_QUOTAS
  eng "group_replication_flow_control_max_quota cannot be smaller than group_replication_flow_control_min_quota or group_replication_flow_control_min_recovery_quota"

ER_GRP_RPL_INVALID_SSL_RECOVERY_STRING
  eng "The given value for recovery ssl option 'group_replication_%s' is invalid as its length is beyond the limit"

OBSOLETE_ER_GRP_RPL_SUPPORTS_ONLY_ONE_FORCE_MEMBERS_SET
  eng "There is one group_replication_force_members operation already ongoing"

OBSOLETE_ER_GRP_RPL_FORCE_MEMBERS_SET_UPDATE_NOT_ALLOWED
  eng "group_replication_force_members can only be updated when Group Replication is running and a majority of the members are unreachable"

ER_GRP_RPL_GRP_COMMUNICATION_INIT_WITH_CONF
  eng "Initialized group communication with configuration: group_replication_group_name: '%s'; group_replication_local_address: '%s'; group_replication_group_seeds: '%s'; group_replication_bootstrap_group: '%s'; group_replication_poll_spin_loops: %lu; group_replication_compression_threshold: %lu; group_replication_ip_allowlist: '%s'; group_replication_communication_debug_options: '%s'; group_replication_member_expel_timeout: '%lu'; group_replication_communication_max_message_size: %lu; group_replication_message_cache_size: '%luu; group_replication_communication_stack: '%lu'"

ER_GRP_RPL_UNKNOWN_GRP_RPL_APPLIER_PIPELINE_REQUESTED
  eng "Unknown group replication applier pipeline requested"

ER_GRP_RPL_FAILED_TO_BOOTSTRAP_EVENT_HANDLING_INFRASTRUCTURE
  eng "Unable to bootstrap group replication event handling infrastructure. Unknown handler type: %d"

ER_GRP_RPL_APPLIER_HANDLER_NOT_INITIALIZED
  eng "One of the group replication applier handlers is null due to an initialization error"

ER_GRP_RPL_APPLIER_HANDLER_IS_IN_USE
  eng "A group replication applier handler, marked as unique, is already in use."

ER_GRP_RPL_APPLIER_HANDLER_ROLE_IS_IN_USE
  eng "A group replication applier handler role, that was marked as unique, is already in use."

ER_GRP_RPL_FAILED_TO_INIT_APPLIER_HANDLER
  eng "Error on group replication applier handler initialization"

ER_GRP_RPL_SQL_SERVICE_FAILED_TO_INIT_SESSION_THREAD
  eng "Error when initializing a session thread for internal server connection."

ER_GRP_RPL_SQL_SERVICE_COMM_SESSION_NOT_INITIALIZED
  eng "Error running internal SQL query: %s. The internal server communication session is not initialized"

ER_GRP_RPL_SQL_SERVICE_SERVER_SESSION_KILLED
  eng "Error running internal SQL query: %s. The internal server session was killed or server is shutting down."

ER_GRP_RPL_SQL_SERVICE_FAILED_TO_RUN_SQL_QUERY
  eng "Error running internal SQL query: %s. Got internal SQL error: %s(%d)"

ER_GRP_RPL_SQL_SERVICE_SERVER_INTERNAL_FAILURE
  eng "Error running internal SQL query: %s. Internal failure."

ER_GRP_RPL_SQL_SERVICE_RETRIES_EXCEEDED_ON_SESSION_STATE
  eng "Error, maximum number of retries exceeded when waiting for the internal server session state to be operating"

ER_GRP_RPL_SQL_SERVICE_FAILED_TO_FETCH_SECURITY_CTX
  eng "Error when trying to fetch security context when contacting the server for internal plugin requests."

ER_GRP_RPL_SQL_SERVICE_SERVER_ACCESS_DENIED_FOR_USER
  eng "There was an error when trying to access the server with user: %s. Make sure the user is present in the server and that the MySQL upgrade procedure was run correctly."

ER_GRP_RPL_SQL_SERVICE_MAX_CONN_ERROR_FROM_SERVER
  eng "Failed to establish an internal server connection to execute plugin operations since the server does not have available connections, please increase @@GLOBAL.MAX_CONNECTIONS. Server error: %i."

ER_GRP_RPL_SQL_SERVICE_SERVER_ERROR_ON_CONN
  eng "Failed to establish an internal server connection to execute plugin operations. Server error: %i. Server error message: %s"

ER_GRP_RPL_UNREACHABLE_MAJORITY_TIMEOUT_FOR_MEMBER
  eng "This member could not reach a majority of the members for more than %ld seconds. The member will now leave the group as instructed by the group_replication_unreachable_majority_timeout option."

ER_GRP_RPL_SERVER_SET_TO_READ_ONLY_DUE_TO_ERRORS
  eng "The server was automatically set into read only mode after an error was detected."

ER_GRP_RPL_GMS_LISTENER_FAILED_TO_LOG_NOTIFICATION
  eng "Unable to log notification to table (errno: %lu) (res: %d)! Message: %s"

ER_GRP_RPL_GRP_COMMUNICATION_ENG_INIT_FAILED
  eng "Failure in group communication engine '%s' initialization"

ER_GRP_RPL_SET_GRP_COMMUNICATION_ENG_LOGGER_FAILED
  eng "Unable to set the group communication engine logger"

ER_GRP_RPL_DEBUG_OPTIONS
  eng "Current debug options are: '%s'."

ER_GRP_RPL_INVALID_DEBUG_OPTIONS
  eng "Some debug options in '%s' are not valid."

ER_GRP_RPL_EXIT_GRP_GCS_ERROR
  eng "Error calling group communication interfaces while trying to leave the group"

ER_GRP_RPL_GRP_MEMBER_OFFLINE
  eng "Member is not ONLINE, it is not possible to force a new group membership"

ER_GRP_RPL_GCS_INTERFACE_ERROR
  eng "Error calling group communication interfaces"

ER_GRP_RPL_FORCE_MEMBER_VALUE_SET_ERROR
  eng "Error setting group_replication_force_members value '%s' on group communication interfaces"

ER_GRP_RPL_FORCE_MEMBER_VALUE_SET
  eng "The group_replication_force_members value '%s' was set in the group communication interfaces"

ER_GRP_RPL_FORCE_MEMBER_VALUE_TIME_OUT
  eng "Timeout on wait for view after setting group_replication_force_members value '%s' into group communication interfaces"

ER_GRP_RPL_BROADCAST_COMMIT_MSSG_TOO_BIG
  eng "Broadcast of committed transactions message failed. Message is too big."

ER_GRP_RPL_SEND_STATS_ERROR
  eng "Error while sending stats message"

ER_GRP_RPL_MEMBER_STATS_INFO
  eng "Flow control - update member stats: %s stats certifier_queue %d, applier_queue %d certified %ld (%ld), applied %ld (%ld), local %ld (%ld), quota %ld (%ld) mode=%d"

ER_GRP_RPL_FLOW_CONTROL_STATS
  eng "Flow control: throttling to %ld commits per %ld sec, with %d writing and %d non-recovering members, min capacity %lld, lim throttle %lld"

ER_GRP_RPL_UNABLE_TO_CONVERT_PACKET_TO_EVENT
  eng "Unable to convert a packet into an event on the applier. Error: %s"

ER_GRP_RPL_PIPELINE_CREATE_FAILED
  eng "Failed to create group replication pipeline cache."

ER_GRP_RPL_PIPELINE_REINIT_FAILED_WRITE
  eng "Failed to reinit group replication pipeline cache for write."

ER_GRP_RPL_UNABLE_TO_CONVERT_EVENT_TO_PACKET
  eng "Unable to convert the event into a packet on the applier. Error: %s"

ER_GRP_RPL_PIPELINE_FLUSH_FAIL
  eng "Failed to flush group replication pipeline cache."

ER_GRP_RPL_PIPELINE_REINIT_FAILED_READ
  eng "Failed to reinit group replication pipeline cache for read."

OBSOLETE_ER_GRP_RPL_STOP_REP_CHANNEL
  eng "Error stopping all replication channels while server was leaving the group. Got error: %d. Please check the error log for more details."

ER_GRP_RPL_GCS_GR_ERROR_MSG
  eng "%s"

ER_GRP_RPL_REPLICA_IO_THREAD_UNBLOCKED
  eng "The replica IO thread of channel '%s' is unblocked as the member is declared ONLINE now."

ER_GRP_RPL_REPLICA_IO_THREAD_ERROR_OUT
  eng "The replica IO thread of channel '%s' will error out as the member failed to come ONLINE."

ER_GRP_RPL_REPLICA_APPLIER_THREAD_UNBLOCKED
  eng "The replica applier thread of channel '%s' is unblocked as the member is declared ONLINE now."

ER_GRP_RPL_REPLICA_APPLIER_THREAD_ERROR_OUT
  eng "The replica applier thread of channel '%s' will error out as the member failed to come ONLINE."

ER_LDAP_AUTH_FAILED_TO_CREATE_OR_GET_CONNECTION
  eng "LDAP authentication initialize: failed to create/ get connection from the pool. "

ER_LDAP_AUTH_DEINIT_FAILED
  eng "LDAP authentication de_initialize Failed"

ER_LDAP_AUTH_SKIPPING_USER_GROUP_SEARCH
  eng "Skipping group search, No group attribute mentioned"

ER_LDAP_AUTH_POOL_DISABLE_MAX_SIZE_ZERO
  eng "Pool max size is 0, connection pool is disabled"

ER_LDAP_AUTH_FAILED_TO_CREATE_LDAP_OBJECT_CREATOR
  eng "Connection pool initialization, failed to create LDAP object creator"

ER_LDAP_AUTH_FAILED_TO_CREATE_LDAP_OBJECT
  eng "Connection pool initialization, failed to create LDAP object"

ER_LDAP_AUTH_TLS_CONF
  eng "LDAP TLS configuration"

ER_LDAP_AUTH_TLS_CONNECTION
  eng "LDAP TLS connection"

ER_LDAP_AUTH_CONN_POOL_NOT_CREATED
  eng "LDAP pool is not created."

ER_LDAP_AUTH_CONN_POOL_INITIALIZING
  eng "LDAP pool is initializing"

ER_LDAP_AUTH_CONN_POOL_DEINITIALIZING
  eng "LDAP pool is de-initializing"

ER_LDAP_AUTH_ZERO_MAX_POOL_SIZE_UNCHANGED
  eng "Pool max size old and new values are 0"

ER_LDAP_AUTH_POOL_REINITIALIZING
  eng "LDAP pool is re-initializing"

ER_LDAP_AUTH_FAILED_TO_WRITE_PACKET
  eng "Plug-in has failed to write the packet."

ER_LDAP_AUTH_SETTING_USERNAME
  eng "Setting LDAP user name as : %s"

ER_LDAP_AUTH_USER_AUTH_DATA
  eng "User authentication data: %s size: %lu"

ER_LDAP_AUTH_INFO_FOR_USER
  eng "User is authenticated as: %s external user: %s"

ER_LDAP_AUTH_USER_GROUP_SEARCH_INFO
  eng "Group search information base DN: %s scope: %d filter: %s attribute: %s"

ER_LDAP_AUTH_GRP_SEARCH_SPECIAL_HDL
  eng "Special handling for group search, {GA} found"

ER_LDAP_AUTH_GRP_IS_FULL_DN
  eng "Group search special handling, group full DN found. "

ER_LDAP_AUTH_USER_NOT_FOUND_IN_ANY_GRP
  eng "User %s is not member of any group."

ER_LDAP_AUTH_USER_FOUND_IN_MANY_GRPS
  eng "User %s is member of more than one group"

ER_LDAP_AUTH_USER_HAS_MULTIPLE_GRP_NAMES
  eng "For user %s has multiple user group names. Please check if group attribute name is correct"

ER_LDAP_AUTH_SEARCHED_USER_GRP_NAME
  eng "Searched group name: %s"

ER_LDAP_AUTH_OBJECT_CREATE_TIMESTAMP
  eng "LDAP authentication object creation time_stamp: %s dn: %s"

ER_LDAP_AUTH_CERTIFICATE_NAME
  eng "Certificate name: %s"

ER_LDAP_AUTH_FAILED_TO_POOL_DEINIT
  eng "Failed to pool de-initialized: pool is already reconstructing"

ER_LDAP_AUTH_FAILED_TO_INITIALIZE_POOL_IN_RECONSTRUCTING
  eng "Pool initialization failed: pool is already initialized"

ER_LDAP_AUTH_FAILED_TO_INITIALIZE_POOL_IN_INIT_STATE
  eng "Pool initialization failed: pool is initializing"

ER_LDAP_AUTH_FAILED_TO_INITIALIZE_POOL_IN_DEINIT_STATE
  eng "Pool initialization failed: pool is de-initializing"

ER_LDAP_AUTH_FAILED_TO_DEINITIALIZE_POOL_IN_RECONSTRUCT_STATE
  eng "Failed to pool deinitialized: pool is already reconstructing"

ER_LDAP_AUTH_FAILED_TO_DEINITIALIZE_NOT_READY_POOL
  eng "Failed to pool deinitialized : pool is not ready"

ER_LDAP_AUTH_FAILED_TO_GET_CONNECTION_AS_PLUGIN_NOT_READY
  eng "Ldap_connection_pool::get: Failed to return connection as plug-in is not ready/initializing/de-initializing"

ER_LDAP_AUTH_CONNECTION_POOL_INIT_FAILED
  eng "Connection pool has failed to initialized"

ER_LDAP_AUTH_MAX_ALLOWED_CONNECTION_LIMIT_HIT
  eng "Ldap_connetion_pool::get LDAP maximum connection allowed size is reached. Increase the maximum limit."

ER_LDAP_AUTH_MAX_POOL_SIZE_SET_FAILED
  eng "Set max pool size failed."

ER_LDAP_AUTH_PLUGIN_FAILED_TO_READ_PACKET
  eng "Plug-in has failed to read the packet from client"

ER_LDAP_AUTH_CREATING_LDAP_CONNECTION
  eng "Ldap_authentication::initialize: creating new LDAP connection. "

ER_LDAP_AUTH_GETTING_CONNECTION_FROM_POOL
  eng "Ldap_authentication::initialize: getting connection from pool. "

ER_LDAP_AUTH_RETURNING_CONNECTION_TO_POOL
  eng "Ldap_authentication::de_initialize putting back connection in the pool"

ER_LDAP_AUTH_SEARCH_USER_GROUP_ATTR_NOT_FOUND
  eng "Ldap_authentication::search_user_group no group attribute found"

ER_LDAP_AUTH_LDAP_INFO_NULL
  eng "Ldap_connetion_pool::put ldap info null"

ER_LDAP_AUTH_FREEING_CONNECTION
  eng "Ldap_connection_pool::put connection is freeing. "

ER_LDAP_AUTH_CONNECTION_PUSHED_TO_POOL
  eng "Ldap_connection_pool::put connection in pushed in the pool"

ER_LDAP_AUTH_CONNECTION_CREATOR_ENTER
  eng "Ldap_connection_creator::Ldap_connection_creator"

ER_LDAP_AUTH_STARTING_TLS
  eng "starting TLS"

ER_LDAP_AUTH_CONNECTION_GET_LDAP_INFO_NULL
  eng "Ldap_connection_pool::get: (ldap_info == NULL)|| (*ldap_info)"

ER_LDAP_AUTH_DELETING_CONNECTION_KEY
  eng "Ldap_connection_pool::deinit: deleting connection key %s"

ER_LDAP_AUTH_POOLED_CONNECTION_KEY
  eng " Ldap_connection_pool::get pooled connection key: %s"

ER_LDAP_AUTH_CREATE_CONNECTION_KEY
  eng "Ldap_connection_pool::get create connection key: %s"

ER_LDAP_AUTH_COMMUNICATION_HOST_INFO
  eng "LDAP communication host %s port %u"

ER_LDAP_AUTH_METHOD_TO_CLIENT
  eng "Sending authentication method to client : %s"

ER_LDAP_AUTH_SASL_REQUEST_FROM_CLIENT
  eng "SASL request received from mysql client: %s"

ER_LDAP_AUTH_SASL_PROCESS_SASL
  eng "Ldap_sasl_authentication::process_sasl rc: %s"

ER_LDAP_AUTH_SASL_BIND_SUCCESS_INFO
  eng "Ldap_sasl_authentication::process_sasl sasl bind succeed. dn: %s method: %s server credential: %s"

ER_LDAP_AUTH_STARTED_FOR_USER
  eng "LDAP authentication started for user name: %s"

ER_LDAP_AUTH_DISTINGUISHED_NAME
  eng "%s"

ER_LDAP_AUTH_INIT_FAILED
  eng "LDAP authentication initialize is failed with: %s"

ER_LDAP_AUTH_OR_GROUP_RETRIEVAL_FAILED
  eng "LDAP authentication failed or group retrieval failed: %s"

ER_LDAP_AUTH_USER_GROUP_SEARCH_FAILED
  eng "Search user group has failed: %s"

ER_LDAP_AUTH_USER_BIND_FAILED
  eng "LDAP user bind has failed: %s"

ER_LDAP_AUTH_POOL_GET_FAILED_TO_CREATE_CONNECTION
  eng "Connection pool get: Failed to create LDAP connection. %s"

ER_LDAP_AUTH_FAILED_TO_CREATE_LDAP_CONNECTION
  eng "Failed to create new LDAP connection:  %s"

ER_LDAP_AUTH_FAILED_TO_ESTABLISH_TLS_CONNECTION
  eng "Failed to establish TLS connection:  %s"

ER_LDAP_AUTH_FAILED_TO_SEARCH_DN
  eng "Failed to search user full dn: %s"

ER_LDAP_AUTH_CONNECTION_POOL_REINIT_ENTER
  eng "Ldap_connection_pool::reinit"

ER_SYSTEMD_NOTIFY_PATH_TOO_LONG
  eng "The path '%s', from the NOTIFY_SOCKET environment variable, is too long. At %u bytes it exceeds the limit of %u bytes for an AF_UNIX socket."

ER_SYSTEMD_NOTIFY_CONNECT_FAILED
  eng "Failed to connect to systemd notification socket named %s. Error: '%s'"

ER_SYSTEMD_NOTIFY_WRITE_FAILED
  eng "Failed to write '%s' to systemd notification. Error: '%s'"

ER_FOUND_MISSING_GTIDS
  eng "Cannot replicate to server with server_uuid='%.36s' because the present server has purged required binary logs. The connecting server needs to replicate the missing transactions from elsewhere, or be replaced by a new server created from a more recent backup. To prevent this error in the future, consider increasing the binary log expiration period on the present server. %s."

ER_PID_FILE_PRIV_DIRECTORY_INSECURE
  eng "Insecure configuration for --pid-file: Location '%s' in the path is accessible to all OS users. Consider choosing a different directory."

ER_CANT_CHECK_PID_PATH
  eng "Can't start server: can't check PID filepath: %s"

ER_VALIDATE_PWD_STATUS_VAR_REGISTRATION_FAILED
  eng "validate_password status variables registration failed."

ER_VALIDATE_PWD_STATUS_VAR_UNREGISTRATION_FAILED
  eng "validate_password status variables unregistration failed."

ER_VALIDATE_PWD_DICT_FILE_OPEN_FAILED
  eng "Dictionary file open failed"

ER_VALIDATE_PWD_COULD_BE_NULL
  eng "given password string could be null"

ER_VALIDATE_PWD_STRING_CONV_TO_LOWERCASE_FAILED
  eng "failed to convert the password string to lower case"

ER_VALIDATE_PWD_STRING_CONV_TO_BUFFER_FAILED
  eng "failed to convert the password string into a buffer"

ER_VALIDATE_PWD_STRING_HANDLER_MEM_ALLOCATION_FAILED
  eng "memory allocation failed for string handler"

ER_VALIDATE_PWD_STRONG_POLICY_DICT_FILE_UNSPECIFIED
  eng "Since the validate_password_policy is mentioned as Strong, dictionary file must be specified"

ER_VALIDATE_PWD_CONVERT_TO_BUFFER_FAILED
  eng "convert_to_buffer service failed"

ER_VALIDATE_PWD_VARIABLE_REGISTRATION_FAILED
  eng "%s variable registration failed."

ER_VALIDATE_PWD_VARIABLE_UNREGISTRATION_FAILED
  eng "%s variable unregistration failed."

ER_KEYRING_MIGRATION_EXTRA_OPTIONS
  eng "Please specify options specific to keyring migration. Any additional options can be ignored. NOTE: Although some options are valid, migration tool can still report error example: plugin variables for which plugin is not loaded yet."

OBSOLETE_ER_INVALID_DEFAULT_UTF8MB4_COLLATION
  eng "Invalid default collation %s: utf8mb4_0900_ai_ci or utf8mb4_general_ci expected"

ER_IB_MSG_0
  eng "%s"

ER_IB_MSG_1
  eng "%s"

ER_IB_MSG_2
  eng "%s"

ER_IB_MSG_3
  eng "%s"

ER_IB_MSG_4
  eng "%s"

ER_IB_MSG_5
  eng "%s"

ER_IB_MSG_6
  eng "%s"

ER_IB_MSG_7
  eng "%s"

ER_IB_MSG_8
  eng "%s"

ER_IB_MSG_9
  eng "%s"

ER_IB_MSG_10
  eng "%s"

ER_IB_MSG_11
  eng "%s"

ER_IB_MSG_12
  eng "%s"

ER_IB_MSG_13
  eng "%s"

ER_IB_MSG_14
  eng "%s"

ER_IB_MSG_15
  eng "%s"

ER_IB_MSG_16
  eng "%s"

ER_IB_MSG_17
  eng "%s"

ER_IB_MSG_18
  eng "%s"

ER_IB_MSG_19
  eng "%s"

ER_IB_MSG_20
  eng "%s"

ER_IB_MSG_21
  eng "%s"

ER_IB_MSG_22
  eng "%s"

ER_IB_MSG_23
  eng "%s"

ER_IB_MSG_24
  eng "%s"

ER_IB_MSG_25
  eng "%s"

ER_IB_MSG_26
  eng "%s"

ER_IB_MSG_27
  eng "%s"

ER_IB_MSG_28
  eng "%s"

ER_IB_MSG_29
  eng "%s"

ER_IB_MSG_30
  eng "%s"

ER_IB_MSG_31
  eng "%s"

ER_IB_MSG_32
  eng "%s"

ER_IB_MSG_33
  eng "%s"

ER_IB_MSG_34
  eng "%s"

ER_IB_MSG_35
  eng "%s"

ER_IB_MSG_36
  eng "%s"

ER_IB_MSG_37
  eng "%s"

ER_IB_MSG_38
  eng "%s"

ER_IB_MSG_39
  eng "%s"

ER_IB_MSG_40
  eng "%s"

ER_IB_MSG_41
  eng "%s"

ER_IB_MSG_42
  eng "%s"

ER_IB_MSG_43
  eng "%s"

ER_IB_MSG_44
  eng "%s"

ER_IB_MSG_45
  eng "%s"

ER_IB_MSG_46
  eng "%s"

ER_IB_MSG_47
  eng "%s"

ER_IB_MSG_48
  eng "%s"

ER_IB_MSG_49
  eng "%s"

ER_IB_MSG_50
  eng "%s"

ER_IB_MSG_51
  eng "%s"

ER_IB_MSG_52
  eng "%s"

ER_IB_MSG_53
  eng "%s"

ER_IB_MSG_54
  eng "Failed to set NUMA memory policy of buffer pool page frames with mbind(%p,%zu,%s,...,...,%s) failed with %s"

ER_IB_MSG_55
  eng "%s"

ER_IB_MSG_56
  eng "%s"

ER_IB_MSG_57
  eng "%s"

ER_IB_MSG_58
  eng "%s"

ER_IB_MSG_59
  eng "%s"

ER_IB_MSG_60
  eng "%s"

ER_IB_MSG_61
  eng "%s"

ER_IB_MSG_62
  eng "%s"

ER_IB_MSG_63
  eng "%s"

ER_IB_MSG_64
  eng "%s"

ER_IB_MSG_65
  eng "%s"

ER_IB_MSG_66
  eng "%s"

ER_IB_MSG_67
  eng "%s"

ER_IB_MSG_68
  eng "%s"

ER_IB_MSG_69
  eng "%s"

ER_IB_MSG_70
  eng "%s"

ER_IB_MSG_71
  eng "%s"

ER_IB_MSG_72
  eng "%s"

ER_IB_MSG_73
  eng "%s"

ER_IB_MSG_74
  eng "%s"

ER_IB_MSG_75
  eng "%s"

ER_IB_MSG_76
  eng "%s"

ER_IB_MSG_77
  eng "%s"

ER_IB_MSG_78
  eng "%s"

ER_IB_MSG_79
  eng "%s"

ER_IB_MSG_80
  eng "%s"

ER_IB_MSG_81
  eng "%s"

ER_IB_MSG_82
  eng "%s"

ER_IB_MSG_83
  eng "%s"

ER_IB_MSG_84
  eng "%s"

ER_IB_MSG_85
  eng "%s"

ER_IB_MSG_86
  eng "%s"

OBSOLETE_ER_IB_MSG_87
  eng "%s"

OBSOLETE_ER_IB_MSG_88
  eng "%s"

OBSOLETE_ER_IB_MSG_89
  eng "%s"

OBSOLETE_ER_IB_MSG_90
  eng "%s"

OBSOLETE_ER_IB_MSG_91
  eng "%s"

OBSOLETE_ER_IB_MSG_92
  eng "%s"

OBSOLETE_ER_IB_MSG_93
  eng "%s"

OBSOLETE_ER_IB_MSG_94
  eng "%s"

ER_IB_MSG_95
  eng "%s"

ER_IB_MSG_96
  eng "%s"

ER_IB_MSG_97
  eng "%s"

ER_IB_MSG_98
  eng "%s"

ER_IB_MSG_99
  eng "%s"

ER_IB_MSG_100
  eng "%s"

ER_IB_MSG_101
  eng "%s"

ER_IB_MSG_102
  eng "%s"

ER_IB_MSG_103
  eng "%s"

ER_IB_MSG_104
  eng "%s"

ER_IB_MSG_105
  eng "%s"

ER_IB_MSG_106
  eng "%s"

ER_IB_MSG_107
  eng "%s"

ER_IB_MSG_108
  eng "%s"

ER_IB_MSG_109
  eng "%s"

ER_IB_MSG_110
  eng "%s"

ER_IB_MSG_111
  eng "%s"

ER_IB_MSG_112
  eng "%s"

OBSOLETE_ER_IB_MSG_113
  eng "%s"

OBSOLETE_ER_IB_MSG_114
  eng "%s"

OBSOLETE_ER_IB_MSG_115
  eng "%s"

OBSOLETE_ER_IB_MSG_116
  eng "%s"

OBSOLETE_ER_IB_MSG_117
  eng "%s"

OBSOLETE_ER_IB_MSG_118
  eng "%s"

ER_IB_MSG_119
  eng "%s"

ER_IB_MSG_120
  eng "%s"

ER_IB_MSG_121
  eng "%s"

ER_IB_MSG_122
  eng "%s"

ER_IB_MSG_123
  eng "%s"

ER_IB_MSG_124
  eng "%s"

ER_IB_MSG_125
  eng "%s"

ER_IB_MSG_126
  eng "%s"

ER_IB_MSG_127
  eng "%s"

ER_IB_MSG_128
  eng "%s"

ER_IB_MSG_129
  eng "%s"

ER_IB_MSG_130
  eng "%s"

ER_IB_MSG_131
  eng "%s"

ER_IB_MSG_132
  eng "%s"

ER_IB_MSG_133
  eng "%s"

ER_IB_MSG_134
  eng "%s"

ER_IB_MSG_135
  eng "%s"

ER_IB_MSG_136
  eng "%s"

ER_IB_MSG_137
  eng "%s"

ER_IB_MSG_138
  eng "%s"

ER_IB_MSG_139
  eng "%s"

ER_IB_MSG_140
  eng "%s"

ER_IB_MSG_141
  eng "%s"

ER_IB_MSG_142
  eng "%s"

ER_IB_MSG_143
  eng "%s"

ER_IB_MSG_144
  eng "%s"

ER_IB_MSG_145
  eng "%s"

ER_IB_MSG_146
  eng "%s"

ER_IB_MSG_147
  eng "%s"

ER_IB_MSG_148
  eng "%s"

ER_IB_CLONE_INTERNAL
  eng "%s"

ER_IB_CLONE_TIMEOUT
  eng "%s"

ER_IB_CLONE_STATUS_FILE
  eng "%s"

ER_IB_CLONE_SQL
  eng "%s"

ER_IB_CLONE_VALIDATE
  eng "%s"

ER_IB_CLONE_PUNCH_HOLE
  eng "%s"

ER_IB_CLONE_GTID_PERSIST
  eng "%s"

ER_IB_MSG_156
  eng "%s"

ER_IB_MSG_157
  eng "%s"

ER_IB_MSG_158
  eng "%s"

ER_IB_MSG_159
  eng "%s"

ER_IB_MSG_160
  eng "%s"

ER_IB_MSG_161
  eng "%s"

ER_IB_MSG_162
  eng "%s"

ER_IB_MSG_163
  eng "%s"

ER_IB_MSG_164
  eng "%s"

ER_IB_MSG_165
  eng "%s"

ER_IB_MSG_166
  eng "%s"

ER_IB_MSG_167
  eng "%s"

ER_IB_MSG_168
  eng "%s"

ER_IB_MSG_169
  eng "%s"

ER_IB_MSG_170
  eng "%s"

ER_IB_MSG_171
  eng "%s"

ER_IB_MSG_172
  eng "%s"

ER_IB_MSG_173
  eng "%s"

ER_IB_MSG_174
  eng "%s"

ER_IB_MSG_175
  eng "%s"

ER_IB_MSG_176
  eng "%s"

ER_IB_MSG_177
  eng "%s"

ER_IB_MSG_178
  eng "%s"

ER_IB_MSG_179
  eng "%s"

ER_IB_MSG_180
  eng "%s"

ER_IB_LONG_AHI_DISABLE_WAIT
  eng "Waited for %u secs for hash index ref_count (%zu) to drop to 0. index: \"%s\" table: \"%s\""

ER_IB_MSG_182
  eng "%s"

ER_IB_MSG_183
  eng "%s"

ER_IB_MSG_184
  eng "%s"

ER_IB_MSG_185
  eng "%s"

ER_IB_MSG_186
  eng "%s"

ER_IB_MSG_187
  eng "%s"

ER_IB_MSG_188
  eng "%s"

ER_IB_MSG_189
  eng "%s"

ER_IB_MSG_190
  eng "%s"

ER_IB_MSG_191
  eng "%s"

ER_IB_MSG_192
  eng "%s"

ER_IB_MSG_193
  eng "%s"

ER_IB_MSG_194
  eng "%s"

ER_IB_MSG_195
  eng "%s"

ER_IB_MSG_196
  eng "%s"

ER_IB_MSG_197
  eng "%s"

ER_IB_MSG_198
  eng "%s"

ER_IB_MSG_199
  eng "%s"

ER_IB_MSG_200
  eng "%s"

ER_IB_MSG_201
  eng "%s"

ER_IB_MSG_202
  eng "%s"

ER_IB_MSG_203
  eng "%s"

ER_IB_MSG_204
  eng "%s"

ER_IB_MSG_205
  eng "%s"

ER_IB_MSG_206
  eng "%s"

ER_IB_MSG_207
  eng "%s"

ER_IB_MSG_208
  eng "%s"

ER_IB_MSG_209
  eng "%s"

ER_IB_MSG_210
  eng "%s"

ER_IB_MSG_211
  eng "%s"

ER_IB_MSG_212
  eng "%s"

ER_IB_MSG_213
  eng "%s"

ER_IB_MSG_214
  eng "%s"

ER_IB_MSG_215
  eng "%s"

ER_IB_MSG_216
  eng "%s"

ER_IB_MSG_217
  eng "%s"

ER_IB_MSG_218
  eng "%s"

ER_IB_MSG_219
  eng "%s"

ER_IB_MSG_220
  eng "%s"

ER_IB_MSG_221
  eng "%s"

ER_IB_MSG_222
  eng "%s"

ER_IB_MSG_223
  eng "%s"

ER_IB_MSG_224
  eng "%s"

ER_IB_MSG_225
  eng "%s"

ER_IB_MSG_226
  eng "%s"

ER_IB_MSG_227
  eng "%s"

ER_IB_MSG_228
  eng "%s"

ER_IB_MSG_229
  eng "%s"

ER_IB_MSG_230
  eng "%s"

ER_IB_MSG_231
  eng "%s"

ER_IB_MSG_232
  eng "%s"

ER_IB_MSG_233
  eng "%s"

ER_IB_MSG_234
  eng "%s"

ER_IB_MSG_235
  eng "%s"

ER_IB_MSG_236
  eng "%s"

ER_IB_MSG_237
  eng "%s"

ER_IB_MSG_238
  eng "%s"

ER_IB_MSG_239
  eng "%s"

ER_IB_MSG_240
  eng "%s"

ER_IB_MSG_241
  eng "%s"

ER_IB_MSG_242
  eng "%s"

ER_IB_MSG_243
  eng "%s"

ER_IB_MSG_244
  eng "%s"

ER_IB_MSG_245
  eng "%s"

ER_IB_MSG_246
  eng "%s"

ER_IB_MSG_247
  eng "%s"

ER_IB_MSG_248
  eng "%s"

ER_IB_MSG_249
  eng "%s"

ER_IB_MSG_250
  eng "%s"

ER_IB_MSG_251
  eng "%s"

ER_IB_MSG_252
  eng "%s"

ER_IB_MSG_253
  eng "%s"

ER_IB_MSG_254
  eng "%s"

ER_IB_MSG_255
  eng "%s"

ER_IB_MSG_256
  eng "%s"

ER_IB_MSG_257
  eng "%s"

ER_IB_MSG_258
  eng "%s"

ER_IB_MSG_259
  eng "%s"

ER_IB_MSG_260
  eng "%s"

ER_IB_MSG_261
  eng "%s"

ER_IB_MSG_262
  eng "%s"

ER_IB_MSG_263
  eng "%s"

ER_IB_MSG_264
  eng "%s"

ER_IB_MSG_265
  eng "%s"

ER_IB_MSG_266
  eng "%s"

ER_IB_MSG_267
  eng "%s"

ER_IB_MSG_268
  eng "%s"

ER_IB_MSG_269
  eng "%s"

ER_IB_MSG_270
  eng "%s"

ER_IB_MSG_271
  eng "%s"

ER_IB_MSG_272
  eng "Table flags are 0x%lx in the data dictionary but the flags in file %s  are 0x%llx!"

ER_IB_MSG_273
  eng "Can't read encryption key from file %s!"

OBSOLETE_ER_IB_MSG_274
  eng "Cannot close file %s, because n_pending_flushes %zu"

OBSOLETE_ER_IB_MSG_275
  eng "Cannot close file %s, because modification count %lld != flush count %lld"

OBSOLETE_ER_IB_MSG_276
  eng "Cannot close file %s, because it is in use"

OBSOLETE_ER_IB_MSG_277
  eng "Open file list len in shard %zu is %llu"

ER_IB_MSG_278
  eng "Tablespace %s, waiting for IO to stop for %lld seconds"

OBSOLETE_ER_IB_MSG_279
  eng "%s"

ER_IB_MSG_280
  eng "%s"

ER_IB_MSG_281
  eng "%s"

ER_IB_MSG_282
  eng "%s"

ER_IB_MSG_283
  eng "%s"

ER_IB_MSG_284
  eng "You must raise the value of innodb_open_files in my.cnf! Remember that InnoDB keeps all redo log files and all system tablespace files open for the whole time mysqld is running, and needs to open also some .ibd files if the file-per-table storage model is used. Current open files %zu, max allowed open files %zu."

ER_IB_MSG_285
  eng "Max tablespace id is too high, %lu"

ER_IB_WARN_ACCESSING_NONEXISTINC_SPACE
  eng "Trying to access missing tablespace %lu"

ER_IB_MSG_287
  eng "Trying to close/delete tablespace '%s' but there are %lu pending operations on it."

ER_IB_MSG_288
  eng "Trying to delete/close tablespace '%s' but there are %lu flushes and %zu pending I/O's on it."

ER_IB_MSG_289
  eng "%s"

OBSOLETE_ER_IB_MSG_290
  eng "Cannot delete tablespace %lu because it is not found in the tablespace memory cache."

ER_IB_MSG_291
  eng "While deleting tablespace %lu in DISCARD TABLESPACE. File rename/delete failed: %s"

ER_IB_MSG_292
  eng "Cannot delete tablespace %lu in DISCARD TABLESPACE: %s"

ER_IB_MSG_293
  eng "Cannot rename '%s' to '%s' for space ID %lu because the source file does not exist."

ER_IB_MSG_294
  eng "Cannot rename '%s' to '%s' for space ID %lu because the target file exists. Remove the target file and try again."

ER_IB_MSG_295
  eng "Cannot rename file '%s' (space id %lu) retried %llu times. There are either pending IOs or flushes or the file is being extended."

ER_IB_MSG_296
  eng "Cannot find space id %lu in the tablespace memory cache, though the file '%s' in a rename operation should have that ID."

ER_IB_MSG_297
  eng "Rename waiting for IO to resume"

ER_IB_MSG_298
  eng "Cannot find tablespace for '%s' in the tablespace memory cache"

ER_IB_MSG_299
  eng "Cannot find tablespace for '%s' in the tablespace memory cache"

ER_IB_MSG_300
  eng "Tablespace '%s' is already in the tablespace memory cache"

ER_IB_MSG_301
  eng "Cannot create file '%s'"

ER_IB_MSG_UNEXPECTED_FILE_EXISTS
  eng "The file '%s' already exists though the corresponding table did not exist. Have you moved InnoDB .ibd files around without using the SQL commands DISCARD TABLESPACE and IMPORT TABLESPACE, or did mysqld crash in the middle of CREATE TABLE? You can resolve the problem by removing the file '%s' under the 'datadir' of MySQL."

ER_IB_MSG_303
  eng "posix_fallocate(): Failed to preallocate data for file %s, desired size %llu Operating system error number %d - %s. Check that the disk is not full or a disk quota exceeded. Make sure the file system supports this function. Refer to your operating system documentation for operating system error code information."

ER_IB_MSG_304
  eng "Could not write the first page to tablespace '%s'"

ER_IB_MSG_305
  eng "File flush of tablespace '%s' failed"

ER_IB_MSG_306
  eng "Could not find a valid tablespace file for `%s`. %s"

ER_IB_MSG_307
  eng "Ignoring data file '%s' with space ID %lu. Another data file called '%s' exists with the same space ID"

ER_IB_MSG_308
  eng "%s"

ER_IB_MSG_309
  eng "%s"

ER_IB_MSG_310
  eng "%s"

ER_IB_MSG_311
  eng "%s"

ER_IB_MSG_312
  eng "Can't set encryption information for tablespace %s!"

ER_IB_MSG_313
  eng "%s"

ER_IB_MSG_314
  eng "%s"

ER_IB_MSG_315
  eng "%s"

ER_IB_MSG_316
  eng "%s"

ER_IB_MSG_317
  eng "%s"

ER_IB_MSG_318
  eng "%s"

ER_IB_MSG_319
  eng "%s"

ER_IB_MSG_320
  eng "%s"

ER_IB_MSG_321
  eng "%s"

ER_IB_MSG_322
  eng "%s"

ER_IB_MSG_323
  eng "%s"

ER_IB_MSG_324
  eng "%s"

ER_IB_MSG_325
  eng "%s"

ER_IB_MSG_326
  eng "%s"

OBSOLETE_ER_IB_MSG_327
  eng "%s"

ER_IB_MSG_328
  eng "%s"

ER_IB_MSG_329
  eng "%s"

ER_IB_MSG_330
  eng "%s"

ER_IB_MSG_331
  eng "%s"

ER_IB_MSG_332
  eng "%s"

ER_IB_MSG_333
  eng "%s"

ER_IB_MSG_334
  eng "%s"

ER_IB_MSG_335
  eng "%s"

ER_IB_MSG_336
  eng "%s"

ER_IB_MSG_337
  eng "%s"

ER_IB_MSG_338
  eng "%s"

ER_IB_MSG_339
  eng "%s"

ER_IB_MSG_340
  eng "%s"

ER_IB_MSG_341
  eng "%s"

ER_IB_MSG_342
  eng "%s"

ER_IB_MSG_343
  eng "%s"

ER_IB_MSG_344
  eng "%s"

ER_IB_MSG_345
  eng "%s"

ER_IB_MSG_346
  eng "%s"

ER_IB_MSG_347
  eng "%s"

ER_IB_MSG_348
  eng "%s"

ER_IB_MSG_349
  eng "%s"

ER_IB_MSG_350
  eng "%s"

OBSOLETE_ER_IB_MSG_351
  eng "%s"

ER_IB_MSG_UNPROTECTED_LOCATION_ALLOWED
  eng "The datafile '%s' for tablespace %s is in an unprotected location. This file cannot be recovered after a crash until this location is added to innodb_directories."

OBSOLETE_ER_IB_MSG_353
  eng "%s"

ER_IB_MSG_354
  eng "%s"

ER_IB_MSG_355
  eng "%s"

ER_IB_MSG_356
  eng "%s"

ER_IB_MSG_357
  eng "%s"

ER_IB_MSG_358
  eng "%s"

ER_IB_MSG_359
  eng "%s"

ER_IB_MSG_360
  eng "%s"

ER_IB_MSG_361
  eng "%s"

ER_IB_MSG_362
  eng "%s"

OBSOLETE_ER_IB_MSG_363
  eng "%s"

ER_IB_MSG_364
  eng "%s"

ER_IB_MSG_365
  eng "%s"

ER_IB_MSG_IGNORE_SCAN_PATH
  eng "Scan path '%s' is ignored because %s"

ER_IB_MSG_367
  eng "%s"

ER_IB_MSG_368
  eng "%s"

ER_IB_MSG_369
  eng "%s"

ER_IB_MSG_370
  eng "%s"

ER_IB_MSG_371
  eng "%s"

ER_IB_MSG_372
  eng "%s"

ER_IB_MSG_373
  eng "%s"

ER_IB_MSG_374
  eng "%s"

ER_IB_MSG_375
  eng "%s"

ER_IB_MSG_376
  eng "%s"

ER_IB_MSG_377
  eng "%s"

ER_IB_MSG_378
  eng "%s"

ER_IB_MSG_379
  eng "%s"

ER_IB_MSG_380
  eng "%s"

ER_IB_MSG_381
  eng "%s"

ER_IB_MSG_382
  eng "%s"

ER_IB_MSG_383
  eng "%s"

ER_IB_MSG_384
  eng "%s"

ER_IB_MSG_385
  eng "%s"

ER_IB_MSG_386
  eng "%s"

ER_IB_MSG_387
  eng "%s"

ER_IB_MSG_GENERAL_TABLESPACE_UNDER_DATADIR
  eng "A general tablespace cannot be located under the datadir. Cannot open file '%s'."

ER_IB_MSG_IMPLICIT_TABLESPACE_IN_DATADIR
  eng "A file-per-table tablespace cannot be located in the datadir. Cannot open file '%s'."

ER_IB_MSG_390
  eng "%s"

ER_IB_MSG_391
  eng "%s"

ER_IB_MSG_392
  eng "%s"

ER_IB_MSG_393
  eng "%s"

ER_IB_MSG_394
  eng "%s"

ER_IB_MSG_395
  eng "%s"

ER_IB_MSG_396
  eng "%s"

ER_IB_MSG_397
  eng "%s"

ER_IB_MSG_398
  eng "%s"

ER_IB_MSG_399
  eng "%s"

OBSOLETE_ER_IB_MSG_400
  eng "%s"

ER_IB_MSG_401
  eng "%s"

ER_IB_MSG_402
  eng "%s"

ER_IB_MSG_403
  eng "%s"

ER_IB_MSG_404
  eng "%s"

ER_IB_MSG_405
  eng "%s"

ER_IB_MSG_406
  eng "%s"

ER_IB_MSG_407
  eng "%s"

ER_IB_MSG_408
  eng "%s"

ER_IB_MSG_409
  eng "%s"

ER_IB_MSG_410
  eng "%s"

ER_IB_MSG_411
  eng "%s"

ER_IB_MSG_412
  eng "%s"

ER_IB_MSG_413
  eng "%s"

ER_IB_MSG_414
  eng "%s"

ER_IB_MSG_415
  eng "%s"

ER_IB_MSG_416
  eng "%s"

ER_IB_MSG_417
  eng "%s"

ER_IB_MSG_418
  eng "%s"

ER_IB_MSG_419
  eng "%s"

ER_IB_MSG_420
  eng "%s"

ER_IB_MSG_421
  eng "%s"

ER_IB_MSG_422
  eng "%s"

ER_IB_MSG_423
  eng "%s"

ER_IB_MSG_424
  eng "%s"

ER_IB_MSG_425
  eng "%s"

ER_IB_MSG_426
  eng "%s"

ER_IB_MSG_427
  eng "%s"

ER_IB_MSG_428
  eng "%s"

ER_IB_MSG_429
  eng "%s"

ER_IB_MSG_430
  eng "%s"

ER_IB_MSG_431
  eng "%s"

ER_IB_MSG_432
  eng "%s"

ER_IB_MSG_433
  eng "%s"

ER_IB_MSG_434
  eng "%s"

ER_IB_MSG_435
  eng "%s"

ER_IB_MSG_436
  eng "%s"

ER_IB_MSG_437
  eng "%s"

ER_IB_MSG_438
  eng "%s"

ER_IB_MSG_439
  eng "%s"

ER_IB_MSG_440
  eng "%s"

ER_IB_MSG_441
  eng "%s"

ER_IB_MSG_442
  eng "%s"

ER_IB_MSG_443
  eng "%s"

ER_IB_MSG_444
  eng "%s"

ER_IB_MSG_445
  eng "%s"

ER_IB_MSG_446
  eng "%s"

ER_IB_MSG_447
  eng "%s"

ER_IB_MSG_448
  eng "%s"

ER_IB_MSG_449
  eng "%s"

ER_IB_MSG_450
  eng "%s"

ER_IB_MSG_451
  eng "%s"

ER_IB_MSG_452
  eng "%s"

ER_IB_MSG_453
  eng "%s"

ER_IB_MSG_454
  eng "%s"

ER_IB_MSG_455
  eng "%s"

ER_IB_MSG_456
  eng "%s"

ER_IB_MSG_457
  eng "%s"

ER_IB_MSG_458
  eng "%s"

ER_IB_MSG_459
  eng "%s"

ER_IB_MSG_460
  eng "%s"

ER_IB_MSG_461
  eng "%s"

ER_IB_MSG_462
  eng "%s"

ER_IB_MSG_463
  eng "%s"

ER_IB_MSG_464
  eng "%s"

ER_IB_MSG_465
  eng "%s"

ER_IB_MSG_466
  eng "%s"

ER_IB_MSG_467
  eng "%s"

ER_IB_MSG_468
  eng "%s"

ER_IB_MSG_469
  eng "%s"

ER_IB_MSG_470
  eng "%s"

ER_IB_MSG_471
  eng "%s"

ER_IB_MSG_472
  eng "%s"

ER_IB_MSG_473
  eng "%s"

ER_IB_MSG_474
  eng "%s"

ER_IB_MSG_475
  eng "%s"

ER_IB_MSG_476
  eng "%s"

ER_IB_MSG_477
  eng "%s"

ER_IB_MSG_478
  eng "%s"

ER_IB_MSG_479
  eng "%s"

ER_IB_MSG_480
  eng "%s"

ER_IB_MSG_481
  eng "%s"

ER_IB_MSG_482
  eng "%s"

ER_IB_MSG_483
  eng "%s"

ER_IB_MSG_484
  eng "%s"

ER_IB_MSG_485
  eng "%s"

ER_IB_MSG_486
  eng "%s"

ER_IB_MSG_487
  eng "%s"

ER_IB_MSG_488
  eng "%s"

ER_IB_MSG_489
  eng "%s"

ER_IB_MSG_490
  eng "%s"

ER_IB_MSG_491
  eng "%s"

ER_IB_MSG_492
  eng "%s"

ER_IB_MSG_493
  eng "%s"

ER_IB_MSG_494
  eng "%s"

ER_IB_MSG_495
  eng "%s"

ER_IB_MSG_496
  eng "%s"

ER_IB_MSG_497
  eng "%s"

ER_IB_MSG_498
  eng "%s"

ER_IB_MSG_499
  eng "%s"

ER_IB_MSG_500
  eng "%s"

ER_IB_MSG_501
  eng "%s"

ER_IB_MSG_502
  eng "%s"

ER_IB_MSG_503
  eng "%s"

ER_IB_MSG_504
  eng "%s"

ER_IB_MSG_505
  eng "%s"

ER_IB_MSG_506
  eng "%s"

ER_IB_MSG_507
  eng "%s"

ER_IB_MSG_508
  eng "%s"

ER_IB_MSG_509
  eng "%s"

ER_IB_MSG_510
  eng "%s"

ER_IB_MSG_511
  eng "%s"

ER_IB_MSG_512
  eng "%s"

ER_IB_MSG_513
  eng "%s"

ER_IB_MSG_514
  eng "%s"

ER_IB_MSG_515
  eng "%s"

ER_IB_MSG_516
  eng "%s"

ER_IB_MSG_517
  eng "%s"

ER_IB_MSG_518
  eng "%s"

ER_IB_MSG_519
  eng "%s"

ER_IB_MSG_520
  eng "%s"

ER_IB_MSG_521
  eng "%s"

ER_IB_MSG_522
  eng "%s"

ER_IB_MSG_523
  eng "%s"

ER_IB_MSG_524
  eng "%s"

ER_IB_MSG_525
  eng "%s"

ER_IB_MSG_526
  eng "%s"

ER_IB_MSG_527
  eng "%s"

OBSOLETE_ER_IB_MSG_528
  eng "%s"

OBSOLETE_ER_IB_MSG_529
  eng "%s"

ER_IB_MSG_530
  eng "%s"

ER_IB_MSG_531
  eng "%s"

ER_IB_MSG_532
  eng "%s"

ER_IB_MSG_533
  eng "%s"

ER_IB_MSG_534
  eng "%s"

OBSOLETE_ER_IB_MSG_535
  eng "%s"

OBSOLETE_ER_IB_MSG_536
  eng "%s"

ER_IB_MSG_537
  eng "%s"

ER_IB_MSG_538
  eng "%s"

ER_IB_MSG_539
  eng "%s"

ER_IB_MSG_540
  eng "%s"

ER_IB_MSG_541
  eng "%s"

ER_IB_MSG_542
  eng "%s"

ER_IB_MSG_543
  eng "%s"

ER_IB_MSG_544
  eng "%s"

ER_IB_MSG_545
  eng "%s"

ER_IB_MSG_546
  eng "%s"

ER_IB_MSG_547
  eng "%s"

ER_IB_MSG_548
  eng "%s"

ER_IB_MSG_549
  eng "%s"

ER_IB_MSG_550
  eng "%s"

ER_IB_MSG_551
  eng "%s"

ER_IB_MSG_552
  eng "%s"

ER_IB_MSG_553
  eng "%s"

ER_IB_MSG_554
  eng "%s"

ER_IB_MSG_555
  eng "%s"

ER_IB_MSG_556
  eng "%s"

ER_IB_MSG_557
  eng "%s"

ER_IB_MSG_558
  eng "%s"

ER_IB_MSG_559
  eng "%s"

ER_IB_MSG_560
  eng "%s"

ER_IB_MSG_561
  eng "%s"

ER_IB_MSG_562
  eng "%s"

ER_IB_MSG_563
  eng "%s"

ER_IB_MSG_564
  eng "%s"

ER_IB_MSG_INVALID_LOCATION_FOR_TABLE
  eng "Cannot create a tablespace for table %s because the directory is not a valid location. %s"

ER_IB_MSG_566
  eng "%s"

ER_IB_MSG_567
  eng "%s"

ER_IB_MSG_568
  eng "%s"

ER_IB_MSG_569
  eng "%s"

ER_IB_MSG_570
  eng "%s"

ER_IB_MSG_571
  eng "%s"

OBSOLETE_ER_IB_MSG_572
  eng "%s"

ER_IB_MSG_573
  eng "%s"

ER_IB_MSG_574
  eng "%s"

OBSOLETE_ER_IB_MSG_575
  eng "%s"

OBSOLETE_ER_IB_MSG_576
  eng "%s"

OBSOLETE_ER_IB_MSG_577
  eng "%s"

ER_IB_MSG_578
  eng "%s"

ER_IB_MSG_579
  eng "%s"

ER_IB_MSG_580
  eng "%s"

ER_IB_MSG_581
  eng "%s"

ER_IB_MSG_582
  eng "%s"

ER_IB_MSG_583
  eng "%s"

ER_IB_MSG_584
  eng "%s"

ER_IB_MSG_585
  eng "%s"

ER_IB_MSG_586
  eng "%s"

ER_IB_MSG_587
  eng "%s"

ER_IB_MSG_588
  eng "%s"

ER_IB_MSG_589
  eng "%s"

ER_IB_MSG_590
  eng "%s"

ER_IB_MSG_591
  eng "%s"

ER_IB_MSG_592
  eng "%s"

ER_IB_MSG_593
  eng "%s"

ER_IB_MSG_594
  eng "%s"

ER_IB_MSG_595
  eng "%s"

ER_IB_MSG_596
  eng "%s"

ER_IB_MSG_597
  eng "%s"

ER_IB_MSG_598
  eng "%s"

ER_IB_MSG_599
  eng "%s"

ER_IB_MSG_600
  eng "%s"

ER_IB_MSG_601
  eng "%s"

ER_IB_MSG_602
  eng "%s"

ER_IB_MSG_603
  eng "%s"

ER_IB_MSG_604
  eng "%s"

ER_IB_MSG_605
  eng "%s"

ER_IB_MSG_606
  eng "%s"

ER_IB_MSG_607
  eng "%s"

ER_IB_MSG_608
  eng "%s"

ER_IB_MSG_609
  eng "%s"

ER_IB_MSG_610
  eng "%s"

ER_IB_MSG_611
  eng "%s"

ER_IB_MSG_612
  eng "%s"

ER_IB_MSG_613
  eng "%s"

ER_IB_MSG_614
  eng "%s"

ER_IB_MSG_615
  eng "%s"

ER_IB_MSG_616
  eng "%s"

ER_IB_MSG_617
  eng "%s"

ER_IB_MSG_618
  eng "%s"

ER_IB_MSG_619
  eng "%s"

ER_IB_MSG_620
  eng "%s"

ER_IB_MSG_621
  eng "%s"

ER_IB_MSG_622
  eng "%s"

ER_IB_MSG_623
  eng "%s"

ER_IB_MSG_624
  eng "%s"

ER_IB_MSG_625
  eng "%s"

ER_IB_MSG_626
  eng "%s"

ER_IB_MSG_627
  eng "%s"

ER_IB_MSG_628
  eng "%s"

ER_IB_MSG_629
  eng "%s"

ER_IB_MSG_630
  eng "%s"

ER_IB_MSG_631
  eng "%s"

ER_IB_MSG_632
  eng "%s"

ER_IB_MSG_633
  eng "%s"

ER_IB_MSG_634
  eng "%s"

ER_IB_MSG_635
  eng "%s"

ER_IB_MSG_636
  eng "Blocked High Priority Transaction (ID %llu, Thread ID %s) forces rollback of the blocking transaction (ID %llu - %s)."

ER_IB_MSG_637
  eng "%s"

ER_IB_MSG_638
  eng "%s"

ER_IB_MSG_639
  eng "Blocked High Priority Transaction (Thread ID %s) waking up the blocking transaction (ID %llu) by pretending it's a deadlock victim."

OBSOLETE_ER_IB_MSG_640
  eng "%s"

OBSOLETE_ER_IB_MSG_641
  eng "%s"

ER_IB_MSG_642
  eng "%s"

ER_IB_MSG_643
  eng "%s"

ER_IB_MSG_644
  eng "%s"

ER_IB_MSG_645
  eng "%s"

ER_IB_MSG_646
  eng "%s"

ER_IB_MSG_647
  eng "%s"

ER_IB_MSG_648
  eng "%s"

ER_IB_MSG_649
  eng "%s"

ER_IB_MSG_650
  eng "%s"

ER_IB_MSG_651
  eng "%s"

ER_IB_MSG_652
  eng "%s"

ER_IB_MSG_DDL_LOG_DELETE_BY_ID_OK
  eng "%s"

ER_IB_MSG_654
  eng "%s"

ER_IB_MSG_655
  eng "%s"

ER_IB_MSG_656
  eng "%s"

ER_IB_MSG_657
  eng "%s"

ER_IB_MSG_658
  eng "%s"

ER_IB_MSG_659
  eng "%s"

ER_IB_MSG_660
  eng "%s"

ER_IB_MSG_661
  eng "%s"

ER_IB_MSG_662
  eng "%s"

ER_IB_MSG_663
  eng "%s"

OBSOLETE_ER_IB_MSG_664
  eng "The transaction log size is too large for innodb_log_buffer_size (%lu >= %lu / 2). Trying to extend it."

OBSOLETE_ER_IB_MSG_665
  eng "innodb_log_buffer_size was extended to %lu bytes."

OBSOLETE_ER_IB_MSG_666
  eng "The transaction log files are too small for the single transaction log (size=%lu). So, the last checkpoint age might exceed the log group capacity %llu."

OBSOLETE_ER_IB_MSG_667
  eng "The age of the last checkpoint is %llu, which exceeds the log group capacity %llu."

OBSOLETE_ER_IB_MSG_668
  eng "Cannot continue operation. ib_logfiles are too small for innodb_thread_concurrency %lu. The combined size of ib_logfiles should be bigger than 200 kB * innodb_thread_concurrency. To get mysqld to start up, set innodb_thread_concurrency in my.cnf to a lower value, for example, to 8. After an ERROR-FREE shutdown of mysqld you can adjust the size of ib_logfiles. %s"

OBSOLETE_ER_IB_MSG_669
  eng "Redo log was encrypted, but keyring plugin is not loaded."

OBSOLETE_ER_IB_MSG_670
  eng "Read redo log encryption metadata successful."

OBSOLETE_ER_IB_MSG_671
  eng "Can't set redo log tablespace encryption metadata."

OBSOLETE_ER_IB_MSG_672
  eng "Cannot read the encryption information in log file header, please check if keyring plugin loaded and the key file exists."

OBSOLETE_ER_IB_MSG_673
  eng "Can't set redo log tablespace to be encrypted in read-only mode."

OBSOLETE_ER_IB_MSG_674
  eng "Can't set redo log tablespace to be encrypted."

OBSOLETE_ER_IB_MSG_675
  eng "Can't set redo log tablespace to be encrypted."

OBSOLETE_ER_IB_MSG_676
  eng "Redo log encryption is enabled."

OBSOLETE_ER_IB_MSG_677
  eng "Flush waiting for archiver to catch up lag LSN: %llu"

OBSOLETE_ER_IB_MSG_678
  eng "Flush overwriting data to archive - wait too long (1 minute) lag LSN: %llu"

OBSOLETE_ER_IB_MSG_679
  eng "%s"

OBSOLETE_ER_IB_MSG_680
  eng "Starting shutdown..."

OBSOLETE_ER_IB_MSG_681
  eng "Waiting for %s to exit"

OBSOLETE_ER_IB_MSG_682
  eng "Waiting for %lu active transactions to finish"

OBSOLETE_ER_IB_MSG_683
  eng "Waiting for master thread to be suspended"

OBSOLETE_ER_IB_MSG_684
  eng "Waiting for page_cleaner to finish flushing of buffer pool"

OBSOLETE_ER_IB_MSG_685
  eng "Pending checkpoint_writes: %lu. Pending log flush writes: %lu."

OBSOLETE_ER_IB_MSG_686
  eng "Waiting for %lu buffer page I/Os to complete"

OBSOLETE_ER_IB_MSG_687
  eng "MySQL has requested a very fast shutdown without flushing the InnoDB buffer pool to data files. At the next mysqld startup InnoDB will do a crash recovery!"

OBSOLETE_ER_IB_MSG_688
  eng "Background thread %s woke up during shutdown"

OBSOLETE_ER_IB_MSG_689
  eng "Waiting for archiver to finish archiving page and log"

OBSOLETE_ER_IB_MSG_690
  eng "Background thread %s woke up during shutdown"

OBSOLETE_ER_IB_MSG_691
  eng "Waiting for dirty buffer pages to be flushed"

OBSOLETE_ER_IB_MSG_692
  eng "Log sequence number at shutdown %llu is lower than at startup %llu!"

OBSOLETE_ER_IB_MSG_693
  eng "Waiting for archiver to finish archiving page and log"

ER_IB_MSG_694
  eng "############### CORRUPT LOG RECORD FOUND ###############"

ER_IB_MSG_695
  eng "Log record type %d, page %lu:%lu. Log parsing proceeded successfully up to %llu. Previous log record type %d, is multi %llu Recv offset %zd, prev %llu"

ER_IB_MSG_696
  eng "Hex dump starting %llu bytes before and ending %llu bytes after the corrupted record:"

ER_IB_MSG_697
  eng "Set innodb_force_recovery to ignore this error."

ER_IB_MSG_LOG_CORRUPT
  eng "The redo log file may have been corrupt and it is possible that the log scan did not proceed far enough in recovery! Please run CHECK TABLE on your InnoDB tables to check that they are ok! If mysqld crashes after this recovery; %s"

ER_IB_MSG_699
  eng "%llu pages with log records were left unprocessed!"

ER_IB_MSG_LOG_FORMAT_OLD_AND_LOG_CORRUPTED
  eng "Upgrade after a crash is not supported. This redo log was created with %s, and it appears corrupted. Please follow the instructions at %s"

ER_IB_MSG_LOG_FORMAT_OLD_AND_NO_CLEAN_SHUTDOWN
  eng "Upgrade is not supported after a crash or shutdown with innodb_fast_shutdown = 2. This redo log was created with %s, and it appears logically non empty. Please follow the instructions at %s"

OBSOLETE_ER_IB_MSG_702
  eng "%s"

OBSOLETE_ER_IB_MSG_703
  eng "%s"

ER_IB_MSG_LOG_FORMAT_BEFORE_8_0_30
  eng "Redo log format is v%lu. The redo log was created before MySQL 8.0.30."

ER_IB_MSG_LOG_FILE_FORMAT_UNKNOWN
  eng "Unknown redo log format (v%lu) in file %s. Please follow the instructions at %s"

ER_IB_MSG_RECOVERY_CHECKPOINT_NOT_FOUND
  eng "No valid checkpoint found (corrupted redo log). You can try --innodb-force-recovery=6 as a last resort."

ER_IB_MSG_707
  eng "Applying a batch of %llu redo log records ..."

ER_IB_MSG_708
  eng "%s"

ER_IB_MSG_709
  eng "%s"

ER_IB_MSG_710
  eng "Apply batch completed!"

ER_IB_MSG_711
  eng "%s"

ER_IB_MSG_712
  eng "%s"

ER_IB_MSG_713
  eng "%s"

ER_IB_MSG_714
  eng "%s"

ER_IB_MSG_715
  eng "%s"

ER_IB_MSG_716
  eng "%s"

ER_IB_MSG_717
  eng "An optimized(without redo logging) DDL operation has been performed. All modified pages may not have been flushed to the disk yet.\nThis offline backup may not be consistent"

ER_IB_MSG_718
  eng "Extending tablespace : %lu space name: %s to new size: %lu pages during recovery."

ER_IB_MSG_719
  eng "Could not extend tablespace: %lu space name: %s to new size: %lu pages during recovery."

ER_IB_MSG_720
  eng "Log block %lu at lsn %llu has valid header, but checksum field contains %lu, should be %lu."

ER_IB_MSG_RECOVERY_SKIPPED_IN_READ_ONLY_MODE
  eng "Recovery skipped, --innodb-read-only set!"

ER_IB_MSG_722
  eng "Log scan progressed past the checkpoint LSN %llu."

ER_IB_MSG_723
  eng "Log parsing buffer overflow. Recovery may have failed! Please set log_buffer_size to a value higher than %lu."

ER_IB_MSG_724
  eng "Set innodb_force_recovery to ignore this error.";

ER_IB_MSG_725
  eng "Doing recovery: scanned up to log sequence number %llu"

ER_IB_MSG_726
  eng "Database was not shutdown normally!"

ER_IB_MSG_727
  eng "Starting crash recovery."

ER_IB_MSG_728
  eng "The user has set SRV_FORCE_NO_LOG_REDO on, skipping log redo"

ER_IB_MSG_LOG_FILES_CREATED_BY_MEB_AND_READ_ONLY_MODE
  eng "Cannot restore from mysqlbackup, InnoDB running in read-only mode!"

ER_IB_MSG_LOG_FILES_CREATED_BY_MEB
  eng "The redo log file was created by mysqlbackup --apply-log at %s. The following crash recovery is part of a normal restore."

ER_IB_MSG_LOG_FILES_CREATED_BY_CLONE
  eng "Opening cloned database"

ER_IB_MSG_LOG_FORMAT_OLD
  eng "Redo log is from an earlier version, v%lu."

OBSOLETE_ER_IB_MSG_LOG_FORMAT_NOT_SUPPORTED
  eng "Redo log format v%lu not supported. Current supported format is v%lu."

ER_IB_MSG_RECOVERY_CHECKPOINT_FROM_BEFORE_CLEAN_SHUTDOWN
  eng "Are you sure you are using the right redo log files to start up the database? Log sequence number in the redo log files is %llu, less than the log sequence number in the first system tablespace file header, %llu."

ER_IB_MSG_RECOVERY_IS_NEEDED
  eng "The log sequence number %llu in the system tablespace does not match the log sequence number %llu in the redo log files!"

ER_IB_MSG_RECOVERY_IN_READ_ONLY
  eng "Can't initiate database recovery, running in read-only-mode."

ER_IB_MSG_737
  eng "We scanned the log up to %llu. A checkpoint was at %llu and the maximum LSN on a database page was %llu. It is possible that the database is now corrupt!"

ER_IB_MSG_738
  eng "Waiting for recv_writer to finish flushing of buffer pool"

ER_IB_MSG_739
  eng "Recovery parsing buffer extended to %zu."

ER_IB_MSG_740
  eng "Out of memory while resizing recovery parsing buffer."

ER_IB_MSG_741
  eng "%s"

ER_IB_MSG_742
  eng "%s"

ER_IB_MSG_743
  eng "%s"

ER_IB_MSG_744
  eng "%s"

ER_IB_MSG_745
  eng "%s"

ER_IB_MSG_746
  eng "%s"

ER_IB_MSG_747
  eng "%s"

ER_IB_MSG_748
  eng "%s"

ER_IB_MSG_749
  eng "%s"

ER_IB_MSG_750
  eng "%s"

ER_IB_MSG_751
  eng "%s"

ER_IB_MSG_752
  eng "%s"

ER_IB_MSG_753
  eng "%s"

ER_IB_MSG_754
  eng "%s"

ER_IB_MSG_755
  eng "%s"

ER_IB_MSG_756
  eng "%s"

ER_IB_MSG_757
  eng "%s"

ER_IB_MSG_758
  eng "%s"

ER_IB_MSG_759
  eng "%s"

ER_IB_MSG_760
  eng "%s"

ER_IB_MSG_761
  eng "%s"

ER_IB_MSG_762
  eng "%s"

ER_IB_MSG_763
  eng "%s"

ER_IB_MSG_764
  eng "%s"

ER_IB_MSG_765
  eng "%s"

ER_IB_MSG_766
  eng "%s"

ER_IB_MSG_767
  eng "%s"

ER_IB_MSG_768
  eng "%s"

ER_IB_MSG_769
  eng "%s"

ER_IB_MSG_770
  eng "%s"

ER_IB_MSG_771
  eng "%s"

ER_IB_MSG_772
  eng "%s"

ER_IB_MSG_773
  eng "%s"

ER_IB_MSG_774
  eng "%s"

ER_IB_MSG_775
  eng "%s"

ER_IB_MSG_776
  eng "%s"

ER_IB_MSG_777
  eng "%s"

ER_IB_MSG_778
  eng "%s"

ER_IB_MSG_779
  eng "%s"

ER_IB_MSG_780
  eng "%s"

ER_IB_MSG_781
  eng "%s"

ER_IB_MSG_782
  eng "%s"

ER_IB_MSG_783
  eng "%s"

ER_IB_MSG_784
  eng "%s"

ER_IB_MSG_785
  eng "%s"

ER_IB_MSG_786
  eng "%s"

ER_IB_MSG_787
  eng "%s"

ER_IB_MSG_788
  eng "%s"

ER_IB_MSG_789
  eng "%s"

ER_IB_MSG_790
  eng "%s"

ER_IB_MSG_791
  eng "%s"

ER_IB_MSG_792
  eng "%s"

ER_IB_MSG_793
  eng "%s"

ER_IB_MSG_794
  eng "%s"

ER_IB_MSG_795
  eng "%s"

ER_IB_MSG_796
  eng "%s"

ER_IB_MSG_797
  eng "%s"

ER_IB_MSG_798
  eng "%s"

ER_IB_MSG_799
  eng "%s"

ER_IB_MSG_800
  eng "%s"

ER_IB_MSG_801
  eng "%s"

ER_IB_MSG_802
  eng "%s"

ER_IB_MSG_803
  eng "%s"

ER_IB_MSG_804
  eng "%s"

ER_IB_MSG_805
  eng "%s"

ER_IB_MSG_806
  eng "%s"

ER_IB_MSG_807
  eng "%s"

ER_IB_MSG_808
  eng "%s"

ER_IB_MSG_809
  eng "%s"

ER_IB_MSG_810
  eng "%s"

ER_IB_MSG_811
  eng "%s"

ER_IB_MSG_812
  eng "%s"

ER_IB_MSG_813
  eng "%s"

ER_IB_MSG_814
  eng "%s"

ER_IB_MSG_815
  eng "%s"

ER_IB_MSG_816
  eng "%s"

ER_IB_MSG_817
  eng "%s"

ER_IB_MSG_818
  eng "%s"

ER_IB_MSG_819
  eng "%s"

ER_IB_MSG_820
  eng "%s"

ER_IB_MSG_821
  eng "%s"

ER_IB_MSG_822
  eng "%s"

ER_IB_MSG_823
  eng "%s"

ER_IB_MSG_824
  eng "%s"

ER_IB_MSG_825
  eng "%s"

ER_IB_MSG_826
  eng "%s"

ER_IB_MSG_827
  eng "%s"

ER_IB_MSG_828
  eng "%s"

ER_IB_MSG_829
  eng "%s"

ER_IB_MSG_830
  eng "%s"

ER_IB_MSG_831
  eng "%s"

ER_IB_MSG_832
  eng "%s"

ER_IB_MSG_833
  eng "%s"

ER_IB_MSG_834
  eng "%s"

ER_IB_MSG_835
  eng "%s"

ER_IB_MSG_836
  eng "%s"

ER_IB_MSG_837
  eng "%s"

ER_IB_MSG_838
  eng "%s"

ER_IB_MSG_839
  eng "%s"

ER_IB_MSG_840
  eng "%s"

ER_IB_MSG_841
  eng "%s"

ER_IB_MSG_842
  eng "%s"

ER_IB_MSG_CANT_ENCRYPT_REDO_LOG_DATA
  eng "Can't encrypt data of redo log"

ER_IB_MSG_844
  eng "%s"

ER_IB_MSG_845
  eng "%s"

ER_IB_MSG_846
  eng "%s"

ER_IB_MSG_847
  eng "%s"

ER_IB_MSG_848
  eng "%s"

ER_IB_MSG_849
  eng "%s"

ER_IB_MSG_850
  eng "%s"

ER_IB_MSG_851
  eng "%s"

ER_IB_MSG_852
  eng "%s"

ER_IB_MSG_853
  eng "%s"

ER_IB_MSG_854
  eng "%s"

ER_IB_MSG_855
  eng "%s"

ER_IB_MSG_856
  eng "%s"

ER_IB_MSG_857
  eng "%s"

ER_IB_MSG_858
  eng "%s"

ER_IB_MSG_859
  eng "%s"

ER_IB_MSG_860
  eng "%s"

ER_IB_MSG_861
  eng "%s"

ER_IB_MSG_862
  eng "%s"

ER_IB_MSG_863
  eng "%s"

ER_IB_MSG_864
  eng "%s"

ER_IB_MSG_865
  eng "%s"

ER_IB_MSG_866
  eng "%s"

ER_IB_MSG_867
  eng "%s"

ER_IB_MSG_868
  eng "%s"

ER_IB_MSG_869
  eng "%s"

ER_IB_MSG_870
  eng "%s"

ER_IB_MSG_871
  eng "%s"

ER_IB_MSG_872
  eng "%s"

ER_IB_MSG_873
  eng "%s"

ER_IB_MSG_874
  eng "%s"

ER_IB_MSG_875
  eng "%s"

ER_IB_MSG_876
  eng "%s"

ER_IB_MSG_877
  eng "%s"

ER_IB_MSG_878
  eng "%s"

ER_IB_MSG_879
  eng "%s"

ER_IB_MSG_880
  eng "%s"

ER_IB_MSG_881
  eng "%s"

ER_IB_MSG_882
  eng "%s"

ER_IB_MSG_883
  eng "%s"

ER_IB_MSG_884
  eng "%s"

ER_IB_MSG_885
  eng "%s"

ER_IB_MSG_886
  eng "%s"

ER_IB_MSG_887
  eng "%s"

ER_IB_MSG_888
  eng "%s"

ER_IB_MSG_889
  eng "%s"

ER_IB_MSG_890
  eng "%s"

ER_IB_MSG_891
  eng "%s"

ER_IB_MSG_892
  eng "%s"

ER_IB_MSG_893
  eng "%s"

ER_IB_MSG_894
  eng "%s"

ER_IB_MSG_895
  eng "%s"

ER_IB_MSG_896
  eng "%s"

ER_IB_MSG_897
  eng "%s"

ER_IB_MSG_898
  eng "%s"

ER_IB_MSG_899
  eng "%s"

ER_IB_MSG_900
  eng "%s"

ER_IB_MSG_901
  eng "%s"

ER_IB_MSG_902
  eng "%s"

ER_IB_MSG_903
  eng "%s"

ER_IB_MSG_904
  eng "%s"

ER_IB_MSG_905
  eng "%s"

ER_IB_MSG_906
  eng "%s"

ER_IB_MSG_907
  eng "%s"

ER_IB_MSG_908
  eng "%s"

ER_IB_MSG_909
  eng "%s"

ER_IB_MSG_910
  eng "%s"

ER_IB_MSG_911
  eng "%s"

ER_IB_MSG_912
  eng "%s"

ER_IB_MSG_913
  eng "%s"

ER_IB_MSG_914
  eng "%s"

ER_IB_MSG_915
  eng "%s"

ER_IB_MSG_916
  eng "%s"

ER_IB_MSG_917
  eng "%s"

ER_IB_MSG_918
  eng "%s"

ER_IB_MSG_919
  eng "%s"

ER_IB_MSG_920
  eng "%s"

ER_IB_MSG_921
  eng "%s"

ER_IB_MSG_922
  eng "%s"

ER_IB_MSG_923
  eng "%s"

ER_IB_MSG_924
  eng "%s"

ER_IB_MSG_925
  eng "%s"

ER_IB_MSG_926
  eng "%s"

ER_IB_MSG_927
  eng "%s"

ER_IB_MSG_928
  eng "%s"

ER_IB_MSG_929
  eng "%s"

ER_IB_MSG_930
  eng "%s"

ER_IB_MSG_931
  eng "%s"

ER_IB_MSG_932
  eng "%s"

ER_IB_MSG_933
  eng "%s"

ER_IB_MSG_934
  eng "%s"

ER_IB_MSG_935
  eng "%s"

ER_IB_MSG_936
  eng "%s"

ER_IB_MSG_937
  eng "%s"

ER_IB_MSG_938
  eng "%s"

ER_IB_MSG_939
  eng "%s"

ER_IB_MSG_940
  eng "%s"

ER_IB_MSG_941
  eng "%s"

ER_IB_MSG_942
  eng "%s"

ER_IB_MSG_943
  eng "%s"

ER_IB_MSG_944
  eng "%s"

ER_IB_MSG_945
  eng "%s"

ER_IB_MSG_946
  eng "%s"

ER_IB_MSG_947
  eng "%s"

ER_IB_MSG_948
  eng "%s"

ER_IB_MSG_949
  eng "%s"

ER_IB_MSG_950
  eng "%s"

ER_IB_MSG_951
  eng "%s"

ER_IB_MSG_952
  eng "%s"

ER_IB_MSG_953
  eng "%s"

ER_IB_MSG_954
  eng "%s"

ER_IB_MSG_955
  eng "%s"

ER_IB_MSG_956
  eng "%s"

ER_IB_MSG_957
  eng "%s"

ER_IB_MSG_958
  eng "%s"

ER_IB_MSG_959
  eng "%s"

ER_IB_MSG_960
  eng "%s"

ER_IB_MSG_961
  eng "%s"

ER_IB_MSG_962
  eng "%s"

ER_IB_MSG_963
  eng "%s"

ER_IB_MSG_964
  eng "%s"

ER_IB_MSG_965
  eng "%s"

ER_IB_MSG_966
  eng "%s"

ER_IB_MSG_967
  eng "%s"

ER_IB_MSG_968
  eng "%s"

ER_IB_MSG_969
  eng "%s"

ER_IB_MSG_970
  eng "%s"

ER_IB_MSG_971
  eng "%s"

ER_IB_MSG_972
  eng "%s"

ER_IB_MSG_973
  eng "%s"

ER_IB_MSG_974
  eng "%s"

ER_IB_MSG_975
  eng "%s"

ER_IB_MSG_976
  eng "%s"

ER_IB_MSG_977
  eng "%s"

ER_IB_MSG_978
  eng "%s"

ER_IB_MSG_979
  eng "%s"

ER_IB_MSG_980
  eng "%s"

ER_IB_MSG_981
  eng "%s"

ER_IB_MSG_982
  eng "%s"

ER_IB_MSG_983
  eng "%s"

ER_IB_MSG_984
  eng "%s"

ER_IB_MSG_985
  eng "%s"

ER_IB_MSG_986
  eng "%s"

ER_IB_MSG_987
  eng "%s"

ER_IB_MSG_988
  eng "%s"

ER_IB_MSG_989
  eng "%s"

ER_IB_MSG_990
  eng "%s"

ER_IB_MSG_991
  eng "%s"

ER_IB_MSG_992
  eng "%s"

ER_IB_MSG_993
  eng "%s"

ER_IB_MSG_994
  eng "%s"

ER_IB_MSG_995
  eng "%s"

ER_IB_MSG_996
  eng "%s"

ER_IB_MSG_997
  eng "%s"

ER_IB_MSG_998
  eng "%s"

ER_IB_MSG_999
  eng "%s"

ER_IB_MSG_1000
  eng "%s"

ER_IB_MSG_1001
  eng "%s"

ER_IB_MSG_1002
  eng "%s"

ER_IB_MSG_1003
  eng "%s"

ER_IB_MSG_1004
  eng "%s"

ER_IB_MSG_1005
  eng "%s"

ER_IB_MSG_1006
  eng "%s"

ER_IB_MSG_1007
  eng "%s"

ER_IB_MSG_1008
  eng "%s"

ER_IB_MSG_1009
  eng "%s"

ER_IB_MSG_1010
  eng "%s"

ER_IB_MSG_1011
  eng "%s"

ER_IB_MSG_1012
  eng "%s"

ER_IB_MSG_1013
  eng "%s"

ER_IB_MSG_1014
  eng "%s"

ER_IB_MSG_1015
  eng "%s"

ER_IB_MSG_1016
  eng "%s"

ER_IB_MSG_1017
  eng "%s"

ER_IB_MSG_1018
  eng "%s"

ER_IB_MSG_1019
  eng "%s"

ER_IB_MSG_1020
  eng "%s"

ER_IB_MSG_1021
  eng "%s"

ER_IB_MSG_1022
  eng "%s"

ER_IB_MSG_1023
  eng "%s"

ER_IB_MSG_1024
  eng "%s"

ER_IB_MSG_1025
  eng "%s"

ER_IB_MSG_1026
  eng "%s"

ER_IB_MSG_1027
  eng "%s"

ER_IB_MSG_1028
  eng "%s"

ER_IB_MSG_1029
  eng "%s"

ER_IB_MSG_1030
  eng "%s"

ER_IB_MSG_1031
  eng "%s"

ER_IB_MSG_1032
  eng "%s"

ER_IB_MSG_1033
  eng "%s"

ER_IB_MSG_1034
  eng "%s"

ER_IB_MSG_1035
  eng "%s"

ER_IB_MSG_1036
  eng "%s"

ER_IB_MSG_1037
  eng "%s"

ER_IB_MSG_1038
  eng "%s"

ER_IB_MSG_1039
  eng "%s"

ER_IB_MSG_1040
  eng "%s"

ER_IB_MSG_1041
  eng "%s"

ER_IB_MSG_1042
  eng "%s"

ER_IB_MSG_1043
  eng "%s"

ER_IB_MSG_1044
  eng "%s"

ER_IB_MSG_1045
  eng "%s"

ER_IB_MSG_1046
  eng "Old log sequence number %llu was greater than the new log sequence number %llu. Please submit a bug report to http://bugs.mysql.com"

ER_IB_MSG_1047
  eng "Semaphore wait has lasted > %llu seconds. We intentionally crash the server because it appears to be hung."

ER_IB_MSG_1048
  eng "Waiting for %llu table(s) to be dropped"

ER_IB_MSG_1049
  eng "Waiting for change buffer merge to complete number of bytes of change buffer just merged: %llu"

OBSOLETE_ER_IB_MSG_1050
  eng "Can't set undo tablespace(s) to be encrypted since --innodb_undo_tablespaces=0."

ER_IB_MSG_1051
  eng "Can't set undo tablespace(s) to be encrypted in read-only-mode."

ER_IB_MSG_1052
  eng "Can't set undo tablespace '%s' to be encrypted."

ER_IB_MSG_1053
  eng "Can't set undo tablespace '%s' to be encrypted. Failed to write header page."

ER_IB_MSG_1054
  eng "Can't set undo tablespace '%s' to be encrypted. Error %d - %s"

ER_IB_MSG_1055
  eng "Encryption is enabled for undo tablespace '%s'."

ER_IB_MSG_1056
  eng "Can't rotate encryption on undo tablespace '%s'."

ER_IB_MSG_1057
  eng "Encryption is enabled for undo tablespace '%s'."

ER_IB_MSG_1058
  eng "os_file_get_status() failed on '%s'. Can't determine file permissions."

ER_IB_MSG_1059
  eng "%s can't be opened in %s mode."

ER_IB_MSG_1060
  eng "'%s' not a regular file."

ER_IB_MSG_LOG_FILE_OS_CREATE_FAILED
  eng "Cannot create %s"

ER_IB_MSG_FILE_RESIZE
  eng "Setting file %s size to %llu MB. Progress : %u%%"

ER_IB_MSG_LOG_FILE_RESIZE_FAILED
  eng "Cannot resize redo log file %s to %llu MB (%s)"

ER_IB_MSG_LOG_FILES_CREATE_AND_READ_ONLY_MODE
  eng "Cannot create redo log files in read-only mode (--innodb-read-only)."

ER_IB_MSG_1065
  eng "Redo log encryption is enabled, but the keyring is not loaded."

ER_IB_MSG_LOG_FILE_PREPARE_ON_CREATE_FAILED
  eng "Failed to create redo log file %s (error: %d) for start LSN %llu"

OBSOLETE_ER_IB_MSG_1067
  eng "Renaming log file %s to %s"

ER_IB_MSG_LOG_FILES_INITIALIZED
  eng "New redo log files created, LSN=%llu"

ER_IB_MSG_LOG_FILE_OPEN_FAILED
  eng "Unable to open '%s' (error: %d)."

ER_IB_MSG_1070
  eng "Cannot create construction log file '%s' for undo tablespace '%s'."

ER_IB_MSG_1071
  eng "Creating UNDO Tablespace %s"

ER_IB_MSG_1072
  eng "Setting file %s size to %llu MB"

ER_IB_MSG_1073
  eng "Physically writing the file full"

ER_IB_MSG_1074
  eng "Error in creating %s: probably out of disk space"

ER_IB_MSG_1075
  eng "Can't set encryption metadata for space %s"

ER_IB_MSG_1076
  eng "Cannot read first page of '%s' - %s"

ER_IB_MSG_1077
  eng "Undo tablespace number %lu was being truncated when mysqld quit."

ER_IB_MSG_1078
  eng "Cannot recover a truncated undo tablespace in read-only mode"

ER_IB_MSG_1079
  eng "Reconstructing undo tablespace number %lu."

ER_IB_MSG_1080
  eng "Cannot create %s because %s already uses Space ID=%lu! Did you change innodb_undo_directory?"

ER_IB_MSG_1081
  eng "UNDO tablespace %s must be %s"

ER_IB_MSG_1082
  eng "Error creating file for %s"

ER_IB_MSG_1083
  eng "Error reading encryption for %s"

ER_IB_MSG_CANNOT_OPEN_57_UNDO
  eng "Unable to open undo tablespace number %lu"

ER_IB_MSG_1085
  eng "Opened %llu existing undo tablespaces."

ER_IB_MSG_1086
  eng "Cannot create undo tablespaces since innodb_%s has been set. Using %llu existing undo tablespaces."

ER_IB_MSG_1087
  eng "Cannot continue InnoDB startup in %s mode because there are no existing undo tablespaces found."

ER_IB_MSG_1088
  eng "Could not create undo tablespace '%s'."

ER_IB_MSG_1089
  eng "Error %d - %s - opening newly created undo tablespace '%s'."

ER_IB_MSG_1090
  eng "Created %llu undo tablespaces."

ER_IB_MSG_1091
  eng "Unable to create encrypted undo tablespace number %lu. please check if the keyring is initialized correctly"

ER_IB_MSG_1092
  eng "Encryption is enabled for undo tablespace number %lu."

ER_IB_MSG_1093
  eng "Unable to initialize the header page in undo tablespace number %lu."

ER_IB_MSG_1094
  eng "Cannot delete old undo tablespaces because they contain undo logs for XA PREPARED transactions."

ER_IB_MSG_1095
  eng "Upgrading %zu existing undo tablespaces that were tracked in the system tablespace to %lu new independent undo tablespaces."

ER_IB_MSG_1096
  eng "Deleting %llu new independent undo tablespaces that we just created."

ER_IB_MSG_1097
  eng "Waiting for purge to start"

ER_IB_MSG_1098
  eng "Creating shared tablespace for temporary tables"

ER_IB_MSG_1099
  eng "The %s data file must be writable!"

ER_IB_MSG_1100
  eng "Could not create the shared %s."

ER_IB_MSG_1101
  eng "Unable to create the shared %s."

ER_IB_MSG_1102
  eng "The %s data file cannot be re-opened after check_file_spec() succeeded!"

ER_IB_MSG_1103
  eng "%d threads created by InnoDB had not exited at shutdown!"

ER_IB_MSG_1104
  eng "InnoDB Database creation was aborted %swith error %s. You may need to delete the ibdata1 file before trying to start up again."

ER_IB_MSG_1105
  eng "Plugin initialization aborted %swith error %s."

ER_IB_MSG_BUF_PENDING_IO
  eng "Waiting for %zu buffer page I/Os to complete."

ER_IB_MSG_1107
  eng "PUNCH HOLE support available"

ER_IB_MSG_1108
  eng "PUNCH HOLE support not available"

ER_IB_MSG_1109
  eng "Size of InnoDB's ulint is %zu but size of void* is %zu. The sizes should be the same so that on a 64-bit platforms you can allocate more than 4 GB of memory."

ER_IB_MSG_1110
  eng "Database upgrade cannot be accomplished in read-only mode."

ER_IB_MSG_1111
  eng "Database upgrade cannot be accomplished with innodb_force_recovery > 0"

ER_IB_MSG_1112
  eng "%s"

ER_IB_MSG_1113
  eng "%s"

ER_IB_MSG_1114
  eng "%s"

ER_IB_MSG_1115
  eng "%s"

ER_IB_MSG_1116
  eng "%s"

ER_IB_MSG_1117
  eng "%s"

OBSOLETE_ER_IB_MSG_1118
  eng "%s"

ER_IB_MSG_1119
  eng "%s"

ER_IB_MSG_1120
  eng "%s"

ER_IB_MSG_1121
  eng "%s"

ER_IB_MSG_1122
  eng "MySQL was built without a memory barrier capability on this architecture, which might allow a mutex/rw_lock violation under high thread concurrency. This may cause a hang."

ER_IB_MSG_1123
  eng "Compressed tables use zlib %s"

ER_IB_MSG_1124
  eng "%s"

ER_IB_MSG_1125
  eng "Startup called second time during the process lifetime. In the MySQL Embedded Server Library you cannot call server_init() more than once during the process lifetime."

ER_IB_MSG_1126
  eng "%s"

ER_IB_MSG_1127
  eng "Unable to create monitor file %s: %s"

ER_IB_MSG_1128
  eng "Disabling background ibuf IO read threads."

ER_IB_MSG_1129
  eng "Cannot initialize AIO sub-system"

ER_IB_MSG_1130
  eng "Initializing buffer pool, total size = %lf%c, instances = %lu, chunk size =%lf%c "

ER_IB_MSG_1131
  eng "Cannot allocate memory for the buffer pool"

ER_IB_MSG_1132
  eng "Completed initialization of buffer pool"

ER_IB_MSG_1133
  eng "Small buffer pool size (%lluM), the flst_validate() debug function can cause a deadlock if the buffer pool fills up."

ER_IB_MSG_1134
  eng "Could not open or create the system tablespace. If you tried to add new data files to the system tablespace, and it failed here, you should now edit innodb_data_file_path in my.cnf back to what it was, and remove the new ibdata files InnoDB created in this failed attempt. InnoDB only wrote those files full of zeros, but did not yet use them in any way. But be careful: do not remove old data files which contain your precious data!"

ER_IB_MSG_DATA_DIRECTORY_NOT_INITIALIZED_OR_CORRUPTED
  eng "Cannot create redo log files because data files are corrupt or the database was not shut down cleanly after creating the data files."

ER_IB_MSG_LOG_FILES_INVALID_SET
  eng "Only one redo log file found"

ER_IB_MSG_LOG_FILE_SIZE_INVALID
  eng "The redo log file %s size %llu is not a multiple of innodb_page_size"

ER_IB_MSG_LOG_FILES_DIFFERENT_SIZES
  eng "The redo log file %s is of different size %llu bytes than other log files %llu bytes!"

ER_IB_MSG_1139
  eng "Use --innodb-directories to find the tablespace files. If that fails then use --innodb-force-recovery=1 to ignore this and to permanently lose all changes to the missing tablespace(s)"

ER_IB_MSG_RECOVERY_CORRUPT
  eng "The redo log file may have been corrupt and it is possible that the log scan or parsing did not proceed far enough in recovery. Please run CHECK TABLE on your InnoDB tables to check that they are ok! It may be safest to recover your InnoDB database from a backup!"

OBSOLETE_ER_IB_MSG_LOG_FILES_RESIZE_ON_START_IN_READ_ONLY_MODE
  eng "Cannot resize redo log files in read-only mode. Provide --innodb_redo_log_capacity >= %lluMB or start without --innodb-read-only."

ER_IB_MSG_1142
  eng "Cannot open DD tablespace."

ER_IB_MSG_LOG_FILES_REWRITING
  eng "Starting to delete and rewrite redo log files."

ER_IB_MSG_1144
  eng "Undo from 5.7 found. It will be purged"

ER_IB_MSG_1145
  eng "%s"

ER_IB_MSG_1146
  eng "%s"

ER_IB_MSG_1147
  eng "Tablespace size stored in header is %lu pages, but the sum of data file sizes is %lu pages"

ER_IB_MSG_1148
  eng "Cannot start InnoDB. The tail of the system tablespace is missing. Have you edited innodb_data_file_path in my.cnf in an inappropriate way, removing ibdata files from there? You can set innodb_force_recovery=1 in my.cnf to force a startup if you are trying to recover a badly corrupt database."

ER_IB_MSG_1149
  eng "Tablespace size stored in header is %lu pages, but the sum of data file sizes is only %lu pages"

ER_IB_MSG_1150
  eng "Cannot start InnoDB. The tail of the system tablespace is missing. Have you edited innodb_data_file_path in my.cnf in an InnoDB: inappropriate way, removing ibdata files from there? You can set innodb_force_recovery=1 in my.cnf to force InnoDB: a startup if you are trying to recover a badly corrupt database."

ER_IB_MSG_1151
  eng "%s started; log sequence number %llu"

ER_IB_MSG_1152
  eng "Waiting for purge to complete"

OBSOLETE_ER_IB_MSG_1153
  eng "Waiting for dict_stats_thread to exit"

ER_IB_MSG_1154
  eng "Query counter shows %lld queries still inside InnoDB at shutdown"

ER_IB_MSG_1155
  eng "Shutdown completed; log sequence number %llu"

ER_IB_MSG_1156
  eng "Cannot continue operation."

ER_IB_MSG_1157
  eng "%s"

ER_IB_MSG_1158
  eng "%s"

ER_IB_MSG_1159
  eng "%s"

ER_IB_MSG_1160
  eng "%s"

ER_IB_MSG_1161
  eng "%s"

ER_IB_MSG_1162
  eng "%s"

ER_IB_MSG_1163
  eng "%s"

ER_IB_MSG_1164
  eng "%s"

ER_IB_MSG_1165
  eng "%s"

ER_IB_MSG_UNDO_TRUNCATE_FAIL_TO_READ_LOG_FILE
  eng "Unable to read the existing undo truncate log file '%s'. The error is %s"

ER_IB_MSG_UNDO_MARKED_FOR_TRUNCATE
  eng "Undo tablespace %s is marked for truncate"

OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_MDL
  eng "%s"

ER_IB_MSG_UNDO_TRUNCATE_START
  eng "Truncating UNDO tablespace %s"

OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_DDL_LOG_START
  eng "%s"

ER_IB_MSG_UNDO_TRUNCATE_DELAY_BY_LOG_CREATE
  eng "Cannot create truncate log for undo tablespace '%s'."

OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_TRUNCATE
  eng "%s"

ER_IB_MSG_UNDO_TRUNCATE_DELAY_BY_FAILURE
  eng "Failed to truncate undo tablespace '%s'."

OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_STATE_UPDATE
  eng "%s"

ER_IB_MSG_UNDO_TRUNCATE_COMPLETE
  eng "Completed truncate of undo tablespace %s."

OBSOLETE_ER_IB_MSG_UNDO_INJECT_TRUNCATE_DONE
  eng "%s"

ER_IB_MSG_1177
  eng "%s"

ER_IB_MSG_1178
  eng "%s"

ER_IB_MSG_1179
  eng "%s"

ER_IB_MSG_1180
  eng "%s"

ER_IB_MSG_1181
  eng "%s"

ER_IB_MSG_1182
  eng "%s"

ER_IB_MSG_1183
  eng "%s"

ER_IB_MSG_1184
  eng "%s"

ER_IB_MSG_1185
  eng "%s"

ER_IB_MSG_1186
  eng "%s"

ER_IB_MSG_1187
  eng "%s"

ER_IB_MSG_1188
  eng "%s"

ER_IB_MSG_1189
  eng "%s"

ER_IB_MSG_TRX_RECOVERY_ROLLBACK_COMPLETED
  eng "Rollback of non-prepared transactions completed"

ER_IB_MSG_1191
  eng "%s"

ER_IB_MSG_1192
  eng "%s"

ER_IB_MSG_1193
  eng "%s"

ER_IB_MSG_1194
  eng "%s"

ER_IB_MSG_1195
  eng "%s"

ER_IB_MSG_1196
  eng "%s"

ER_IB_MSG_1197
  eng "%s"

ER_IB_MSG_1198
  eng "%s"

ER_IB_MSG_1199
  eng "%s"

ER_IB_MSG_1200
  eng "%s"

ER_IB_MSG_1201
  eng "%s"

ER_IB_MSG_1202
  eng "%s"

ER_IB_MSG_1203
  eng "%s"

ER_IB_MSG_1204
  eng "%s"

ER_IB_MSG_1205
  eng "%s"

ER_IB_MSG_1206
  eng "%s"

ER_IB_MSG_1207
  eng "%s"

ER_IB_MSG_1208
  eng "%s"

ER_IB_MSG_1209
  eng "%s"

ER_IB_MSG_1210
  eng "%s"

ER_IB_MSG_1211
  eng "Blocked High Priority Transaction (ID %llu, Thread ID %s) killed the blocking transaction (ID %llu - %s) by rolling it back."

ER_IB_MSG_1212
  eng "%s"

ER_IB_MSG_1213
  eng "gettimeofday() failed: %s"

ER_IB_MSG_1214
  eng "Can't create UNDO tablespace %s %s"

ER_IB_MSG_1215
  eng "%s"

ER_IB_MSG_LOG_FILES_RESIZE_ON_START
  eng "Resizing redo log from %lluM to %lluM (LSN=%llu) synchronously. If this takes too long, consider starting the server with large --innodb_redo_log_capacity, and resizing the redo log online using SET."

ER_IB_MSG_1217
  eng "%s"

ER_IB_MSG_1218
  eng "%s"

ER_IB_MSG_1219
  eng "%s"

ER_IB_MSG_1220
  eng "%s"

ER_IB_MSG_1221
  eng "%s"

ER_IB_MSG_1222
  eng "%s"

ER_IB_MSG_1223
  eng "%s"

ER_IB_MSG_1224
  eng "%s"

ER_IB_MSG_1225
  eng "%s"

ER_IB_MSG_1226
  eng "%s"

ER_IB_MSG_1227
  eng "%s"

ER_IB_MSG_1228
  eng "%s"

ER_IB_MSG_1229
  eng "%s"

OBSOLETE_ER_IB_MSG_1230
  eng "%s"

ER_IB_MSG_1231
  eng "%s"

OBSOLETE_ER_IB_MSG_1232
  eng "%s"

ER_IB_MSG_1233
  eng "%s"

ER_IB_MSG_LOG_WRITER_OUT_OF_SPACE
  eng "Out of space in the redo log. Checkpoint LSN: %llu. Consider increasing innodb_redo_log_capacity."

ER_IB_MSG_1235
  eng "%s"

ER_IB_MSG_LOG_WRITER_ABORTS_LOG_ARCHIVER
  eng "Log writer waited too long for redo-archiver to advance (1 second). There are unarchived: %llu bytes. Archiver LSN: %llu. Aborted the redo-archiver. Consider increasing innodb_redo_log_capacity."

ER_IB_MSG_LOG_WRITER_WAITING_FOR_ARCHIVER
  eng "Log writer is waiting for redo-archiver to catch up unarchived: %llu bytes. Archiver LSN: %llu. Consider increasing innodb_redo_log_capacity."

ER_IB_MSG_1238
  eng "%s"

ER_IB_MSG_1239
  eng "%s"

OBSOLETE_ER_IB_MSG_1240
  eng "%s"

ER_IB_MSG_1241
  eng "%s"

ER_IB_MSG_LOG_FILES_CANNOT_ENCRYPT_IN_READ_ONLY
  eng "Can't set redo log files to be encrypted in read-only mode."

ER_IB_MSG_LOG_FILES_ENCRYPTION_INIT_FAILED
  eng "Can't set redo log files to be encrypted."

OBSOLETE_ER_IB_MSG_1244
  eng "Can't set redo log tablespace to be encrypted."

ER_IB_MSG_1245
  eng "Redo log encryption is enabled."

ER_IB_MSG_1246
  eng "Waiting for archiver to finish archiving page and log"

ER_IB_MSG_1247
  eng "Starting shutdown..."

ER_IB_MSG_1248
  eng "Waiting for %s to exit."

ER_IB_MSG_1249
  eng "Waiting for rollback of %zu recovered transactions, before shutdown."

ER_IB_MSG_1250
  eng "Waiting for master thread to be suspended."

ER_IB_MSG_1251
  eng "Waiting for page_cleaner to finish flushing of buffer pool."

ER_IB_MSG_BUF_PENDING_IO_ON_SHUTDOWN
  eng "Shutdown is waiting for %zu buffer page I/Os to complete."

ER_IB_MSG_1253
  eng "MySQL has requested a very fast shutdown without flushing the InnoDB buffer pool to data files. At the next mysqld startup InnoDB will do a crash recovery!"

OBSOLETE_ER_IB_MSG_1254
  eng "%s"

ER_IB_MSG_1255
  eng "%s"

ER_IB_MSG_1256
  eng "%s"

ER_IB_MSG_1257
  eng "%s"

ER_IB_MSG_1258
  eng "%s"

ER_IB_MSG_1259
  eng "%s"

ER_IB_MSG_1260
  eng "%s"

ER_IB_MSG_1261
  eng "%s"

ER_IB_MSG_1262
  eng "%s"

ER_IB_MSG_1263
  eng "%s"

ER_IB_MSG_LOG_FILE_HEADER_INVALID_CHECKSUM
  eng "Invalid redo log header checksum."

ER_IB_MSG_LOG_FORMAT_BEFORE_5_7_9
  eng "Unsupported redo log format (v%lu). The redo log was created before MySQL 5.7.9"

ER_IB_MSG_1266
  eng "%s"

ER_IB_MSG_LOG_PARAMS_CONCURRENCY_MARGIN_UNSAFE
  eng "Cannot continue operation. The innodb_redo_log_capacity=%lluM is too small for the innodb_thread_concurrency=%lu. The capacity of redo should be >= %lluM. To get mysqld running, set innodb_thread_concurrency to a smaller value or increase innodb_redo_log_capacity. %s"

ER_IB_MSG_1268
  eng "%s"

ER_IB_MSG_1269
  eng "%s"

ER_IB_MSG_THREAD_CONCURRENCY_CHANGED
  eng "User has set innodb_thread_concurrency to %lu."

ER_RPL_REPLICA_SQL_THREAD_STOP_CMD_EXEC_TIMEOUT
  eng "STOP REPLICA command execution is incomplete: Replica SQL thread got the stop signal, thread is busy, SQL thread will stop once the current task is complete."

ER_RPL_REPLICA_IO_THREAD_STOP_CMD_EXEC_TIMEOUT
  eng "STOP REPLICA command execution is incomplete: Replica IO thread got the stop signal, thread is busy, IO thread will stop once the current task is complete."

ER_RPL_GTID_UNSAFE_STMT_ON_NON_TRANS_TABLE
  eng "Statement violates GTID consistency: Updates to non-transactional tables can only be done in either autocommitted statements or single-statement transactions, and never in the same statement as updates to transactional tables."

ER_RPL_GTID_UNSAFE_STMT_CREATE_SELECT
  eng "Statement violates GTID consistency: CREATE TABLE ... SELECT."

OBSOLETE_ER_RPL_GTID_UNSAFE_STMT_ON_TEMPORARY_TABLE
  eng "Statement violates GTID consistency: CREATE TEMPORARY TABLE and DROP TEMPORARY TABLE can only be executed outside transactional context.  These statements are also not allowed in a function or trigger because functions and triggers are also considered to be multi-statement transactions."

ER_BINLOG_ROW_VALUE_OPTION_IGNORED
  eng "When %.192s, the option binlog_row_value_options=%.192s will be ignored and updates will be written in full format to binary log."

ER_BINLOG_USE_V1_ROW_EVENTS_IGNORED
  eng "When %.192s, the option log_bin_use_v1_row_events=1 will be ignored and row events will be written in new format to binary log."

ER_BINLOG_ROW_VALUE_OPTION_USED_ONLY_FOR_AFTER_IMAGES
  eng "When %.192s, the option binlog_row_value_options=%.192s will be used only for the after-image. Full values will be written in the before-image, so the saving in disk space due to binlog_row_value_options is limited to less than 50%%."

ER_CONNECTION_ABORTED
  eng "Aborted connection %u to db: '%-.192s' user: '%-.48s' host: '%-.255s' (%-.64s)."

ER_NORMAL_SERVER_SHUTDOWN
  eng "%s: Normal shutdown."

ER_KEYRING_MIGRATE_FAILED
  eng "Can not perform keyring migration : %s."

ER_GRP_RPL_LOWER_CASE_TABLE_NAMES_DIFF_FROM_GRP
  eng "The member is configured with a lower_case_table_names option value '%u' different from the group '%u'. The member will now exit the group. If there is existing data on member, it may be incompatible with group if it was created with a lower_case_table_names value different from the group."

ER_OOM_SAVE_GTIDS
  eng "An out-of-memory error occurred while saving the set of GTIDs from the last binary log into the mysql.gtid_executed table"

ER_LCTN_NOT_FOUND
  eng "The lower_case_table_names setting for the data dictionary was not found. Starting the server using lower_case_table_names = '%u'."

OBSOLETE_ER_REGEXP_INVALID_CAPTURE_GROUP_NAME
  eng "A capture group has an invalid name."

ER_COMPONENT_FILTER_WRONG_VALUE
  eng "Variable '%-.64s' can't be set to the value of '%-.200s'"

ER_XPLUGIN_FAILED_TO_STOP_SERVICES
  eng "Stopping services failed with error \"%s\""

ER_INCONSISTENT_ERROR
  eng "Query caused different errors on source and replica. Error on source: message (format)='%s' error code=%d; Error on replica:actual message='%s', error code=%d. Default database:'%s'. Query:'%s'"

ER_SERVER_SOURCE_FATAL_ERROR_READING_BINLOG
  eng "Got fatal error %d from source when reading data from binary log: '%-.512s'"

ER_NETWORK_READ_EVENT_CHECKSUM_FAILURE
  eng "Replication event checksum verification failed while reading from network."

ER_REPLICA_CREATE_EVENT_FAILURE
  eng "Failed to create %s"

ER_REPLICA_FATAL_ERROR
  eng "Fatal error: %s"

ER_REPLICA_HEARTBEAT_FAILURE
  eng "Unexpected source's heartbeat data: %s"

ER_REPLICA_INCIDENT
  eng "The incident %s occurred on the source. Message: %s"

ER_REPLICA_SOURCE_COM_FAILURE
  eng "Source command %s failed: %s"

ER_REPLICA_RELAY_LOG_READ_FAILURE
  eng "Relay log read failure: %s"

ER_REPLICA_RELAY_LOG_WRITE_FAILURE
  eng "Relay log write failure: %s"

ER_SERVER_REPLICA_CM_INIT_REPOSITORY
  eng "Replica failed to initialize connection metadata structure from the repository"

ER_SERVER_REPLICA_AM_INIT_REPOSITORY
  eng "Replica failed to initialize applier metadata structure from the repository"

ER_SERVER_NET_PACKET_TOO_LARGE
  eng "Got a packet bigger than 'max_allowed_packet' bytes"

ER_SERVER_NO_SYSTEM_TABLE_ACCESS
  eng "Access to %.64s '%.64s.%.64s' is rejected."

OBSOLETE_ER_SERVER_UNKNOWN_ERROR
  eng "Unknown error"

ER_SERVER_UNKNOWN_SYSTEM_VARIABLE
  eng "Unknown system variable '%-.64s'"

ER_SERVER_NO_SESSION_TO_SEND_TO
  eng "A message intended for a client cannot be sent there as no client-session is attached. Therefore, we're sending the information to the error-log instead: MY-%06d - %s"

ER_SERVER_NEW_ABORTING_CONNECTION 08S01
  eng "Aborted connection %u to db: '%-.192s' user: '%-.48s' host: '%-.255s' (%-.64s; diagnostics area: MY-%06d - %-.64s)"

ER_SERVER_OUT_OF_SORTMEMORY
  eng "Out of sort memory, consider increasing server sort buffer size!"

ER_SERVER_RECORD_FILE_FULL
  eng "The table '%-.192s' is full!"

ER_SERVER_DISK_FULL_NOWAIT
  eng "Create table/tablespace '%-.192s' failed, as disk is full."

ER_SERVER_HANDLER_ERROR
  eng "Handler reported error %d - %s"

ER_SERVER_NOT_FORM_FILE
  eng "Incorrect information in file: '%-.200s'"

ER_SERVER_CANT_OPEN_FILE
  eng "Can't open file: '%-.200s' (OS errno: %d - %s)"

ER_SERVER_FILE_NOT_FOUND
  eng "Can't find file: '%-.200s' (OS errno: %d - %s)"

ER_SERVER_FILE_USED
  eng "'%-.192s' is locked against change (OS errno: %d - %s)"

ER_SERVER_CANNOT_LOAD_FROM_TABLE_V2
  eng "Cannot load from %s.%s. The table is probably corrupted!"

ER_ERROR_INFO_FROM_DA
  eng "Error in diagnostics area: MY-%06d - %s"

ER_SERVER_TABLE_CHECK_FAILED
  eng "Incorrect definition of table %s.%s: expected column '%s' at position %d, found '%s'."

ER_SERVER_COL_COUNT_DOESNT_MATCH_PLEASE_UPDATE_V2
  eng "The column count of %s.%s is wrong. Expected %d, found %d. Created with MySQL %d, now running %d. Please perform the MySQL upgrade procedure."

ER_SERVER_COL_COUNT_DOESNT_MATCH_CORRUPTED_V2
  eng "Column count of %s.%s is wrong. Expected %d, found %d. The table is probably corrupted"

ER_SERVER_ACL_TABLE_ERROR
  eng ""

ER_SERVER_REPLICA_INIT_QUERY_FAILED
  eng "Replica SQL thread aborted. Can't execute init_replica query, MY-%06d - '%s'"

ER_SERVER_REPLICA_CONVERSION_FAILED
  eng "Column %d of table '%-.192s.%-.192s' cannot be converted from type '%-.32s' to type '%-.32s'"

ER_SERVER_REPLICA_IGNORED_TABLE
  eng "Replica SQL thread ignored the query because of replicate-*-table rules"

ER_CANT_REPLICATE_ANONYMOUS_WITH_AUTO_POSITION
  eng "Cannot replicate anonymous transaction when AUTO_POSITION = 1, at file %.400s, position %lld."

ER_CANT_REPLICATE_ANONYMOUS_WITH_GTID_MODE_ON
  eng "Cannot replicate anonymous transaction when @@GLOBAL.GTID_MODE = ON, at file %.400s, position %lld."

ER_CANT_REPLICATE_GTID_WITH_GTID_MODE_OFF
  eng "Cannot replicate GTID-transaction when @@GLOBAL.GTID_MODE = OFF, at file %.400s, position %lld."

# This entry is intended for testing (for instance, the server-log test
# component throws this, and has the built-in filtering engine select these)
# as it is disjunct from all error-codes used in real operation. Message is
# kept intentionally short to despam resulting output files.
ER_SERVER_TEST_MESSAGE
  eng "Simulated error"

ER_AUDIT_LOG_JSON_FILTER_PARSING_ERROR
  eng "%s"

ER_AUDIT_LOG_JSON_FILTERING_NOT_ENABLED
  eng "Audit Log filtering has not been installed."

ER_PLUGIN_FAILED_TO_OPEN_TABLES
  eng "Failed to open the %s filter tables."

ER_PLUGIN_FAILED_TO_OPEN_TABLE
  eng "Failed to open '%s.%s' %s table."

ER_AUDIT_LOG_JSON_FILTER_NAME_CANNOT_BE_EMPTY
  eng "Filter name cannot be empty."

ER_AUDIT_LOG_USER_NAME_INVALID_CHARACTER
  eng "Invalid character in the user name."

ER_AUDIT_LOG_UDF_INSUFFICIENT_PRIVILEGE
  eng "Request ignored for '%s'@'%s'. SUPER or AUDIT_ADMIN needed to perform operation"

ER_AUDIT_LOG_NO_KEYRING_PLUGIN_INSTALLED
  eng "No keyring installed."

ER_AUDIT_LOG_HOST_NAME_INVALID_CHARACTER
  eng "Invalid character in the host name."

ER_AUDIT_LOG_ENCRYPTION_PASSWORD_HAS_NOT_BEEN_SET
  eng "Audit log encryption password has not been set; it will be generated automatically. Use audit_log_encryption_password_get to obtain the password or audit_log_encryption_password_set to set a new one."

ER_AUDIT_LOG_COULD_NOT_CREATE_AES_KEY
  eng "Could not create AES key. OpenSSL's EVP_BytesToKey function failed."

ER_AUDIT_LOG_ENCRYPTION_PASSWORD_CANNOT_BE_FETCHED
  eng "Audit log encryption password cannot be fetched from the keyring. Password used so far is used for encryption."

ER_COULD_NOT_REINITIALIZE_AUDIT_LOG_FILTERS
  eng "Could not reinitialize audit log filters."

ER_AUDIT_LOG_JSON_USER_NAME_CANNOT_BE_EMPTY
  eng "User cannot be empty."

ER_AUDIT_LOG_USER_FIRST_CHARACTER_MUST_BE_ALPHANUMERIC
  eng "First character of the user name must be alphanumeric."

ER_AUDIT_LOG_JSON_FILTER_DOES_NOT_EXIST
  eng "Specified filter has not been found."

ER_IB_MSG_1271
  eng "Cannot upgrade server earlier than 5.7 to 8.0"

ER_STARTING_INIT
  eng "%s (mysqld %s) initializing of server in progress as process %lu"

ER_ENDING_INIT
  eng "%s (mysqld %s) initializing of server has completed"

ER_IB_MSG_1272
  eng "Cannot boot server version %lu on data directory built by version %llu. Downgrade is not supported"

ER_SERVER_SHUTDOWN_INFO
  eng "Received SHUTDOWN from user %s. Shutting down mysqld (Version: %s)."

ER_GRP_RPL_PLUGIN_ABORT
  eng "The plugin encountered a critical error and will abort: %s"

OBSOLETE_ER_REGEXP_INVALID_FLAG
  eng "Invalid match mode flag in regular expression."

OBSOLETE_ER_XA_REPLICATION_FILTERS
  eng "The use of replication filters with XA transactions is not supported, and can lead to an undefined state in the replica."

OBSOLETE_ER_UPDATE_GTID_PURGED_WITH_GR
  eng "Cannot update GTID_PURGED with the Group Replication plugin running"

ER_AUDIT_LOG_TABLE_DEFINITION_NOT_UPDATED
  eng "'%s.%s' table definition has not been upgraded; Please perform the MySQL upgrade procedure."

ER_DD_INITIALIZE_SQL_ERROR
  eng "Execution of server-side SQL statement '%s' failed with error code = %d, error message = '%s'."

ER_NO_PATH_FOR_SHARED_LIBRARY
  eng "No paths allowed for shared library."

ER_UDF_ALREADY_EXISTS
  eng "Function '%-.192s' already exists."

ER_SET_EVENT_FAILED
  eng "Got Error: %ld from SetEvent."

ER_FAILED_TO_ALLOCATE_SSL_BIO
  eng "Error allocating SSL BIO."

ER_IB_MSG_1273
  eng "%s"

ER_PID_FILEPATH_LOCATIONS_INACCESSIBLE
  eng "One or several locations were inaccessible while checking PID filepath."

ER_UNKNOWN_VARIABLE_IN_PERSISTED_CONFIG_FILE
  eng "Currently unknown variable '%s' was read from the persisted config file."

ER_FAILED_TO_HANDLE_DEFAULTS_FILE
  eng "Fatal error in defaults handling. Program aborted!"

ER_DUPLICATE_SYS_VAR
  eng "Duplicate variable name '%s'."

ER_FAILED_TO_INIT_SYS_VAR
  eng "Failed to initialize system variables."

ER_SYS_VAR_NOT_FOUND
  eng "Variable name '%s' not found."

ER_IB_MSG_1274
  eng "Some (%d) threads are still active"

ER_IB_MSG_1275
  eng "%s"

# Unused since MySQL 8.0.15
OBSOLETE_ER_TARGET_TS_UNENCRYPTED
  eng "Source tablespace is encrypted but target tablespace is not."

ER_IB_MSG_WAIT_FOR_ENCRYPT_THREAD
  eng "Waiting for tablespace_alter_encrypt_thread to exit"

ER_IB_MSG_1277
  eng "%s"

ER_IB_MSG_NO_ENCRYPT_PROGRESS_FOUND
  eng "%s"

ER_IB_MSG_RESUME_OP_FOR_SPACE
  eng "%s"

ER_IB_MSG_1280
  eng "%s"

ER_IB_MSG_1281
  eng "%s"

ER_IB_MSG_1282
  eng "%s"

ER_IB_MSG_1283
  eng "%s"

ER_IB_MSG_1284
  eng "%s"

ER_CANT_SET_ERROR_SUPPRESSION_LIST_FROM_COMMAND_LINE
  eng "%s: Could not add suppression rule for code \"%s\". Rule-set may be full, or code may not correspond to an error-log message."

ER_INVALID_VALUE_OF_BIND_ADDRESSES
  eng "Invalid value for command line option bind-addresses: '%s'"

ER_RELAY_LOG_SPACE_LIMIT_DISABLED
  eng "Ignoring the @@global.relay_log_space_limit option because @@global.relay_log_purge is disabled."

ER_GRP_RPL_ERROR_GTID_SET_EXTRACTION
  eng "Error when extracting GTID execution information: %s"

ER_GRP_RPL_MISSING_GRP_RPL_ACTION_COORDINATOR
  eng "Message received without a proper group coordinator module."

ER_GRP_RPL_JOIN_WHEN_GROUP_ACTION_RUNNING
  eng "A member cannot join the group while a group configuration operation '%s' is running initiated by '%s'."

ER_GRP_RPL_JOINER_EXIT_WHEN_GROUP_ACTION_RUNNING
  eng "A member is joining the group while a group configuration operation '%s' is running initiated by '%s'. The member will now leave the group."

ER_GRP_RPL_CHANNEL_THREAD_WHEN_GROUP_ACTION_RUNNING
  eng "Can't start %s for channel '%s' when group replication is running a group configuration operation '%s' initiated by '%s'."

ER_GRP_RPL_APPOINTED_PRIMARY_NOT_PRESENT
  eng "A primary election was invoked but the requested primary member is not in the group. Request ignored."

ER_GRP_RPL_ERROR_ON_MESSAGE_SENDING
  eng "Error while sending message. Context: %s"

ER_GRP_RPL_CONFIGURATION_ACTION_ERROR
  eng "Error while executing a group configuration operation: %s"

ER_GRP_RPL_CONFIGURATION_ACTION_LOCAL_TERMINATION
  eng "Configuration operation '%s' terminated. %s"

ER_GRP_RPL_CONFIGURATION_ACTION_START
  eng "Starting group operation local execution: %s"

ER_GRP_RPL_CONFIGURATION_ACTION_END
  eng "Termination of group operation local execution: %s"

ER_GRP_RPL_CONFIGURATION_ACTION_KILLED_ERROR
  eng "A configuration change was killed in this member. The member will now leave the group as its configuration may have diverged."

ER_GRP_RPL_PRIMARY_ELECTION_PROCESS_ERROR
  eng "There was an issue on the primary election process: %s The member will now leave the group."

ER_GRP_RPL_PRIMARY_ELECTION_STOP_ERROR
  eng "There was an issue when stopping a previous election process: %s"

ER_GRP_RPL_NO_STAGE_SERVICE
  eng "It was not possible to initialize stage logging for this task. The operation will still run without stage tracking."

ER_GRP_RPL_UDF_REGISTER_ERROR
  eng "Could not execute the installation of Group Replication UDF function: %s. Check if the function is already present, if so, try to remove it"

ER_GRP_RPL_UDF_UNREGISTER_ERROR
  eng "Could not uninstall Group Replication UDF functions. Try to remove them manually if present."

ER_GRP_RPL_UDF_REGISTER_SERVICE_ERROR
  eng "Could not execute the installation of Group Replication UDF functions. Check for other errors in the log and try to reinstall the plugin"

ER_GRP_RPL_SERVER_UDF_ERROR
  eng "The function '%s' failed. %s"

OBSOLETE_ER_CURRENT_PASSWORD_NOT_REQUIRED
  eng "Do not specify the current password while changing it for other users."

OBSOLETE_ER_INCORRECT_CURRENT_PASSWORD
  eng "Incorrect current password. Specify the correct password which has to be replaced."

OBSOLETE_ER_MISSING_CURRENT_PASSWORD
  eng "Current password needs to be specified in the REPLACE clause in order to change it."

ER_SERVER_WRONG_VALUE_FOR_VAR
  eng "Variable '%-.64s' can't be set to the value of '%-.200s'"

ER_COULD_NOT_CREATE_WINDOWS_REGISTRY_KEY
  eng "%s was unable to create a new Windows registry key %s for %s; continuing to use the previous ident."

ER_SERVER_GTID_UNSAFE_CREATE_DROP_TEMP_TABLE_IN_TRX_IN_SBR
  eng "Statement violates GTID consistency: CREATE TEMPORARY TABLE and DROP TEMPORARY TABLE are not allowed inside a transaction or inside a procedure in a transactional context when @@session.binlog_format=STATEMENT."

OBSOLETE_ER_SECONDARY_ENGINE
  eng "Secondary engine operation failed. %s."

OBSOLETE_ER_SECONDARY_ENGINE_DDL
  eng "DDLs on a table with a secondary engine defined are not allowed."

OBSOLETE_ER_NO_SESSION_TEMP
  eng "Unable to allocate temporary tablespace for this session"

ER_XPLUGIN_FAILED_TO_SWITCH_SECURITY_CTX
  eng "Unable to switch security context to user: %s"

ER_RPL_GTID_UNSAFE_ALTER_ADD_COL_WITH_DEFAULT_EXPRESSION
  eng "Statement violates GTID consistency: ALTER TABLE ... ADD COLUMN .. with expression as DEFAULT."

ER_UPGRADE_PARSE_ERROR
  eng "Error in parsing %s '%s'.'%s' during upgrade. %s"

ER_DATA_DIRECTORY_UNUSABLE
  eng "The designated data directory %s is unusable. You can remove all files that the server added to it."
  bgn "Зададената папка за базата %s е неизползваема. Можете да изтриете файловете които сървъра добави в нея."

ER_LDAP_AUTH_USER_GROUP_SEARCH_ROOT_BIND
  eng "Group search rebinding via root DN: %s "

ER_PLUGIN_INSTALL_ERROR
  eng "Error installing plugin '%s': %s"

ER_PLUGIN_UNINSTALL_ERROR
  eng "Error uninstalling plugin '%s': %s"

ER_SHARED_TABLESPACE_USED_BY_PARTITIONED_TABLE
  eng "Partitioned table '%s' is not allowed to use shared tablespace '%s'. Please move all partitions to file-per-table tablespaces before upgrade."

ER_UNKNOWN_TABLESPACE_TYPE
  eng "Cannot determine the type of the tablespace named '%s'."

ER_WARN_DEPRECATED_UTF8_ALIAS_OPTION
  eng "%s: 'utf8' is currently an alias for the character set UTF8MB3, but will be an alias for UTF8MB4 in a future release. Please consider using UTF8MB4 in order to be unambiguous."

ER_WARN_DEPRECATED_UTF8MB3_CHARSET_OPTION
  eng "%s: The character set UTF8MB3 is deprecated and will be removed in a future release. Please consider using UTF8MB4 instead."

ER_WARN_DEPRECATED_UTF8MB3_COLLATION_OPTION
  eng "%s: '%-.64s' is a collation of the deprecated character set UTF8MB3. Please consider using UTF8MB4 with an appropriate collation instead."

ER_SSL_MEMORY_INSTRUMENTATION_INIT_FAILED
  eng "The SSL library function %s failed. This is typically caused by the SSL library already being used. As a result the SSL memory allocation will not be instrumented."
  bgn "Функцията от SSL библиотеката %s върна грешка. Това обикновено е защото SSL библиотеката вече е била използвана. Заради това SSL паметта няма да се инструментира."

ER_IB_MSG_MADV_DONTDUMP_UNSUPPORTED
  eng "Disabling @@core_file because @@innodb_buffer_pool_in_core_file is disabled, yet MADV_DONTDUMP is not supported on this platform"

ER_IB_MSG_MADVISE_FAILED
  eng "Disabling @@core_file because @@innodb_buffer_pool_in_core_file is disabled, yet madvise(%p,%zu,%s) failed with %s"

OBSOLETE_ER_COLUMN_CHANGE_SIZE
  eng "Could not change column '%s' of table '%s'. The resulting size of index '%s' would exceed the max key length of %d bytes."

ER_WARN_REMOVED_SQL_MODE
  eng "sql_mode=0x%08x has been removed and will be ignored"

ER_IB_MSG_FAILED_TO_ALLOCATE_WAIT
  eng "Failed to allocate memory for a pool of size %zu bytes. Will wait for %zu seconds for a thread to free a resource."

OBSOLETE_ER_IB_MSG_NUM_POOLS
 eng "Number of pools: %zu"

ER_IB_MSG_USING_UNDO_SPACE
  eng "Using undo tablespace '%s'."

ER_IB_MSG_FAIL_TO_SAVE_SPACE_STATE
  eng "%s Unable to save the current state of tablespace '%s' to the data dictionary"

ER_IB_MSG_MAX_UNDO_SPACES_REACHED
  eng "Cannot create undo tablespace %s at %s because %d undo tablespaces already exist."

ER_IB_MSG_ERROR_OPENING_NEW_UNDO_SPACE
  eng "Error %d opening newly created undo tablespace %s."

ER_IB_MSG_FAILED_SDI_Z_BUF_ERROR
  eng "SDI Compression failed, Z_BUF_ERROR"

ER_IB_MSG_FAILED_SDI_Z_MEM_ERROR
  eng "SDI Compression failed, Z_MEM_ERROR"

ER_IB_MSG_SDI_Z_STREAM_ERROR
  eng "SDI Compression failed, Z_STREAM_ERROR"

ER_IB_MSG_SDI_Z_UNKNOWN_ERROR
  eng "%s"

ER_IB_MSG_FOUND_WRONG_UNDO_SPACE
  eng "Expected to find undo tablespace '%s' for Space ID=%lu, but found '%s' instead!  Did you change innodb_undo_directory?"

ER_IB_MSG_NOT_END_WITH_IBU
  eng "Cannot use %s as an undo tablespace because it does not end with '.ibu'."

OBSOLETE_ER_IB_MSG_UNDO_TRUNCATE_EMPTY_FILE
  eng "ib_undo_trunc_empty_file"

OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_DD_UPDATE
  eng "ib_undo_trunc_before_dd_update"

OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_UNDO_LOGGING
  eng "ib_undo_trunc_before_done_logging"

OBSOLETE_ER_IB_MSG_UNDO_INJECT_BEFORE_RSEG
  eng "ib_undo_trunc_before_rsegs"

ER_IB_MSG_FAILED_TO_FINISH_TRUNCATE
  eng "%s Failed to finish truncating Undo Tablespace '%s'"

ER_IB_MSG_DEPRECATED_INNODB_UNDO_TABLESPACES
  eng "The setting INNODB_UNDO_TABLESPACES is deprecated and is no longer used.  InnoDB always creates 2 undo tablespaces to start with. If you need more, please use CREATE UNDO TABLESPACE."

ER_IB_MSG_WRONG_TABLESPACE_DIR
  eng "The directory for tablespace %s does not exist or is incorrect."

ER_IB_MSG_LOCK_FREE_HASH_USAGE_STATS
  eng "%s"

ER_CLONE_DONOR_TRACE
  eng "Clone donor reported : %.512s."

ER_CLONE_PROTOCOL_TRACE
  eng "Clone received unexpected response from donor : %.512s."

ER_CLONE_CLIENT_TRACE
  eng "Client: %.512s."

ER_CLONE_SERVER_TRACE
  eng "Server: %.512s."

ER_THREAD_POOL_PFS_TABLES_INIT_FAILED
  eng "Failed to initialize the performance schema tables service."

ER_THREAD_POOL_PFS_TABLES_ADD_FAILED
  eng "Failed to add thread pool performance schema tables."

ER_CANT_SET_DATA_DIR
  eng "Failed to set datadir to \'%-.200s\' (OS errno: %d - %s)"

ER_INNODB_INVALID_INNODB_UNDO_DIRECTORY_LOCATION
  eng "The innodb_undo_directory is not allowed to be an ancestor of the datadir."

ER_SERVER_RPL_ENCRYPTION_FAILED_TO_FETCH_KEY
  eng "Failed to fetch key from keyring, please check if keyring is loaded."

ER_SERVER_RPL_ENCRYPTION_KEY_NOT_FOUND
  eng "Can't find key from keyring, please check in the server log if a keyring is loaded and initialized successfully."

ER_SERVER_RPL_ENCRYPTION_KEYRING_INVALID_KEY
  eng "Fetched an invalid key from keyring."

ER_SERVER_RPL_ENCRYPTION_HEADER_ERROR
  eng "Error reading a replication log encryption header: %s."

ER_SERVER_RPL_ENCRYPTION_FAILED_TO_ROTATE_LOGS
  eng "Failed to rotate some logs after changing binlog encryption settings. Please fix the problem and rotate the logs manually."

ER_SERVER_RPL_ENCRYPTION_KEY_EXISTS_UNEXPECTED
  eng "Key %s exists unexpected."

ER_SERVER_RPL_ENCRYPTION_FAILED_TO_GENERATE_KEY
  eng "Failed to generate key, please check if keyring is loaded."

ER_SERVER_RPL_ENCRYPTION_FAILED_TO_STORE_KEY
  eng "Failed to store key, please check if keyring is loaded."

ER_SERVER_RPL_ENCRYPTION_FAILED_TO_REMOVE_KEY
  eng "Failed to remove key, please check if keyring is loaded."

ER_SERVER_RPL_ENCRYPTION_MASTER_KEY_RECOVERY_FAILED
  eng "Unable to recover binlog encryption master key, please check if keyring is loaded."

ER_SERVER_RPL_ENCRYPTION_UNABLE_TO_INITIALIZE
  eng "Failed to initialize binlog encryption, please check if keyring is loaded."

ER_SERVER_RPL_ENCRYPTION_UNABLE_TO_ROTATE_MASTER_KEY_AT_STARTUP
  eng "Failed to rotate binlog encryption master key at startup, please check if keyring is loaded."

ER_SERVER_RPL_ENCRYPTION_IGNORE_ROTATE_MASTER_KEY_AT_STARTUP
  eng "Ignoring binlog_rotate_encryption_master_key_at_startup because binlog_encryption option is disabled."

ER_INVALID_ADMIN_ADDRESS
  eng "Invalid value for command line option admin-address: '%s'"

ER_SERVER_STARTUP_ADMIN_INTERFACE
  eng "Admin interface ready for connections, address: '%s'  port: %d"

ER_CANT_CREATE_ADMIN_THREAD
  eng "Can't create thread to handle admin connections (errno= %d)"

ER_WARNING_RETAIN_CURRENT_PASSWORD_CLAUSE_VOID
  eng "RETAIN CURRENT PASSWORD ignored for user '%s'@'%s' as its authentication plugin %s does not support multiple passwords."

ER_WARNING_DISCARD_OLD_PASSWORD_CLAUSE_VOID
  eng "DISCARD OLD PASSWORD ignored for user '%s'@'%s' as its authentication plugin %s does not support multiple passwords."

OBSOLETE_ER_SECOND_PASSWORD_CANNOT_BE_EMPTY
  eng "Empty password can not be retained as second password for user '%s'@'%s'."

OBSOLETE_ER_PASSWORD_CANNOT_BE_RETAINED_ON_PLUGIN_CHANGE
  eng "Current password can not be retained for user '%s'@'%s' because authentication plugin is being changed."

OBSOLETE_ER_CURRENT_PASSWORD_CANNOT_BE_RETAINED
  eng "Current password can not be retained for user '%s'@'%s' because new password is empty."

ER_WARNING_AUTHCACHE_INVALID_USER_ATTRIBUTES
  eng "Can not read and process value of User_attributes column from mysql.user table for user: '%s@%s'; Ignoring user."

ER_MYSQL_NATIVE_PASSWORD_SECOND_PASSWORD_USED_INFORMATION
  eng "Second password was used for login by user: '%s'@'%s'."

ER_SHA256_PASSWORD_SECOND_PASSWORD_USED_INFORMATION
  eng "Second password was used for login by user: '%s'@'%s'."

ER_CACHING_SHA2_PASSWORD_SECOND_PASSWORD_USED_INFORMATION
  eng "Second password was used for login by user: '%s'@'%s'."

ER_GRP_RPL_SEND_TRX_PREPARED_MESSAGE_FAILED
  eng "Error sending transaction '%d:%lld' prepared message from session '%u'."

ER_GRP_RPL_RELEASE_COMMIT_AFTER_GROUP_PREPARE_FAILED
  eng "Error releasing transaction '%d:%lld' for commit on session '%u' after being prepared on all group members."

ER_GRP_RPL_TRX_ALREADY_EXISTS_ON_TCM_ON_AFTER_CERTIFICATION
  eng "Transaction '%d:%lld' already exists on Group Replication consistency manager while being registered after conflict detection."

ER_GRP_RPL_FAILED_TO_INSERT_TRX_ON_TCM_ON_AFTER_CERTIFICATION
  eng "Error registering transaction '%d:%lld' on Group Replication consistency manager after conflict detection."

ER_GRP_RPL_REGISTER_TRX_TO_WAIT_FOR_GROUP_PREPARE_FAILED
  eng "Error registering transaction '%d:%lld' from session '%u' to wait for being prepared on all group members."

ER_GRP_RPL_TRX_WAIT_FOR_GROUP_PREPARE_FAILED
  eng "Error on transaction '%d:%lld' from session '%u' while waiting for being prepared on all group members."

ER_GRP_RPL_TRX_DOES_NOT_EXIST_ON_TCM_ON_HANDLE_REMOTE_PREPARE
  eng "Transaction '%d:%lld' does not exist on Group Replication consistency manager while receiving remote transaction prepare."

ER_GRP_RPL_RELEASE_BEGIN_TRX_AFTER_DEPENDENCIES_COMMIT_FAILED
  eng "Error releasing transaction '%d:%lld' for execution on session '%u' after its dependencies did complete commit."

ER_GRP_RPL_REGISTER_TRX_TO_WAIT_FOR_DEPENDENCIES_FAILED
  eng "Error registering transaction from session '%u' to wait for its dependencies to complete commit."

ER_GRP_RPL_WAIT_FOR_DEPENDENCIES_FAILED
  eng "Error on session '%u' while waiting for its dependencies to complete commit."

ER_GRP_RPL_REGISTER_TRX_TO_WAIT_FOR_SYNC_BEFORE_EXECUTION_FAILED
  eng "Error registering transaction from session '%u' to wait for sync before execution."

ER_GRP_RPL_SEND_TRX_SYNC_BEFORE_EXECUTION_FAILED
  eng "Error sending sync before execution message from session '%u'."

ER_GRP_RPL_TRX_WAIT_FOR_SYNC_BEFORE_EXECUTION_FAILED
  eng "Error on transaction from session '%u' while waiting for sync before execution."

ER_GRP_RPL_RELEASE_BEGIN_TRX_AFTER_WAIT_FOR_SYNC_BEFORE_EXEC
  eng "Error releasing transaction for execution on session '%u' after wait for sync before execution."

ER_GRP_RPL_TRX_WAIT_FOR_GROUP_GTID_EXECUTED
  eng "Error waiting for group executed transactions commit on session '%u'."

OBSOLETE_ER_UNIT_NOT_FOUND SU001
  eng "There's no unit of measure named '%s'."

OBSOLETE_ER_GEOMETRY_IN_UNKNOWN_LENGTH_UNIT SU001
  eng "The function %s uses %s as a unit, but was passed geometry without units (\"SRID 0\"). Conversion is not possible."

ER_WARN_PROPERTY_STRING_PARSE_FAILED
  eng "Could not parse key-value pairs in property string '%s'"

ER_INVALID_PROPERTY_KEY
  eng "Property key '%s' is invalid."

ER_GRP_RPL_GTID_SET_EXTRACT_ERROR_DURING_RECOVERY
  eng "Error when extracting the group_replication_applier channel received transactions set. Unable to ensure the execution of group transactions received during recovery."

ER_SERVER_RPL_ENCRYPTION_FAILED_TO_ENCRYPT
  eng "Failed to encrypt content to write into binlog file: %s."

ER_CANNOT_GET_SERVER_VERSION_FROM_TABLESPACE_HEADER
  eng "Cannot get the server version number from the dictionary tablespace header."

ER_CANNOT_SET_SERVER_VERSION_IN_TABLESPACE_HEADER
  eng "Cannot set the server version number in the dictionary tablespace header."

ER_SERVER_UPGRADE_VERSION_NOT_SUPPORTED
  eng "Upgrading the server from server version '%u' is not supported."

ER_SERVER_UPGRADE_FROM_VERSION
  eng "MySQL server upgrading from version '%u' to '%u'."

ER_GRP_RPL_ERROR_ON_CERT_DB_INSTALL
  eng "The certification information could not be set in this server: '%s'"

ER_GRP_RPL_FORCE_MEMBERS_WHEN_LEAVING
  eng "A request to force a new group membership was issued when the member is leaving the group."

ER_TRG_WRONG_ORDER
  eng "Trigger %s.%s for table %s.%s is listed in wrong order. Please drop and recreate all triggers for the table."

OBSOLETE_ER_SECONDARY_ENGINE_PLUGIN
  eng "%s"

ER_LDAP_AUTH_GRP_SEARCH_NOT_SPECIAL_HDL
  eng "Special handling for group search, {GA} not found"

ER_LDAP_AUTH_GRP_USER_OBJECT_HAS_GROUP_INFO
  eng "User group retrieval: User object has group information"

ER_LDAP_AUTH_GRP_INFO_FOUND_IN_MANY_OBJECTS
  eng "Group information found in multiple user objects. Search filter configuration is incorrect."

ER_LDAP_AUTH_GRP_INCORRECT_ATTRIBUTE
  eng "User group retrieval: no group attribute found. Incorrect group search attribute. "

ER_LDAP_AUTH_GRP_NULL_ATTRIBUTE_VALUE
  eng "User group retrieval: Group attribute values is NULL. "

ER_LDAP_AUTH_GRP_DN_PARSING_FAILED
  eng "User group retrieval: parsing DN failed. "

ER_LDAP_AUTH_GRP_OBJECT_HAS_USER_INFO
  eng "User group retrieval: Group object has user information"

ER_LDAP_AUTH_LDAPS
  eng "Reserved port for ldaps using ldaps"

ER_LDAP_MAPPING_GET_USER_PROXY
  eng "Get user proxy"

ER_LDAP_MAPPING_USER_DONT_BELONG_GROUP
  eng "Get user proxy: User doesn't belongs to any group, user name will be treated as authenticated user."

ER_LDAP_MAPPING_INFO
  eng "Get user proxy: configured mapping info: %s"

ER_LDAP_MAPPING_EMPTY_MAPPING
  eng "Get user proxy: User doesn't have group mapping information, First LDAP group will be treated as authenticated user."

ER_LDAP_MAPPING_PROCESS_MAPPING
  eng "Process group proxy mapping"

ER_LDAP_MAPPING_CHECK_DELIMI_QUOTE
  eng "Check delimiter after quote"

ER_LDAP_MAPPING_PROCESS_DELIMITER
  eng "Processing delimiter"

ER_LDAP_MAPPING_PROCESS_DELIMITER_EQUAL_NOT_FOUND
  eng "Processing delimiter, separator = not found, resetting position"

ER_LDAP_MAPPING_PROCESS_DELIMITER_TRY_COMMA
  eng ""Processing delimiter, failed to get data for = separator try for separator ,.""

ER_LDAP_MAPPING_PROCESS_DELIMITER_COMMA_NOT_FOUND
  eng "Processing delimiter, separator , not found, resetting position"

ER_LDAP_MAPPING_NO_SEPEARATOR_END_OF_GROUP
  eng "Processing delimiter: No mapping separator is found, end of group information"

ER_LDAP_MAPPING_GETTING_NEXT_MAPPING
  eng "Getting next mapping information"

ER_LDAP_MAPPING_PARSING_CURRENT_STATE
  eng "Parsing mapping, current state: %d  delimiter char: %c "

ER_LDAP_MAPPING_PARSING_MAPPING_INFO
  eng "Parsing mapping info, LDAP group: %s MySQL proxy: %s"

ER_LDAP_MAPPING_PARSING_ERROR
  eng "Mapping parsing error"

ER_LDAP_MAPPING_TRIMMING_SPACES
  eng "Trimming left spaces"

ER_LDAP_MAPPING_IS_QUOTE
  eng "Checking if current characters is quote"

ER_LDAP_MAPPING_NON_DESIRED_STATE
  eng "Not desired state or un-defined states."

ER_INVALID_NAMED_PIPE_FULL_ACCESS_GROUP
  eng "Invalid value for named_pipe_full_access_group."

# This error is not supposed to be reported to the users. It is only
# meant for internal use to signal that a statement should be
# reprepared for a secondary storage engine. The error should be
# caught and handled by the server.
ER_PREPARE_FOR_SECONDARY_ENGINE
  eng "Retry the statement using a secondary storage engine."

ER_SERVER_WARN_DEPRECATED
  eng "'%s' is deprecated and will be removed in a future release. Please use %s instead"
ER_AUTH_ID_WITH_SYSTEM_USER_PRIV_IN_MANDATORY_ROLES
  eng "Cannot set mandatory_roles: AuthId `%.64s`@`%.64s` has '%s' privilege."

ER_SERVER_BINLOG_MASTER_KEY_RECOVERY_OUT_OF_COMBINATION
  eng "Unable to recover binary log master key, the combination of new_master_key_seqno=%u, master_key_seqno=%u and old_master_key_seqno=%u are wrong."

ER_SERVER_BINLOG_MASTER_KEY_ROTATION_FAIL_TO_CLEANUP_AUX_KEY
  eng "Failed to remove auxiliary binary log encryption key from keyring, please check if keyring is loaded. The cleanup of the binary log master key rotation process did not finish as expected and the cleanup will take place upon server restart or next 'ALTER INSTANCE ROTATE BINLOG MASTER KEY' execution."

OBSOLETE_ER_CANNOT_GRANT_SYSTEM_PRIV_TO_MANDATORY_ROLE
  eng "AuthId `%.64s`@`%.64s` is set as mandatory_roles. Cannot grant the '%s' privilege."

OBSOLETE_ER_PARTIAL_REVOKE_AND_DB_GRANT_BOTH_EXISTS
  eng "'%s' privilege for database '%s' exists both as partial revoke and mysql.db simultaneously. It could mean 'mysql' schema is corrupted."

OBSOLETE_ER_DB_ACCESS_DENIED
  eng "Access denied for AuthId `%.64s`@`%.64s` to database '%-.192s'."

OBSOLETE_ER_PARTIAL_REVOKES_EXIST
  eng "At least one partial revoke exists on a database. The system variable '@@partial_revokes' must be set to ON."

ER_TURNING_ON_PARTIAL_REVOKES
  eng "At least one partial revoke exists on a database. Turning ON the system variable '@@partial_revokes'."

ER_WARN_PARTIAL_REVOKE_AND_DB_GRANT
  eng "For user '%s'@'%s', one or more privileges granted through mysql.db for database '%s', conflict with partial revoke. It could mean 'mysql' schema is corrupted."

ER_WARN_INCORRECT_PRIVILEGE_FOR_DB_RESTRICTIONS
  eng "For user %s, ignored restrictions for privilege(s) '%s' for database '%s' as these are not valid database privileges."

ER_WARN_INVALID_DB_RESTRICTIONS
  eng "For user %s, ignored restrictions for privilege(s) '%s' for database '%s' as corresponding global privilege(s) are not granted."

ER_GRP_RPL_INVALID_COMMUNICATION_PROTOCOL
  eng "'%s' is an invalid value for group_replication_communication_protocol_join, please use a MySQL version between 5.7.14 and this server's version"

ER_GRP_RPL_STARTED_AUTO_REJOIN
  eng "Started auto-rejoin procedure attempt %lu of %lu"

ER_GRP_RPL_TIMEOUT_RECEIVED_VC_ON_REJOIN
  eng "Timeout while waiting for a view change event during the auto-rejoin procedure"

ER_GRP_RPL_FINISHED_AUTO_REJOIN
  eng "Auto-rejoin procedure attempt %lu of %lu finished. Member was%s able to join the group."

ER_GRP_RPL_DEFAULT_TABLE_ENCRYPTION_DIFF_FROM_GRP
  eng "The member is configured with a default_table_encryption option value '%d' different from the group '%d'. The member will now exit the group."

ER_SERVER_UPGRADE_OFF
  eng "Server shutting down because upgrade is required, yet prohibited by the command line option '--upgrade=NONE'."

ER_SERVER_UPGRADE_SKIP
  eng "Server upgrade is required, but skipped by command line option '--upgrade=MINIMAL'."

ER_SERVER_UPGRADE_PENDING
  eng "Server upgrade started with version %d, but server upgrade of version %d is still pending."

ER_SERVER_UPGRADE_FAILED
  eng "Failed to upgrade server."

ER_SERVER_UPGRADE_STATUS
  eng "Server upgrade from '%d' to '%d' %s."

ER_SERVER_UPGRADE_REPAIR_REQUIRED
  eng "Table '%s' requires repair."

ER_SERVER_UPGRADE_REPAIR_STATUS
  eng "Table '%s' repair %s."

ER_SERVER_UPGRADE_INFO_FILE
  eng "Could not open server upgrade info file '%s' for writing. Please make sure the file is writable."

ER_SERVER_UPGRADE_SYS_SCHEMA
  eng "Upgrading the sys schema."

ER_SERVER_UPGRADE_MYSQL_TABLES
  eng "Running queries to upgrade MySQL server."

ER_SERVER_UPGRADE_SYSTEM_TABLES
  eng "Upgrading system table data."

ER_SERVER_UPGRADE_EMPTY_SYS
  eng "Found empty sys database. Installing the sys schema."

ER_SERVER_UPGRADE_NO_SYS_VERSION
  eng "A sys schema exists with no sys.version view. If you have a user created sys schema, this must be renamed for the upgrade to succeed."

ER_SERVER_UPGRADE_SYS_VERSION_EMPTY
  eng "A sys schema exists with a sys.version view, but it returns no results."

ER_SERVER_UPGRADE_SYS_SCHEMA_OUTDATED
  eng "Found outdated sys schema version %s."

ER_SERVER_UPGRADE_SYS_SCHEMA_UP_TO_DATE
  eng "The sys schema is already up to date (version %s)."

ER_SERVER_UPGRADE_SYS_SCHEMA_OBJECT_COUNT
  eng "Found %d sys %s, but expected %d. Re-installing the sys schema."

ER_SERVER_UPGRADE_CHECKING_DB
  eng "Checking '%s' schema."

ER_IB_MSG_DDL_LOG_DELETE_BY_ID_TMCT
  eng "Too many concurrent transactions while clearing the DDL Log. Please increase the number of Rollback Segments."

ER_IB_MSG_POST_RECOVER_DDL_LOG_RECOVER
  eng "Error in DDL Log recovery during Post-Recovery processing."

ER_IB_MSG_POST_RECOVER_POST_TS_ENCRYPT
  eng "Error in Post-Tablespace-Encryption during Post-Recovery processing."

ER_IB_MSG_DDL_LOG_FAIL_POST_DDL
  eng "Error in DLL Log cleanup during Post-DDL processing."

ER_SERVER_BINLOG_UNSAFE_SYSTEM_FUNCTION
  eng "'%s' statement is unsafe because it uses a system function that may return a different value on the replica."

ER_SERVER_UPGRADE_HELP_TABLE_STATUS
  eng "Upgrade of help tables %s."

OBSOLETE_ER_GRP_RPL_SRV_GTID_WAIT_ERROR
  eng "Error when waiting for the server to execute local transactions in order assure the group change proper logging"

OBSOLETE_ER_GRP_DELAYED_VCLE_LOGGING
  eng "Unable to log the group change View log event in its exaction position in the log. This will not however affect the group replication recovery process or the overall plugin process."

OBSOLETE_ER_CANNOT_GRANT_ROLES_TO_ANONYMOUS_USER
  eng "Cannot grant roles to an anonymous user."

ER_BINLOG_UNABLE_TO_ROTATE_GTID_TABLE_READONLY
  eng "Unable to create a new binlog file: Table `mysql.gtid_executed` couldn't be opened. %s"

ER_NETWORK_NAMESPACES_NOT_SUPPORTED
  eng "Network Namespaces is not supported on this platform"

ER_UNKNOWN_NETWORK_NAMESPACE
  eng "Unknown network namespace '%s'"

ER_NETWORK_NAMESPACE_NOT_ALLOWED_FOR_WILDCARD_ADDRESS
  eng "Network namespace not allowed for wildcard interface address"

ER_SETNS_FAILED
  eng "setns() failed with error '%s'"

ER_WILDCARD_NOT_ALLOWED_FOR_MULTIADDRESS_BIND
  eng "Wildcard address value not allowed for multivalued bind address"

ER_NETWORK_NAMESPACE_FILE_PATH_TOO_LONG
  eng "The path to a special network namespace file is too long. (got %u > max %u)"

ER_IB_MSG_TOO_LONG_PATH
  eng "Cannot create tablespace '%s'. The filepath is too long for this OS."

ER_IB_RECV_FIRST_REC_GROUP_INVALID
  eng "The last block of redo had corrupted first_rec_group and became fixed (%u -> %u)."

ER_DD_UPGRADE_COMPLETED
  eng "Data dictionary upgrade from version '%u' to '%u' completed."

ER_SSL_SERVER_CERT_VERIFY_FAILED
  eng "Server SSL certificate doesn't verify: %s"

ER_PERSIST_OPTION_USER_TRUNCATED
  eng "Truncated a user name for %s that was too long while reading the persisted variables file"

ER_PERSIST_OPTION_HOST_TRUNCATED
  eng "Truncated a host name for %s that was too long while reading the persisted variables file"

ER_NET_WAIT_ERROR
  eng "The wait_timeout period was exceeded, the idle time since last command was too long."

ER_IB_MSG_1285
  eng "'%s' found not encrypted while '%s' is ON. Trying to encrypt it now."

ER_IB_MSG_CLOCK_MONOTONIC_UNSUPPORTED
  eng "CLOCK_MONOTONIC is unsupported, so do not change the system time when MySQL is running !"

ER_IB_MSG_CLOCK_GETTIME_FAILED
  eng "clock_gettime() failed: %s"

ER_PLUGIN_NOT_EARLY_DUP
  eng "Plugin '%s' is not to be used as an "early" plugin. Don't add it to --early-plugin-load, keyring migration etc."
  bgn "Приставката '%s' не може да се използва като ранна приставка. Не я добавайте в --early-plugin-load и т.н."

ER_PLUGIN_NO_INSTALL_DUP
  eng "Plugin '%s' is marked as not dynamically installable. You have to stop the server to install it."

# When using this error message, use the ER_WARN_DEPRECATED_SYNTAX error
# code.
OBSOLETE_ER_WARN_DEPRECATED_SQL_CALC_FOUND_ROWS
  eng "SQL_CALC_FOUND_ROWS is deprecated and will be removed in a future release. Consider using two separate queries instead."

# When using this error message, use the ER_WARN_DEPRECATED_SYNTAX error
# code.
OBSOLETE_ER_WARN_DEPRECATED_FOUND_ROWS
  eng "FOUND_ROWS() is deprecated and will be removed in a future release. Consider using COUNT(*) instead."

ER_BINLOG_UNSAFE_DEFAULT_EXPRESSION_IN_SUBSTATEMENT
  eng "The statement is unsafe because it invokes a trigger or a stored function that modifies a table that has a column with a DEFAULT expression that may return a different value on the replica."

ER_GRP_RPL_MEMBER_VER_READ_COMPATIBLE
  eng "Member version is read compatible with the group."

ER_LOCK_ORDER_INIT_FAILED
  eng "Lock order disabled (reason: init failed)."

ER_AUDIT_LOG_KEYRING_ID_TIMESTAMP_VALUE_IS_INVALID
  eng "Keyring ID timestamp value is invalid: '%s'"

ER_AUDIT_LOG_FILE_NAME_TIMESTAMP_VALUE_IS_MISSING_OR_INVALID
  eng "Cannot process audit log file. File name timestamp value is missing or invalid: '%s'"

ER_AUDIT_LOG_FILE_NAME_DOES_NOT_HAVE_REQUIRED_FORMAT
  eng "Cannot process audit log file. File name does not have required format: '%s'"

ER_AUDIT_LOG_FILE_NAME_KEYRING_ID_VALUE_IS_MISSING
  eng "Cannot process audit log file. File name keyring ID value is missing: '%s'"

ER_AUDIT_LOG_FILE_HAS_BEEN_SUCCESSFULLY_PROCESSED
  eng "Audit log file has been successfully processed: '%s'"

ER_AUDIT_LOG_COULD_NOT_OPEN_FILE_FOR_READING
  eng "Could not open audit log file for reading: '%s'"

ER_AUDIT_LOG_INVALID_FILE_CONTENT
  eng "Invalid audit log file content: '%s'"

ER_AUDIT_LOG_CANNOT_READ_PASSWORD
  eng "Cannot read password: '%.32s'."

ER_AUDIT_LOG_CANNOT_STORE_PASSWORD
  eng "Cannot store password: '%.32s'."

ER_AUDIT_LOG_CANNOT_REMOVE_PASSWORD
  eng "Cannot remove password: '%.32s'."

ER_AUDIT_LOG_PASSWORD_HAS_BEEN_COPIED
  eng "'audit_log' password has been copied into '%.32s' and will be removed with first purged password."

OBSOLETE_ER_AUDIT_LOG_INSUFFICIENT_PRIVILEGE
  eng "Request ignored for '%.64s'@'%.64s'. Role needed to perform operation: '%.32s'"

OBSOLETE_ER_WRONG_MVI_VALUE
  eng "Can't store an array or an object in a scalar key part of the index '%.192s'"

OBSOLETE_ER_WARN_FUNC_INDEX_NOT_APPLICABLE
  eng "Cannot use functional index '%-.64s' due to type or collation conversion"

OBSOLETE_ER_EXCEEDED_MV_KEYS_NUM
  eng "Exceeded max number of values per record for multi-valued index '%-.64s' by %u value(s)"

OBSOLETE_ER_EXCEEDED_MV_KEYS_SPACE
  eng "Exceeded max total length of values per record for multi-valued index '%-.64s' by %u bytes"

OBSOLETE_ER_FUNCTIONAL_INDEX_DATA_IS_TOO_LONG 22001
  eng "Data too long for functional index '%-.64s'"

OBSOLETE_ER_INVALID_JSON_VALUE_FOR_FUNC_INDEX 22018
  eng "Invalid JSON value for CAST for functional index '%-.64s'"

OBSOLETE_ER_JSON_VALUE_OUT_OF_RANGE_FOR_FUNC_INDEX 22003
  eng "Out of range JSON value for CAST for functional index '%-.64s'"

ER_LDAP_EMPTY_USERDN_PASSWORD
  eng "Empty user dn or password is not allowed, not attempting LDAP bind."

OBSOLETE_ER_GROUPING_ON_TIMESTAMP_IN_DST
  eng "Grouping on temporal is non-deterministic for timezones having DST. Please consider switching to UTC for this query."

ER_ACL_WRONG_OR_MISSING_ACL_TABLES_LOG
  eng "The current layout of the ACL tables does not conform to the server's expected layout. They're either altered, missing or not upgraded from a previous version. However a best effort attempt to read data from these tables will still be made."

ER_LOCK_ORDER_FAILED_WRITE_FILE
  eng "LOCK_ORDER: Failed to write to file <%s>."

ER_LOCK_ORDER_FAILED_READ_FILE
  eng "LOCK_ORDER: Failed to read from file <%s>."

ER_LOCK_ORDER_MESSAGE
  eng "LOCK_ORDER message: %s"

ER_LOCK_ORDER_DEPENDENCIES_SYNTAX
  eng "Lock order dependencies file <%s> (%d:%d) - (%d:%d) : %s"

ER_LOCK_ORDER_SCANNER_SYNTAX
  eng "Lock order scanner: (%d:%d) - (%d:%d) : %s"

ER_DATA_DIRECTORY_UNUSABLE_DELETABLE
  eng "The newly created data directory %s by --initialize is unusable. You can remove it."
  bgn "Новосъздадената от --initialize папка за базата %s е неизползваема. Можете да я изтриете."

ER_IB_MSG_BTREE_LEVEL_LIMIT_EXCEEDED
  eng "No. of B-tree level created for index %s has crossed the permissible limit. If debug option innodb_limit_optimistic_insert_debug is being used try tweaking it to include more records in a page."

ER_IB_CLONE_START_STOP
  eng "%s"

ER_IB_CLONE_OPERATION
  eng "%s"

ER_IB_CLONE_RESTART
  eng "%s"

ER_IB_CLONE_USER_DATA
  eng "Clone removing all user data for provisioning: %s"

ER_IB_CLONE_NON_INNODB_TABLE
  eng "Non innodb table: %s.%s is not cloned and is empty."

ER_CLONE_SHUTDOWN_TRACE
  eng "Clone shutting down server as RESTART failed. Please start server to complete clone operation."

ER_GRP_RPL_GTID_PURGED_EXTRACT_ERROR
  eng "Error when extracting this member GTID purged set. Operations and checks made to group joiners may be incomplete."

ER_GRP_RPL_CLONE_PROCESS_PREPARE_ERROR
  eng "There was an issue when configuring the remote cloning process: %s"

ER_GRP_RPL_CLONE_PROCESS_EXEC_ERROR
  eng "There was an issue when cloning from another server: %s"

ER_GRP_RPL_RECOVERY_EVAL_ERROR
  eng "There was an issue when trying to evaluate the best distributed recovery strategy while joining.%s"

ER_GRP_RPL_NO_POSSIBLE_RECOVERY
  eng "No valid or ONLINE members exist to get the missing data from the group. For cloning check if donors of the same version and with clone plugin installed exist. For incremental recovery check if you have donors where the required data was not purged from the binary logs."

ER_GRP_RPL_CANT_KILL_THREAD
  eng "The group replication plugin could not kill the plugin routine for %s. %s"

ER_GRP_RPL_RECOVERY_STRAT_CLONE_THRESHOLD
  eng "This member will start distributed recovery using clone. It is due to the number of missing transactions being higher than the configured threshold of %llu."

ER_GRP_RPL_RECOVERY_STRAT_CLONE_PURGED
  eng "This member will start distributed recovery using clone. It is due to no ONLINE member has the missing data for recovering in its binary logs."

ER_GRP_RPL_RECOVERY_STRAT_CHOICE
  eng "Distributed recovery will transfer data using: %s"

ER_GRP_RPL_RECOVERY_STRAT_FALLBACK
  eng "Due to some issue on the previous step distributed recovery is now executing: %s"

ER_GRP_RPL_RECOVERY_STRAT_NO_FALLBACK
  eng "Due to a critical cloning error or lack of donors, distributed recovery cannot be executed. The member will now leave the group."

ER_GRP_RPL_REPLICA_THREAD_ERROR_ON_CLONE
  eng "The '%s' thread of channel '%s' will error out as the server will attempt to clone another server"

ER_UNKNOWN_TABLE_IN_UPGRADE
  eng "Unknown table '%-.129s'"

ER_IDENT_CAUSES_TOO_LONG_PATH_IN_UPGRADE
  eng "Long database name and identifier for object resulted in path length exceeding %d characters. Path: '%s'."

ER_XA_CANT_CREATE_MDL_BACKUP
  eng "XA: Failed to take MDL Lock backup of PREPARED XA transaction during client disconnect."

ER_AUDIT_LOG_SUPER_PRIVILEGE_REQUIRED
  eng "SUPER privilege or AUDIT_ADMIN role required for '%s'@'%s' user."

ER_AUDIT_LOG_UDF_INVALID_ARGUMENT_TYPE
  eng "Invalid argument type"

ER_AUDIT_LOG_UDF_INVALID_ARGUMENT_COUNT
  eng "Invalid argument count"

ER_AUDIT_LOG_HAS_NOT_BEEN_INSTALLED
  eng "audit_log plugin has not been installed using INSTALL PLUGIN syntax."

ER_AUDIT_LOG_UDF_READ_INVALID_MAX_ARRAY_LENGTH_ARG_TYPE
  eng "Invalid \"max_array_length\" argument type."

ER_LOG_CANNOT_WRITE_EXTENDED
  eng "Failed to write to %s: %s (%s)"

OBSOLETE_ER_UPGRADE_WITH_PARTITIONED_TABLES_REJECTED
  eng "Upgrading from server version %d with partitioned tables and lower_case_table_names == 1 on a case sensitive file system may cause issues, and is therefore prohibited. To upgrade anyway, restart the new server version with the command line option 'upgrade=FORCE'. When upgrade is completed, please execute 'RENAME TABLE <part_table_name> TO <new_table_name>; RENAME TABLE <new_table_name> TO <part_table_name>;' for each of the partitioned tables. Please see the documentation for further information."

ER_KEYRING_AWS_INCORRECT_PROXY
  eng "Incorrect environment variable %s, invalid port: %s"

ER_GRP_RPL_SERVER_SET_TO_OFFLINE_MODE_DUE_TO_ERRORS
  eng "The server was automatically set into offline mode after an error was detected."

ER_GRP_RPL_MESSAGE_SERVICE_FATAL_ERROR
  eng "A message sent through the Group Replication message deliver service was not delivered successfully. The server will now leave the group. Try to add the server back to the group and check if the problem persists, or check previous messages in the log for hints of what could be the problem."

ER_WARN_WRONG_COMPRESSION_ALGORITHM_LOG
  eng "Invalid SOURCE_COMPRESSION_ALGORITHMS '%.192s' found in repository for channel '%.192s'. Resetting to 'uncompressed' (no compression)."

ER_WARN_WRONG_COMPRESSION_LEVEL_LOG
  eng "Invalid SOURCE_ZSTD_COMPRESSION_LEVEL found in repository for channel '%.192s'. Resetting to %u."

ER_PROTOCOL_COMPRESSION_RESET_LOG
  eng "Option --protocol-compression-algorithms is reset to default value."

ER_XPLUGIN_COMPRESSION_ERROR
  eng "Fatal error while compressing outgoing data - %s"

ER_MYSQLBACKUP_MSG
  eng "%s"

ER_WARN_UNKNOWN_KEYRING_AWS_REGION
  eng "Unknown keyring_aws_region '%.192s'. Connection to AWS KMS may fail."

ER_WARN_LOG_PRIVILEGE_CHECKS_USER_DOES_NOT_EXIST
  eng "PRIVILEGE_CHECKS_USER for replication channel '%.192s' was set to `%.64s`@`%.255s`, but this is not an existing user. Correct this before starting replication threads."

ER_WARN_LOG_PRIVILEGE_CHECKS_USER_CORRUPT
  eng "Invalid, corrupted PRIVILEGE_CHECKS_USER was found in the replication configuration repository for channel '%.192s'. Use CHANGE REPLICATION SOURCE TO PRIVILEGE_CHECKS_USER to correct the configuration."

ER_WARN_LOG_PRIVILEGE_CHECKS_USER_NEEDS_RPL_APPLIER_PRIV
  eng "PRIVILEGE_CHECKS_USER for replication channel '%.192s' was set to `%.64s`@`%.255s`, but this user does not have REPLICATION_APPLIER privilege. Correct this before starting the replication threads."

ER_OBSOLETE_FILE_PRIVILEGE_FOR_REPLICATION_CHECKS
  eng "The PRIVILEGE_CHECKS_USER for channel '%.192s' would need FILE privilege to execute a LOAD DATA INFILE statement replicated in statement format. Consider using binlog_format=ROW on source. If the replicated events are trusted, recover from the failure by temporarily granting FILE to the PRIVILEGE_CHECKS_USER."

ER_RPL_REPLICA_SQL_THREAD_STARTING_WITH_PRIVILEGE_CHECKS
  eng "Replica SQL thread%s initialized, starting replication in log '%s' at position %s, relay log '%s' position: %s, user: '%.64s'@'%.255s', roles: %.512s"

ER_AUDIT_LOG_CANNOT_GENERATE_PASSWORD
  eng "Cannot generate password: '%.32s'"

ER_INIT_FAILED_TO_GENERATE_ROOT_PASSWORD
  eng "Failed to generate a random password for root. Probabably not enough enthropy."

ER_PLUGIN_LOAD_OPTIONS_IGNORED
  eng "Ignoring --plugin-load[_add] list as the server is running with --initialize(-insecure)."

ER_WARN_AUTH_ID_WITH_SYSTEM_USER_PRIV_IN_MANDATORY_ROLES
  eng "Cannot set mandatory_roles: AuthId `%.64s`@`%.64s` has '%s' privilege. AuthId(s) set in the mandatory_roles are ignored."

ER_IB_MSG_SKIP_HIDDEN_DIR
  eng "Directory '%s' will not be scanned because it is a hidden directory."

ER_WARN_RPL_RECOVERY_NO_ROTATE_EVENT_FROM_SOURCE_EOF
  eng "Server was not able to find a rotate event from source server to initialize relay log recovery for channel '%s'. Skipping relay log recovery for the channel."

ER_IB_LOB_ROLLBACK_INDEX_LEN
  eng "Rolling back LOB for transaction %llu undo number %llu : current index length %llu. (iteration %llu)"

ER_CANT_PROCESS_EXPRESSION_FOR_GENERATED_COLUMN_TO_DD
  eng "Error in processing (possibly deprecated) expression or function '%.128s' for generated column %.64s.%.64s.%.64s"

ER_RPL_REPLICA_QUEUE_EVENT_FAILED_INVALID_NON_ROW_FORMAT
  eng "The queue event failed for channel '%s' as an invalid event according to REQUIRE_ROW_FORMAT was found."

ER_OBSOLETE_REQUIRE_ROW_FORMAT_VIOLATION
  eng "The application of relay events failed for channel '%s' as an invalid event according to REQUIRE_ROW_FORMAT was found."

ER_LOG_PRIV_CHECKS_REQUIRE_ROW_FORMAT_NOT_SET
  eng "PRIVILEGE_CHECKS_USER for replication channel '%.192s' can't be set to `%.64s`@`%.255s` unless REQUIRE_ROW_FORMAT is also set to %d."

ER_RPL_REPLICA_SQL_THREAD_DETECTED_UNEXPECTED_EVENT_SEQUENCE
  eng "An unexpected event sequence was detected by the SQL thread while applying an event."

ER_IB_MSG_UPGRADE_PARTITION_FILE
  eng "Updating partition file name '%s' to '%s' and all other partition files during upgrade"

ER_IB_MSG_DOWNGRADE_PARTITION_FILE
  eng "Updating partition file name '%s' to '%s' and all other partition files during downgrade"

ER_IB_MSG_UPGRADE_PARTITION_FILE_IMPORT
  eng "Updating partition file name '%s' to '%s' for import"

ER_IB_WARN_OPEN_PARTITION_FILE
  eng "Unable to open partition file with new name '%s'. Please check if innodb_directories is set to include all external file paths"

ER_IB_MSG_FIL_STATE_MOVED_CORRECTED
  eng "%s DD ID: %llu - Partition tablespace %u, name '%s' is corrected to '%s'"

ER_IB_MSG_FIL_STATE_MOVED_CHANGED_PATH
  eng "%s DD ID: %llu - Tablespace %u, name '%s', '%s' is moved to '%s'"

ER_IB_MSG_FIL_STATE_MOVED_CHANGED_NAME
  eng "%s DD ID: %llu - Partition tablespace %u, name '%s', '%s' is updated to '%s'"

ER_IB_MSG_FIL_STATE_MOVED_TOO_MANY
  eng "%s Too many files have been moved, disabling logging of detailed messages"

ER_GR_ELECTED_PRIMARY_GTID_INFORMATION
  eng "Elected primary member %s: %s"

ER_SCHEMA_NAME_IN_UPPER_CASE_NOT_ALLOWED
  eng "Schema name '%s' containing upper case characters is not allowed with lower_case_table_names = 1."

ER_TABLE_NAME_IN_UPPER_CASE_NOT_ALLOWED
  eng "Table name '%s.%s' containing upper case characters is not allowed with lower_case_table_names = 1."

ER_SCHEMA_NAME_IN_UPPER_CASE_NOT_ALLOWED_FOR_FK
  eng "Schema name '%s' containing upper case characters, used by foreign key '%s' in table '%s.%s', is not allowed with lower_case_table_names = 1."

ER_TABLE_NAME_IN_UPPER_CASE_NOT_ALLOWED_FOR_FK
  eng "Table name '%s.%s' containing upper case characters, used by foreign key '%s' in table '%s.%s', is not allowed with lower_case_table_names = 1."

ER_IB_MSG_DICT_PARTITION_NOT_FOUND
  eng "Table Partition: %s is not found in InnoDB dictionary"

ER_ACCESS_DENIED_FOR_USER_ACCOUNT_BLOCKED_BY_PASSWORD_LOCK
  eng "Access denied for user '%-.48s'@'%-.64s'. Account is blocked for %s day(s) (%s day(s) remaining) due to %u consecutive failed logins. Use FLUSH PRIVILEGES or ALTER USER to reset."

ER_INNODB_OUT_OF_RESOURCES
  eng "%s"

ER_DD_UPGRADE_FOUND_PREPARED_XA_TRANSACTION
  eng "Upgrade cannot proceed due to an existing prepared XA transaction."

ER_MIGRATE_TABLE_TO_DD_OOM
  eng "Could not allocate memory for key_info when migrating table %s.%s"

ER_RPL_RELAY_LOG_RECOVERY_INFO_AFTER_CLONE
  eng "Applier metadata information for channel '%s' was found after a clone operation. Relay log recovery will be executed to adjust positions and file information for this new server. Should that automatic procedure fail please adjust the positions through 'CHANGE REPLICATION SOURCE TO'"

ER_IB_MSG_57_UNDO_SPACE_DELETE_FAIL
  eng "Failed to delete 5.7 undo tablespace: %s during upgrade"

ER_IB_MSG_DBLWR_1285
  eng "Empty doublewrite file: %s"

ER_IB_MSG_DBLWR_1286
  eng "Using '%s' for doublewrite"

ER_IB_MSG_DBLWR_1287
  eng  "Error reading doublewrite buffer from the system tablespace"

ER_IB_MSG_DBLWR_1288
  eng "Cannot create doublewrite buffer: you must increase your buffer pool size. Cannot continue operation."

ER_IB_MSG_DBLWR_1290
  eng "The page in the doublewrite file is corrupt. Cannot continue operation. You can try to recover the database with innodb_force_recovery=6"

ER_IB_MSG_BAD_DBLWR_FILE_NAME
  eng "The doublewrite filename '%s' is incorrect."

OBSOLETE_ER_IB_MSG_DBLWR_1292
  eng "%s"

ER_IB_MSG_DBLWR_1293
  eng "Doublewrite file create failed: %s"

ER_IB_MSG_DBLWR_1294
  eng "DBLWRThread: pthread_setaffinity() failed!"

ER_IB_MSG_DBLWR_1295
  eng "%s"

ER_IB_MSG_DBLWR_1296
  eng "%s"

ER_IB_MSG_DBLWR_1297
  eng "Doublewrite file read failed: %s"

ER_IB_MSG_DBLWR_1298
  eng "Dump of the data file page:"

ER_IB_MSG_DBLWR_1300
  eng "%s"

ER_IB_MSG_DBLWR_1301
  eng "%s"

ER_IB_MSG_DBLWR_1304
  eng "%s"

ER_IB_MSG_DBLWR_1305
  eng "%s"

ER_IB_MSG_DBLWR_1306
  eng "%s"

ER_IB_MSG_DBLWR_1307
  eng "%s"

ER_IB_MSG_DBLWR_1308
  eng "%s"

ER_IB_MSG_DBLWR_1309
  eng "%s"

ER_IB_MSG_DBLWR_1310
  eng "%s"

ER_IB_MSG_DBLWR_1311
  eng "%s"

ER_IB_MSG_DBLWR_1312
  eng "%s"

ER_IB_MSG_DBLWR_1313
  eng "%s"

ER_IB_MSG_DBLWR_1314
  eng "%s"

ER_IB_MSG_DBLWR_1315
  eng "%s"

ER_IB_MSG_DBLWR_1316
  eng "%s"

ER_IB_MSG_DBLWR_1317
  eng "%s"

ER_IB_MSG_DBLWR_1318
  eng "%s"

ER_IB_MSG_DBLWR_1319
  eng "Doublewrite load file %s size %lu is not a multiple of the configured page size %lu""

ER_IB_MSG_DBLWR_1320
  eng "Doublewrite file %s truncate failed"

ER_IB_MSG_DBLWR_1321
  eng "Doublewrite file %s failed to writ zeros"

ER_IB_MSG_DBLWR_1322
  eng "Doublewrite create file %s size %lu is not a multiple of the configured page size %lu""

ER_IB_MSG_DBLWR_1323
  eng "%s"

ER_IB_MSG_DBLWR_1324
  eng "%s"

ER_IB_MSG_DBLWR_1325
  eng "%s"

ER_IB_MSG_DBLWR_1326
  eng "%s"

ER_IB_MSG_DBLWR_1327
  eng "%s"

ER_IB_MSG_GTID_FLUSH_AT_SHUTDOWN
  eng "Could not flush all GTIDs during slow shutdown. Will recover GTIDs when server restarts."

ER_IB_MSG_57_STAT_SPACE_DELETE_FAIL
  eng "Failed to delete 5.7 stat tablespace: %s during upgrade"

ER_NDBINFO_UPGRADING_SCHEMA
  eng "Installing ndbinfo schema version %s"

ER_NDBINFO_NOT_UPGRADING_SCHEMA
  eng "Installed ndbinfo schema is current. Not upgrading."

ER_NDBINFO_UPGRADING_SCHEMA_FAIL
  eng "Failed to upgrade ndbinfo schema."

OBSOLETE_ER_IB_MSG_CREATE_LOG_FILE
  eng "%s"

ER_IB_MSG_INNODB_START_INITIALIZE
  eng "InnoDB initialization has started."

ER_IB_MSG_INNODB_END_INITIALIZE
  eng "InnoDB initialization has ended."

ER_IB_MSG_PAGE_ARCH_NO_RESET_POINTS
  eng "Could not find appropriate reset points."

ER_IB_WRN_PAGE_ARCH_FLUSH_DATA
  eng "Unable to flush. Page archiving data may be corrupt in case of a crash."

ER_IB_ERR_PAGE_ARCH_INVALID_DOUBLE_WRITE_BUF
  eng "Page archiver's doublewrite buffer for %ld is not valid."

ER_IB_ERR_PAGE_ARCH_RECOVERY_FAILED
  eng "Page archiver system's recovery failed."

ER_IB_ERR_PAGE_ARCH_INVALID_FORMAT
  eng "Invalid archived file name format. The archived file is supposed to have the format %s + [0-9]*."

ER_INVALID_XPLUGIN_SOCKET_SAME_AS_SERVER
  eng "X Plugins UNIX socket must use different file than MySQL server. X Plugin won't be accessible through UNIX socket"

ER_INNODB_UNABLE_TO_ACQUIRE_DD_OBJECT
  eng "%s"

ER_WARN_LOG_DEPRECATED_PARTITION_PREFIX_KEY
  eng "Column '%.64s.%.64s.%.64s' having prefix key part '%.64s(%u)' is ignored by the partitioning function. Use of prefixed columns in the PARTITION BY KEY() clause is deprecated and will be removed in a future release."

ER_IB_MSG_UNDO_TRUNCATE_TOO_OFTEN
  eng "Undo Truncation is occurring too often. Consider increasing --innodb-max-undo-log-size."

ER_GRP_RPL_IS_STARTING
  eng "Plugin 'group_replication' is starting."

ER_IB_MSG_INVALID_LOCATION_FOR_TABLESPACE
  eng "Cannot create tablespace %s because the directory is not a valid location. %s";

ER_IB_MSG_INVALID_LOCATION_WRONG_DB
  eng "Scanned file '%s' for tablespace %s cannot be opened because it is not in a sub-directory named for the schema.");

ER_IB_MSG_CANNOT_FIND_DD_UNDO_SPACE
  eng "Cannot find undo tablespace %s with filename '%s' as indicated by the Data Dictionary. Did you move or delete this tablespace? Any undo logs in it cannot be used."

ER_GRP_RPL_RECOVERY_ENDPOINT_FORMAT
  eng "Invalid input value for recovery socket endpoints '%s'. Please, provide a valid, comma separated, list of endpoints (IP:port)".

ER_GRP_RPL_RECOVERY_ENDPOINT_INVALID
  eng "The server is not listening on endpoint '%s'. Only endpoints that the server is listening on are valid recovery endpoints."

ER_GRP_RPL_RECOVERY_ENDPOINT_INVALID_DONOR_ENDPOINT
  eng "Received invalid recovery endpoints configuration from donor. This member is not a valid donor for recovery, so it will be skipped."

ER_GRP_RPL_RECOVERY_ENDPOINT_INTERFACES_IPS
  eng "Failed to retrieve IP addresses from enabled host network interfaces."

ER_WARN_TLS_CHANNEL_INITIALIZATION_ERROR
  eng "Failed to initialize TLS for channel: %s. See below for the description of exact issue."

ER_XPLUGIN_FAILED_TO_VALIDATE_ADDRESS
  eng "Validation of value '%s' set to `Mysqlx_bind_address` failed: %s. Skipping this value."

ER_XPLUGIN_FAILED_TO_BIND_INTERFACE_ADDRESS
  eng "Value '%s' set to `Mysqlx_bind_address`, X Plugin can't bind to it. Skipping this value."

ER_IB_ERR_RECOVERY_REDO_DISABLED
  eng "Server was killed when InnoDB redo logging was disabled. Data files could be corrupt. You can try to restart the database with innodb_force_recovery=6"

ER_IB_WRN_FAST_SHUTDOWN_REDO_DISABLED
  eng "InnoDB cannot do cold shutdown 'innodb_fast_shutdown = 2' and is forcing 'innodb_fast_shutdown = 1' as redo logging is disabled. InnoDB would flush all dirty pages to ensure physical data consistency."

ER_IB_WRN_REDO_DISABLED
  eng "InnoDB redo logging is disabled. All data could be lost in case of a server crash."

ER_IB_WRN_REDO_ENABLED
  eng "InnoDB redo logging is enabled. Data is now safe and can be recovered in case of a server crash."

ER_TLS_CONFIGURED_FOR_CHANNEL
  eng "Channel %s configured to support TLS. Encrypted connections are now supported for this channel."

ER_TLS_CONFIGURATION_REUSED
  eng "No TLS configuration was given for channel %s; re-using TLS configuration of channel %s."

ER_IB_TABLESPACE_PATH_VALIDATION_SKIPPED
  eng "Skipping InnoDB tablespace path validation. Manually moved tablespace files will not be detected!"

ER_IB_CANNOT_UPGRADE_WITH_DISCARDED_TABLESPACES
  eng "Upgrade failed because database contains discarded tablespaces."

ER_USERNAME_TRUNKATED
  eng "The user name '%s' exceeds the maximum number of allowed characters %d and is trunkated."

ER_HOSTNAME_TRUNKATED
  eng "The host name '%s' exceeds the maximum number of allowed characters %d and is trunkated."

ER_IB_MSG_TRX_RECOVERY_ROLLBACK_NOT_COMPLETED
  eng "Rollback of non-prepared transactions not completed, due to fast shutdown"

ER_AUTHCACHE_ROLE_EDGES_IGNORED_EMPTY_NAME
  eng "Found an entry in the 'role_edges' table with empty authorization ID; Skipped"

ER_AUTHCACHE_ROLE_EDGES_UNKNOWN_AUTHORIZATION_ID
  eng "Found an entry in the 'role_edges' table with unknown authorization ID '%s'; Skipped"

ER_AUTHCACHE_DEFAULT_ROLES_IGNORED_EMPTY_NAME
  eng "Found an entry in the 'default_roles' table with empty authorization ID; Skipped"

ER_AUTHCACHE_DEFAULT_ROLES_UNKNOWN_AUTHORIZATION_ID
  eng "Found an entry in the 'default_roles' table with unknown authorization ID '%s'; Skipped"

ER_IB_ERR_DDL_LOG_INSERT_FAILURE
  eng "Couldn't insert entry in ddl log for ddl."

ER_IB_LOCK_VALIDATE_LATCH_ORDER_VIOLATION
  eng "%s"

ER_IB_RELOCK_LATCH_ORDER_VIOLATION
  eng "%s"

OBSOLETE_ER_IB_MSG_1352
  eng "%s"

OBSOLETE_ER_IB_MSG_1353
  eng "%s"

OBSOLETE_ER_IB_MSG_1354
  eng "%s"

OBSOLETE_ER_IB_MSG_1355
  eng "%s"

OBSOLETE_ER_IB_MSG_1356
  eng "%s"

ER_IB_MSG_1357
  eng "%s"

ER_IB_MSG_1358
  eng "%s"

ER_IB_MSG_1359
  eng "%s"

ER_IB_FAILED_TO_DELETE_TABLESPACE_FILE
  eng "%s"

ER_IB_UNABLE_TO_EXPAND_TEMPORARY_TABLESPACE_POOL
  eng "%s"

ER_IB_TMP_TABLESPACE_CANNOT_CREATE_DIRECTORY
  eng "%s"

ER_IB_MSG_SCANNING_TEMP_TABLESPACE_DIR
  eng "%s"

ER_IB_ERR_TEMP_TABLESPACE_DIR_DOESNT_EXIST
  eng "%s"

ER_IB_ERR_TEMP_TABLESPACE_DIR_EMPTY
  eng "%s"

ER_IB_ERR_TEMP_TABLESPACE_DIR_CONTAINS_SEMICOLON
  eng "%s"

ER_IB_ERR_TEMP_TABLESPACE_DIR_SUBDIR_OF_DATADIR
  eng "%s"

ER_IB_ERR_SCHED_SETAFFNINITY_FAILED
  eng "%s"

ER_IB_ERR_UNKNOWN_PAGE_FETCH_MODE
  eng "%s"

ER_IB_ERR_LOG_PARSING_BUFFER_OVERFLOW
  eng "%s"

ER_IB_ERR_NOT_ENOUGH_MEMORY_FOR_PARSE_BUFFER
  eng "%s"

ER_IB_MSG_1372
  eng "%s"

ER_IB_MSG_1373
  eng "%s"

ER_IB_MSG_1374
  eng "%s"

ER_IB_MSG_1375
  eng "%s"

ER_IB_ERR_ZLIB_UNCOMPRESS_FAILED
  eng "%s"

ER_IB_ERR_ZLIB_BUF_ERROR
  eng "%s"

ER_IB_ERR_ZLIB_MEM_ERROR
  eng "%s"

ER_IB_ERR_ZLIB_DATA_ERROR
  eng "%s"

ER_IB_ERR_ZLIB_UNKNOWN_ERROR
  eng "%s"

ER_IB_MSG_1381
  eng "%s"

ER_IB_ERR_INDEX_RECORDS_WRONG_ORDER
  eng "%s"

ER_IB_ERR_INDEX_DUPLICATE_KEY
  eng "%s"

ER_IB_ERR_FOUND_N_DUPLICATE_KEYS
  eng "%s"

ER_IB_ERR_FOUND_N_RECORDS_WRONG_ORDER
  eng "%s"

ER_IB_ERR_PARALLEL_READ_OOM
  eng "%s"

ER_IB_MSG_UNDO_MARKED_ACTIVE
  eng "The state of undo tablespace %s is set to active implicitly."

ER_IB_MSG_UNDO_ALTERED_ACTIVE
  eng "The state of undo tablespace %s is set to 'active' by ALTER TABLESPACE."

ER_IB_MSG_UNDO_ALTERED_INACTIVE
  eng "The state of undo tablespace %s is set to 'inactive' by ALTER TABLESPACE."

ER_IB_MSG_UNDO_MARKED_EMPTY
  eng "The state of undo tablespace %s is set to 'empty'."

ER_IB_MSG_UNDO_TRUNCATE_DELAY_BY_CLONE
  eng "Delaying truncate of undo tablespace %s due to clone activity."

ER_IB_MSG_UNDO_TRUNCATE_DELAY_BY_MDL
  eng "Delaying truncate of undo tablespace %s due to a metadata lock."

ER_IB_MSG_INJECT_CRASH
  eng "Injected debug crash point: %s"

ER_IB_MSG_INJECT_FAILURE
  eng "Injected debug failure point: %s"

ER_GRP_RPL_TIMEOUT_RECEIVED_VC_LEAVE_ON_REJOIN
  eng "Timeout while waiting for a view change event during the leave step before a auto-rejoin attempt."

ER_RPL_ASYNC_RECONNECT_FAIL_NO_SOURCE
  eng "Failed to automatically re-connect to a different source, for channel '%s', because %s. To fix this %s."

ER_UDF_REGISTER_SERVICE_ERROR
  eng "Could not execute the installation of UDF functions. Check for other errors in the log"

ER_UDF_REGISTER_ERROR
  eng "Could not execute the installation of UDF function: %s. Check if the function is already present, if so, try to remove it."

ER_UDF_UNREGISTER_ERROR
  eng "Could not uninstall UDF functions. Try to remove them manually if present."

ER_EMPTY_PRIVILEGE_NAME_IGNORED
  eng "An empty or illegal privilege identifier was ignored when global privileges were read from disk."

ER_IB_MSG_INCORRECT_SIZE
  eng "%s"

ER_TMPDIR_PATH_TOO_LONG
  eng "A tmpdir temporary path \"%s\" is too long (> %zu) for this OS. This would not leave enough space for a temporary filename of length %zu within it."

ER_ERROR_LOG_DESTINATION_NOT_A_FILE
  eng "Error-log destination \"%s\" is not a file. Can not restore error log messages from previous run."

ER_NO_ERROR_LOG_PARSER_CONFIGURED
  eng "None of the log-sinks selected with --log-error-services=... provides a log-parser. The server will not be able to make the previous runs' error-logs available in performance_schema.error_log."

ER_UPGRADE_NONEXISTENT_SCHEMA
  eng "The schema \"%.64s\" referenced by %.16s \"%.128s\" does not exist. Please clean up any orphan %.16s before upgrading."

ER_IB_MSG_CREATED_UNDO_SPACE
  eng "Created undo tablespace '%s'."

ER_IB_MSG_DROPPED_UNDO_SPACE
  eng "Dropped undo tablespace '%s'."

ER_IB_MSG_MASTER_KEY_ROTATED
  eng "The InnoDB Encryption Master Key has been rotated in %d tablespaces."

ER_IB_DBLWR_DECOMPRESS_FAILED
  eng "Failed to decompress a DBLWR page (err=%d).  The original size is %d. Reporting the dblwr page as corrupted."

ER_IB_DBLWR_DECRYPT_FAILED
  eng "Decrypting a page in doublewrite file failed: %s."

ER_IB_DBLWR_KEY_MISSING
  eng "Encryption key missing: %s."

ER_INNODB_IO_WRITE_ERROR_RETRYING
  eng "I/O error while writing to file: %s. Retrying ..."

ER_INNODB_IO_WRITE_FAILED
  eng "Failed to write data to file: %s"

ER_LOG_COMPONENT_CANNOT_INIT
  eng "Log component %s failed to initialize."

ER_RPL_ASYNC_CHANNEL_CANT_CONNECT
  eng "The Monitor IO thread failed to connect to the source (host:%s port:%u network_namespace:%s) for channel '%s', thence it will try to connect to another source."

ER_RPL_ASYNC_SENDER_ADDED
  eng  "The source (host:%s port:%u network_namespace:%s) for channel '%s' has joined the group (group_name: %s), and so added its entry into replication_asynchronous_connection_failover table."

ER_RPL_ASYNC_SENDER_REMOVED
  eng "The source (host:%s port:%u network_namespace:%s) for channel '%s' has left the group (group_name: %s), and so removed its entry from replication_asynchronous_connection_failover table."

ER_RPL_ASYNC_CHANNEL_STOPPED_QUORUM_LOST
  eng "The Monitor IO thread detected that the source (host:%s port:%u network_namespace:%s) does not belong to the group majority, thence the channel '%s' will try to connect to another source."

ER_RPL_ASYNC_CHANNEL_CANT_CONNECT_NO_QUORUM
  eng "The IO thread detected that the source (host:%s port:%u network_namespace:%s) does not belong to the group majority, thence the channel '%s' will try to connect to another source."

ER_RPL_ASYNC_EXECUTING_QUERY
  eng "%s on the source (host:%s port:%u network_namespace:%s) for channel '%s'."

ER_RPL_REPLICA_MONITOR_IO_THREAD_EXITING
  eng "Replica Monitor IO thread exiting."

ER_RPL_ASYNC_MANAGED_NAME_REMOVED
  eng "The group (group_name: %s) for the channel '%s' has been removed, and so removed its entry from replication_asynchronous_connection_failover_managed and all the group members from replication_asynchronous_connection_failover table."

ER_RPL_ASYNC_MANAGED_NAME_ADDED
  eng "The group (group_name: %s) for the channel '%s' has been added, and so added its entry in replication_asynchronous_connection_failover_managed and source to replication_asynchronous_connection_failover table."

ER_RPL_ASYNC_READ_FAILOVER_TABLE
  eng "Error reading failover sources for channel '%s' from replication_asynchronous_connection_failover table."

ER_RPL_REPLICA_MONITOR_IO_THREAD_RECONNECT_CHANNEL
  eng "Error %s the channel '%s', the operation will be automatically retried."

ER_REPLICA_ANON_TO_GTID_IS_LOCAL_OR_UUID_AND_GTID_MODE_NOT_ON
  eng "Replication channel '%.192s' is configured with ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS='%s', which is invalid when GTID_MODE <> ON. If you intend to use GTID_MODE = ON everywhere, change to ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS = OFF and use the procedure for enabling GTIDs online (see the documentation). If you intend to use GTIDs on this replica and cannot enable GTIDs on the source, enable GTID_MODE = ON and leave ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS = LOCAL|<UUID>. If you intend to not use GTIDs at all in the replication topology, change to ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS=OFF and leave GTID_MODE = '%s'."

ER_REPLICA_ANONYMOUS_TO_GTID_UUID_SAME_AS_GROUP_NAME
  eng "Replication channel '%.192s' is configured with ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS='%s' which is equal to group_replication_group_name. To fix this issue, either change the group_replication_group_name  or use a different value for ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS."

ER_GRP_RPL_GRP_NAME_IS_SAME_AS_ANONYMOUS_TO_GTID_UUID
  eng "The group_replication_group_name '%s' is the same as the UUID value for ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS in a server channel"

ER_WARN_GTID_THRESHOLD_BREACH
  eng "The integer component of the GTID number is high. Suggest restarting the server with a new server_uuid to prevent it from reaching the maximum number 2^63-1, which will make it impossible to write the binary log and invoke the behavior specified by binlog_error_action."

ER_HEALTH_INFO
  eng "%s"

ER_HEALTH_WARNING
  eng "%s"

ER_HEALTH_ERROR
  eng "%s"

ER_HEALTH_WARNING_DISK_USAGE_LEVEL_1
  eng "%s: Warning Level 1 (%llu MiB): mount point = '%s', available = %llu MiB, total = %llu MiB, used = %.2f%%, low limit = %llu MiB, critical level = %llu MiB"

ER_HEALTH_WARNING_DISK_USAGE_LEVEL_2
  eng "%s: Warning Level 2 (%llu MiB): mount point = '%s', available = %llu MiB, total = %llu MiB, used = %.2f%%, low limit = %llu MiB, critical level = %llu MiB"

ER_HEALTH_WARNING_DISK_USAGE_LEVEL_3
  eng "%s: Warning Level 3 (%llu MiB): mount point = '%s', available = %llu MiB, total = %llu MiB, used = %.2f%%, low limit = %llu MiB, critical level = %llu MiB"

ER_IB_INNODB_TBSP_OUT_OF_SPACE
  eng "InnoDB: Size of tablespace %s is more than the maximum size allowed."

ER_GRP_RPL_APPLIER_CHANNEL_STILL_RUNNING
  eng "The group_replication_applier channel is still running, most likely it is waiting for a database/table lock, which is preventing the channel from stopping. Please check database/table locks, including the ones created by backup tools."

ER_RPL_ASYNC_RECONNECT_GTID_MODE_OFF_CHANNEL
  eng "Detected misconfiguration: replication channel \'%.192s\' was configured with SOURCE_CONNECTION_AUTO_FAILOVER = 1, but the server was started with a value other then --gtid-mode = ON. Either reconfigure replication using CHANGE REPLICATION SOURCE TO SOURCE_CONNECTION_AUTO_FAILOVER = 0 FOR CHANNEL \'%.192s\', or change GTID_MODE to value ON, before starting the replica receiver thread."

ER_FIREWALL_SERVICES_NOT_ACQUIRED
  eng "Could not acquire required component services."

ER_FIREWALL_UDF_REGISTER_FAILED
  eng "Automatic registration of function(s) failed."

ER_FIREWALL_PFS_TABLE_REGISTER_FAILED
  eng "Automatic registration of Performance schema table(s) failed."

ER_IB_MSG_STATS_SAMPLING_TOO_LARGE
  eng "%s"

ER_AUDIT_LOG_FILE_PRUNE_FAILED
  eng "Failed to auto-prune file '%s', Error (%d): %s"

ER_AUDIT_LOG_FILE_AUTO_PRUNED
  eng "File '%s' auto-pruned"

ER_COMPONENTS_INFRASTRUCTURE_MANIFEST_INIT
  eng "Received an error while processing components from manifest file: %s"

ER_COMPONENTS_INFRASTRUCTURE_MANIFEST_DEINIT
  eng "Received an error while unloading components read from manifest file: %s"

ER_WARN_COMPONENTS_INFRASTRUCTURE_MANIFEST_NOT_RO
  eng "Manifest file '%s' is not read-only. For better security, please make sure that the file is read-only."

ER_WARN_NO_KEYRING_COMPONENT_SERVICE_FOUND
  eng "No suitable '%s' service implementation found to fulfill the request."

ER_NOTE_KEYRING_COMPONENT_INITIALIZED
  eng "Keyring component initialized successfully."

ER_KEYRING_COMPONENT_NOT_INITIALIZED
  eng "The component is not initialized properly. Make sure that configuration is proper and use ALTER INSTANCE RELOAD KEYRING to reinitialize the component."

ER_KEYRING_COMPONENT_EXCEPTION
  eng "Keyring component encountered an exception while executing : '%s' API of service: '%s'"

ER_KEYRING_COMPONENT_MEMORY_ALLOCATION_ERROR
  eng "Failed to allocated memory for '%s' while executing: '%s' API of service: '%s'"

ER_NOTE_KEYRING_COMPONENT_AES_INVALID_MODE_BLOCK_SIZE
  eng "Empty or 0 values for AES encryption mode and/or block size are not permitted."

ER_NOTE_KEYRING_COMPONENT_AES_DATA_IDENTIFIER_EMPTY
  eng "A valid data identifier is required in order to fetch the key required for the AES operation."

ER_NOTE_KEYRING_COMPONENT_AES_INVALID_KEY
  eng "Key identified by Data ID: '%s' and Auth ID: '%s' is not of type AES."

ER_NOTE_KEYRING_COMPONENT_AES_OPERATION_ERROR
  eng "Encountered error: '%s' while executing '%s' API of keyring_aes service. Key details are Data ID: '%s' and Auth ID: '%s'."

ER_NOTE_KEYRING_COMPONENT_READ_DATA_NOT_FOUND
  eng "Could not find the data corresponding to Data ID: '%s', Auth ID: '%s'."

ER_NOTE_KEYRING_COMPONENT_WRITE_MAXIMUM_DATA_LENGTH
  eng "Maximum permissible size of data is '%zu' bits"

ER_NOTE_KEYRING_COMPONENT_STORE_FAILED
  eng "Error writing data for Data ID: '%s', Auth ID: '%s'. Either data already exists with same identifier or keyring backend encountered an error."

ER_NOTE_KEYRING_COMPONENT_REMOVE_FAILED
  eng "Error removing data for Data ID: '%s', Auth ID: '%s'. Either data does not exists with same identifier or keyring backend encountered an error."

ER_NOTE_KEYRING_COMPONENT_GENERATE_FAILED
  eng "Error generating data for Data ID: '%s', Auth ID: '%s'. Either data already exists with same identifier or keyring backend encountered an error."

ER_NOTE_KEYRING_COMPONENT_KEYS_METADATA_ITERATOR_FETCH_FAILED
  eng "Failed to get metadata from current keys metadata iterator position."

ER_NOTE_KEYRING_COMPONENT_METADATA_ITERATOR_INVALID_OUT_PARAM
  eng "Key and value length parameters must not be null."

ER_IB_WRN_FAILED_TO_ACQUIRE_SERVICE
  eng "Innodb could not acquire service : %s"

ER_IB_WRN_OLD_GEOMETRY_TYPE
  eng "Column %s of type GEOMETRY is in old (5.6) format which could be deprecated in the future. To change the format to latest, please consider rebuilding the table after the upgrade."

ER_NET_WAIT_ERROR2
  eng "'wait_timeout' period of %s seconds was exceeded for %s. The idle time since last command was too long."

ER_GRP_RPL_MEMBER_ACTION_TRIGGERED
  eng "The member action "%s" for event "%s" with priority "%u" will be run."

ER_GRP_RPL_MEMBER_ACTION_FAILURE_IGNORE
  eng "The member action "%s" for event "%s" with priority "%u" failed, this error is ignored as instructed. Please check previous messages in the error log for hints about what could have caused this failure."

ER_GRP_RPL_MEMBER_ACTION_FAILURE
  eng "The member action "%s" for event "%s" with priority "%u" failed. Please check previous messages in the error log for hints about what could have caused this failure."

ER_GRP_RPL_MEMBER_ACTION_PARSE_ON_RECEIVE
  eng "Unable to parse the member actions configuration sent by the primary."

ER_GRP_RPL_MEMBER_ACTION_UPDATE_ACTIONS
  eng "Unable to update the member actions configuration with the one sent by the primary. Please check the tables 'mysql.replication_group_member_actions' and 'mysql.replication_group_configuration_version'."

ER_GRP_RPL_MEMBER_ACTION_GET_EXCHANGEABLE_DATA
  eng "Unable to read the member actions configuration during group membership change. Please check the tables 'mysql.replication_group_member_actions' and 'mysql.replication_group_configuration_version'."

ER_GRP_RPL_MEMBER_ACTION_DEFAULT_CONFIGURATION
  eng "This member joined a group on which all members do not support member actions, as such it did reset its member configuration to the default one."

ER_GRP_RPL_MEMBER_ACTION_UNABLE_TO_SET_DEFAULT_CONFIGURATION
  eng "Unable to reset to member actions default configuration on member join. Please check the tables 'mysql.replication_group_member_actions' and 'mysql.replication_group_configuration_version'."

ER_GRP_RPL_MEMBER_ACTION_PARSE_ON_MEMBER_JOIN
  eng "Unable to parse the member actions configuration sent by the group on member join."

ER_GRP_RPL_MEMBER_ACTION_UPDATE_ACTIONS_ON_MEMBER_JOIN
  eng "Unable to update the member actions configuration on member join. Please check the tables 'mysql.replication_group_member_actions' and 'mysql.replication_group_configuration_version'."

ER_GRP_RPL_MEMBER_ACTION_INVALID_ACTIONS_ON_MEMBER_JOIN
  eng "The group members were unable to send their member actions configuration. Please check the tables 'mysql.replication_group_member_actions' and 'mysql.replication_group_configuration_version' on all members."

ER_GRP_RPL_MEMBER_ACTION_ENABLED
  eng "Member action enabled: "%s", type: "%s", event: "%s", priority: "%u", error_handling: "%s"."

ER_GRP_RPL_MEMBER_ACTION_DISABLED
  eng "Member action disabled: "%s", type: "%s", event: "%s", priority: "%u", error_handling: "%s"."

ER_GRP_RPL_MEMBER_ACTIONS_RESET
  eng "Member actions configuration was reset."

OBSOLETE_ER_DEPRECATED_TLS_VERSION_SESSION
  eng "Accepted a connection with deprecated protocol '%s' for account `%s`@`%s` from host `%s`. Client supplied username `%s`"

OBSOLETE_ER_WARN_DEPRECATED_TLS_VERSION_FOR_CHANNEL
  eng "A deprecated TLS version %s is enabled for channel %s"

ER_FIREWALL_DEPRECATED_USER_PROFILE
  eng "User profile \'%s\' loaded. Firewall user profiles are deprecated, consider migrating to group profiles."

ER_GRP_RPL_VIEW_CHANGE_UUID_INVALID
  eng "Invalid input value for group_replication_view_change_uuid '%s'. Please, provide a valid UUID."

ER_GRP_RPL_VIEW_CHANGE_UUID_SAME_AS_GROUP_NAME
  eng "Variable 'group_replication_view_change_uuid' cannot be set to the value of '%s'. If you want to use the UUID of 'group_replication_group_name' for the UUID of View_change_log_events, please set 'group_replication_view_change_uuid' to AUTOMATIC."

ER_GRP_RPL_GROUP_NAME_SAME_AS_VIEW_CHANGE_UUID
  eng "group_replication_group_name '%s', which is the same as group_replication_view_change_uuid. Please change group_replication_view_change_uuid to AUTOMATIC"

ER_GRP_RPL_VIEW_CHANGE_UUID_IS_SAME_AS_ANONYMOUS_TO_GTID_UUID
  eng "The group_replication_view_change_uuid '%s' is the same as the UUID value for ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS in a server channel"

ER_GRP_RPL_GRP_VIEW_CHANGE_UUID_IS_INCOMPATIBLE_WITH_SERVER_UUID
  eng "group_replication_view_change_uuid is incompatible with group. group_replication_view_change_uuid %s matches server_uuid %s."

ER_GRP_RPL_VIEW_CHANGE_UUID_DIFF_FROM_GRP
  eng "The member is configured with a group_replication_view_change_uuid option value '%s' different from the group '%s'. The member will now exit the group."

ER_WARN_REPLICA_ANONYMOUS_TO_GTID_UUID_SAME_AS_VIEW_CHANGE_UUID
  eng "Replication channel '%.192s' is configured with ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS='%s' which is equal to group_replication_view_change_uuid. To fix this issue, either change the group_replication_view_change_uuid or use a different value for ASSIGN_GTIDS_TO_ANONYMOUS_TRANSACTIONS."

ER_GRP_RPL_FAILED_TO_PARSE_THE_VIEW_CHANGE_UUID
  eng "Unable to parse the group_replication_view_change_uuid."

ER_GRP_RPL_FAILED_TO_GENERATE_SIDNO_FOR_VIEW_CHANGE_UUID
  eng "Unable to generate sidno for group_replication_view_change_uuid."

ER_GRP_RPL_VIEW_CHANGE_UUID_PARSE_ERROR
  eng "Unable to parse the group_replication_view_change_uuid during the Certification module initialization."

ER_GRP_RPL_UPDATE_GRPGTID_VIEW_CHANGE_UUID_EXECUTED_ERROR
  eng "Error updating group_gtid_executed GTID set with view change uuid during the Certification module initialization."

ER_GRP_RPL_ADD_VIEW_CHANGE_UUID_TO_GRP_SID_MAP_ERROR
  eng "Unable to add the group_replication_view_change_uuid sidno in the group_gtid_sid_map during the Certification module initialization."

ER_GRP_RPL_DONOR_VIEW_CHANGE_UUID_TRANS_INFO_ERROR
  eng "Unable to handle the donor's view change uuid transaction information when initializing the conflict detection component. Possible out of memory error."

ER_WARN_GRP_RPL_VIEW_CHANGE_UUID_FAIL_GET_VARIABLE
  eng "Unable to retrieve group_replication_view_change_uuid during server checks on replication operations."

ER_WARN_ADUIT_LOG_MAX_SIZE_AND_PRUNE_SECONDS_LOG
  eng "Both audit_log_max_size and audit_log_prune_seconds are set to non-zero. audit_log_max_size takes precedence and audit_log_prune_seconds is ignored"

ER_WARN_ADUIT_LOG_MAX_SIZE_CLOSE_TO_ROTATE_ON_SIZE_LOG
  eng "audit_log_rotate_on_size is not granular enough for the value of audit_log_max_size supplied. Should be at least %d times smaller."

ER_PLUGIN_INVALID_TABLE_DEFINITION
  eng "Invalid table definition for '%s.%s'."

ER_AUTH_KERBEROS_LOGGER_GENERIC_MSG
  eng "%s"

ER_INSTALL_PLUGIN_CONFLICT_LOG
  eng "Cannot install the %.192s plugin when the %.192s plugin is installed."

ER_DEPRECATED_PERSISTED_VARIABLE_WITH_ALIAS
  eng "The variable %s has been renamed to %s, and the old name deprecated. Only the old name was found in the persisted variable file. Next time the file is saved, both names will be stored. Issue any SET PERSIST command to save the file, get rid of this warning, and prepare the persisted configuration for when the variable is removed in a future version."

ER_LOG_COMPONENT_FLUSH_FAILED
  eng "%d error logging component(s) failed to flush. For file-based logs this can happen when the path or permissions of the log-file have changed. Failure to flush filed-based logs may affect log-rotation."

ER_IB_MSG_REENCRYPTED_TABLESPACE_KEY
  eng "Tablespace key for %s has been re-encrypted using the latest InnoDB master key. However, we recommend that you rebuild the table for better security."

ER_IB_MSG_REENCRYPTED_GENERAL_TABLESPACE_KEY
  eng "Tablespace key for %s has been re-encrypted using the latest InnoDB master key. However, we recommend that you reencrypt the tablespace for better security."

ER_IB_ERR_PAGE_ARCH_DBLWR_INIT_FAILED
  eng "Page Archiver's doublewrite buffer initialisation failed. Page tracking is at risk of losing tracked information."

ER_IB_MSG_RECOVERY_NO_SPACE_IN_REDO_LOG__SKIP_IBUF_MERGES
  eng "There is not enough free space in the redo log during recovery to perform pending ibuf merges. Please retry starting MySQL with --innodb-force-recovery=4."

ER_IB_MSG_RECOVERY_NO_SPACE_IN_REDO_LOG__UNEXPECTED
  eng "There is not enough free space in the redo log during recovery, restore from backup (or retry with --innodb-force-recovery=6)."

ER_WARN_AUDIT_LOG_FORMAT_UNIX_TIMESTAMP_ONLY_WHEN_JSON_LOG
  eng "audit_log_format_unix_timestamp is applicable only when audit_log_format = JSON."

# This error is not supposed to be reported to the users. It is only
# meant for internal use to signal that a statement should be
# reprepared for the primary storage engine, without transformations.
# The error should be caught and handled by the server.
ER_PREPARE_FOR_PRIMARY_ENGINE
  eng "Retry the statement using the primary storage engine."

ER_IB_MSG_PAR_RSEG_INIT_COMPLETE_MSG
  eng "Parallel initialization of rseg complete"

ER_IB_MSG_PAR_RSEG_INIT_TIME_MSG
  eng "Time taken to initialize rseg using %u thread: %u ms."

ER_DDL_MSG_1
  eng "DDL failed to create a thread to load an index, fall back to single thread"

ER_MTR_MSG_1
  eng "Debug_check_no_latching failed, slot->type=%d"

ER_GRP_RPL_MYSQL_NETWORK_PROVIDER_CLIENT_ERROR_CONN_ERR
  eng "Failed to establish MySQL client connection in Group Replication. Error establishing connection. Please refer to the manual to make sure that you configured Group Replication properly to work with MySQL Protocol connections."

ER_GRP_RPL_MYSQL_NETWORK_PROVIDER_CLIENT_ERROR_COMMAND_ERR
  eng "Failed to establish MySQL client connection in Group Replication. Error sending connection delegation command. Please refer to the manual to make sure that you configured Group Replication properly to work with MySQL Protocol connections."

ER_GRP_RPL_FAILOVER_CONF_GET_EXCHANGEABLE_DATA
  eng "Unable to read the replication failover channels configuration during group membership change. Please check the tables 'mysql.replication_asynchronous_connection_failover', 'mysql.replication_asynchronous_connection_failover_managed' and 'mysql.replication_group_configuration_version'."

ER_GRP_RPL_FAILOVER_CONF_DEFAULT_CONFIGURATION
  eng "This member joined a group on which all members do not support replication failover channels integration on Group Replication, as such it did reset its replication failover channels configuration to the default one."

ER_GRP_RPL_FAILOVER_CONF_UNABLE_TO_SET_DEFAULT_CONFIGURATION
  eng "Unable to reset to replication failover channels default configuration on member join. Please check the tables 'mysql.replication_asynchronous_connection_failover', 'mysql.replication_asynchronous_connection_failover_managed' and 'mysql.replication_group_configuration_version'."

ER_GRP_RPL_FAILOVER_CONF_PARSE_ON_MEMBER_JOIN
  eng "Unable to parse the replication failover channels configuration sent by the group on member join."

ER_GRP_RPL_FAILOVER_CONF_CHANNEL_DOES_NOT_EXIST
  eng "Unable to set SOURCE_CONNECTION_AUTO_FAILOVER on a non-existent or misconfigured replication channel '%s', please create the channel and rejoin the server to the group."

ER_GRP_RPL_FAILOVER_REGISTER_MESSAGE_LISTENER_SERVICE
  eng "Unable to register the listener 'replication_asynchronous_connection_failover_configuration' to the service 'group_replication_message_service_recv'."

ER_GRP_RPL_FAILOVER_PRIMARY_WITHOUT_MAJORITY
  eng "This server is not able to reach a majority of members in the group. This server will skip the replication failover channels handling until this server is back to the group majority."

ER_GRP_RPL_FAILOVER_PRIMARY_BACK_TO_MAJORITY
  eng "This server is back to the group majority. Replication failover channels handling is resumed."

ER_RPL_INCREMENTING_MEMBER_ACTION_VERSION
  eng "Error incrementing member action configuration version for %s.%s table."

ER_GRP_RPL_REPLICA_THREAD_ERROR_ON_SECONDARY_MEMBER
  eng "The '%s' thread of channel '%s' will error out as this server is a group secondary."

ER_IB_MSG_CLONE_DDL_NTFN
  eng "Clone DDL Notification: %s"

ER_IB_MSG_CLONE_DDL_APPLY
  eng "Clone DDL APPLY: %s"

ER_IB_MSG_CLONE_DDL_INVALIDATE
  eng "Clone DDL Invalidate : %s"

ER_IB_MSG_UNDO_ENCRYPTION_INFO_LOADED
  eng "Encryption key is loaded for undo tablespace '%s'."

ER_IB_WRN_ENCRYPTION_INFO_SIZE_MISMATCH
  eng "Ignoring encryption INFO size in redo log: %zu, expected: %zu"

ER_INVALID_AUTHENTICATION_POLICY
  eng "Option --authentication-policy is set to an invalid value. Please check if the specified authentication plugins are valid."

ER_AUTHENTICATION_PLUGIN_REGISTRATION_FAILED
  eng "Signature verification failed during registration."

ER_AUTHENTICATION_PLUGIN_REGISTRATION_INSUFFICIENT_BUFFER
  eng "Buffer too small to hold registration challenge response."

ER_AUTHENTICATION_PLUGIN_AUTH_DATA_CORRUPT
  eng "FIDO device authenticator data corrupt."

ER_AUTHENTICATION_PLUGIN_SIGNATURE_CORRUPT
  eng "FIDO device signature corrupt."

ER_AUTHENTICATION_PLUGIN_VERIFY_SIGNATURE_FAILED
  eng "Signature verification failed during authentication."

ER_AUTHENTICATION_PLUGIN_OOM
  eng "Out of memory."

ER_AUTHENTICATION_PLUGIN_LOG
  eng "Can't initialize logging service".

ER_WARN_REPLICA_GTID_ONLY_AND_GTID_MODE_NOT_ON
  eng "Replication channel '%.192s' is configured with GTID_ONLY=1, which is invalid when GTID_MODE <> ON. If you intend to disable GTIDs in the replication topology, change GTID_ONLY to 0."

ER_WARN_L_DISABLE_GTID_ONLY_WITH_SOURCE_AUTO_POS_INVALID_POS
  eng "The replication positions relative to the source may be out-of-date on channel '%.192s', due to the use of GTID_ONLY=1. The out-of-date positions can still be used in some cases so, in order to update them, we suggest that you start the replication to receive and apply at least one transaction, which will set the positions to valid values."

ER_RPL_CANNOT_OPEN_RELAY_LOG
  eng "Could not open relay log: %s"

ER_AUTHENTICATION_OCI_PLUGIN_NOT_INITIALIZED
  eng "Authentication plugin not initialized."

ER_AUTHENTICATION_OCI_PRIVATE_KEY_ERROR
  eng "Cannot use the generated private key file."

ER_AUTHENTICATION_OCI_DOWNLOAD_PUBLIC_KEY
  eng "Unavailable public key with fingerprint %s for user %s in tenancy %s ."

ER_AUTHENTICATION_OCI_IMDS
  eng "Cannot obtain the OCI configuration from the IMDS service."

ER_AUTHENTICATION_OCI_IAM
  eng "Cannot initialize the IAM service."

ER_AUTHENTICATION_OCI_INVALID_AUTHENTICATION_STRING
  eng "Invalid authentication string details for user: `%s`@`%s`."

ER_AUTHENTICATION_OCI_NO_MATCHING_GROUPS
  eng "None of the groups returned by IAM matches any of the entries from authentication string."

ER_AUTHENTICATION_OCI_NO_GROUPS_FOUND
  eng "User is not part of any groups. However, account is configured to use group mapping."

ER_AUTHENTICATION_OCI_NONCE
  eng "Received OpenSSL error: %s while authenticating user %s with fingerprint %s in tenancy %s ."

ER_HEALTH_WARNING_MEMORY_USAGE_LEVEL_1
  eng "%s: Warning Level 1 (%llu MiB): available=%llu MiB, total=%llu MiB, used=%.2f%%, mysqld=%llu MiB"

ER_HEALTH_WARNING_MEMORY_USAGE_LEVEL_2
  eng "%s: Warning Level 2 (%llu MiB): available=%llu MiB, total=%llu MiB, used=%.2f%%, mysqld=%llu MiB"

ER_HEALTH_WARNING_MEMORY_USAGE_LEVEL_3
  eng "%s: Warning Level 3 (%llu MiB): available=%llu MiB, total=%llu MiB, used=%.2f%%, mysqld=%llu MiB"

ER_GRP_RPL_SET_SINGLE_CONSENSUS_LEADER
  eng "The member %s:%u, with UUID: %s, was set as the single preferred consensus leader."

ER_GRP_RPL_ERROR_SET_SINGLE_CONSENSUS_LEADER
  eng "Something went wrong trying to set the member %s:%u, with UUID: %s, as the single preferred consensus leader. Please query the performance_schema.replication_group_communication_information table to see whether the operation took effect, i.e. whether the preferred consensus leader matches the current primary. If not, consider electing a different primary to try again. Please check the error log and GCS_DEBUG_TRACE for more information that may help understanding what went wrong."

ER_GRP_RPL_SET_MULTI_CONSENSUS_LEADER
  eng "All members were set as consensus leaders."

ER_GRP_RPL_ERROR_SET_MULTI_CONSENSUS_LEADER
  eng "Something went wrong trying to set all members as consensus leaders. Please query the performance_schema.replication_group_communication_information table to see whether the operation took effect, i.e. whether the consensus leaders match all members. If not, consider resetting the group communication protocol to a version < 8.0.22, or switch to single-primary mode and back again to multi-primary mode, to try again. Please check the error log and GCS_DEBUG_TRACE for more information that may help understanding what went wrong."

ER_GRP_RPL_PAXOS_SINGLE_LEADER_DIFF_FROM_GRP
  eng "This member is configured with a group_replication_paxos_single_leader option value of '%d' that is different from the group's value ('%d'). This member will now exit the group."

ER_MFA_USER_ATTRIBUTES_CORRUPT
  eng "Invalid and/or corrupted multi factor authentication methods in User_attributes column in mysql.user table. \"%s\"."

ER_MFA_PLUGIN_NOT_LOADED
  eng "Plugin \'%-.192s\' is not loaded; Ignoring user"

ER_WARN_DEPRECATED_CHARSET_OPTION
  eng "%s: The character set %s is deprecated and will be removed in a future release. Please consider using %s instead."

ER_WARN_DEPRECATED_COLLATION_OPTION
  eng "%s: '%-.64s' is a collation of the deprecated character set %s. Please consider using %s with an appropriate collation instead."

ER_REGEXP_MISSING_ICU_DATADIR
  eng "Missing data directory for ICU regular expressions: %s."

ER_IB_WARN_MANY_NON_LRU_FILES_OPENED
  eng "More than 90%% of files opened out of the innodb_open_files limit are files that are not easy to close. The performance of system may degrade. Consider increasing value of the innodb_open_files system variable. There are %zu such files opened out of the total limit for all files opened of %zu."

ER_IB_MSG_TRYING_TO_OPEN_FILE_FOR_LONG_TIME
  eng "Trying to open a file for %lld seconds. Configuration only allows for %zu open files. Consider setting innobase_open_files higher."

ER_GLOBAL_CONN_LIMIT
  eng "Connection closed. Global connection memory limit %llu bytes exceeded. Consumed %llu bytes."

ER_CONN_LIMIT
  eng "Connection closed. Connection memory limit %llu bytes exceeded. Consumed %llu bytes."

ER_WARN_AUDIT_LOG_DISABLED
  eng "Audit Log is disabled. Enable it with audit_log_disable = false."

ER_INVALID_TLS_VERSION
  eng "Option --tls-version or --admin-tls-version is set to an invalid value %s."

ER_RPL_RELAY_LOG_RECOVERY_GTID_ONLY
  eng "Relay log recovery on channel with GTID_ONLY=1. The channel will switch to a new relay log and the GTID protocol will be used to replicate unapplied transactions."

ER_KEYRING_OKV_STANDBY_SERVER_COUNT_EXCEEDED
  eng "Number of STANDBY_SERVER values exceeded maximum limit of 64."

ER_WARN_MIGRATION_EMPTY_SOURCE_KEYRING
  eng "Source keyring does not have any keys to migrate."

ER_WARN_CANNOT_PERSIST_SENSITIVE_VARIABLES
  eng "Cannot persist SENSITIVE system variables because keyring component support is unavailable and persist_sensitive_variables_in_plaintext is set to OFF. Please make sure that keyring services are active and required keys are available."

ER_CANNOT_INTERPRET_PERSISTED_SENSITIVE_VARIABLES
  eng "Cannot interpret persisted SENSITIVE system variables. Please make sure that keyring services are active and required keys are available."

ER_PERSISTED_VARIABLES_KEYRING_SUPPORT_REQUIRED
  eng "Keyring has to be loaded through manifest file in order to support secure storage for persisted variables"

ER_PERSISTED_VARIABLES_MASTER_KEY_NOT_FOUND
  eng "Could not find master key %s in keyring"

ER_PERSISTED_VARIABLES_MASTER_KEY_CANNOT_BE_GENERATED
  eng "A new master key %s could not be generated"

ER_PERSISTED_VARIABLES_ENCRYPTION_FAILED
  eng "Failed to encrypt %s using %s"

ER_PERSISTED_VARIABLES_DECRYPTION_FAILED
  eng "Failed to decrypt %s using %s"

ER_PERSISTED_VARIABLES_LACK_KEYRING_SUPPORT
  eng "Persisting SENSITIVE variables in encrypted form requires keyring component loaded through manifest file."

ER_MY_MALLOC_USING_JEMALLOC
  eng "Using jemalloc.dll for my_malloc and ut::malloc etc."

ER_MY_MALLOC_USING_STD_MALLOC
  eng "%s."

ER_MY_MALLOC_LOADLIBRARY_FAILED
  eng "%s."

ER_MY_MALLOC_GETPROCADDRESS_FAILED
  eng "%s."

ER_ACCOUNT_WITH_EXPIRED_PASSWORD
  eng "Password for the account '%-.48s'@'%-.64s' has expired. To log in, either change it using a client that supports expired passwords or send the change request to an administrator."

ER_THREAD_POOL_PLUGIN_STARTED
  eng "Thread pool plugin started successfully with parameters: %s"

ER_THREAD_POOL_DEDICATED_LISTENERS_INVALID
  eng "thread_pool_dedicated_listeners cannot be set unless thread_pool_max_transactions_limit > 0"

ER_IB_DBLWR_BYTES_INFO
  eng "%s"

ER_IB_RDBLWR_BYTES_INFO
  eng "%s"

ER_IB_MSG_LOG_FILE_IS_EMPTY
  eng "The redo log file %s is empty, which indicates it was not generated by InnoDB or become corrupted. Please restore the correct file or try recovering without the redo files, in read-only mode, by providing --innodb-force-recovery=6."

ER_IB_MSG_LOG_FILE_TOO_SMALL
  eng "The redo log file %s is smaller than %llu bytes, which indicates it was not generated by InnoDB or become corrupted. Please restore the correct file."

ER_IB_MSG_LOG_FILE_TOO_BIG
  eng "The redo log file %s is larger than %llu bytes, which indicates it was not generated by InnoDB or become corrupted. Please restore the correct file."

ER_IB_MSG_LOG_FILE_HEADER_READ_FAILED
  eng "Failed to read header of the redo log file %s"

ER_IB_MSG_LOG_INIT_DIR_NOT_EMPTY_WONT_INITIALIZE
  eng "--initialize specified but the redo log directory %s has redo log files inside. Aborting."

ER_IB_MSG_LOG_INIT_DIR_LIST_FAILED
  eng "Failed to list redo log files in the redo log directory %s"

ER_IB_MSG_LOG_INIT_DIR_MISSING_SUBDIR
  eng "Neither found %s subdirectory, nor %s* files in %s"

ER_IB_MSG_LOG_FILES_CREATED_BY_CLONE_AND_READ_ONLY_MODE
  eng "Cannot restore cloned data directory, InnoDB running in read-only mode!"

ER_IB_MSG_LOG_WRITER_WRITE_FAILED
  eng "Error %d encountered when writing to the redo log file: %s."

ER_IB_MSG_LOG_WRITER_WAIT_ON_NEW_LOG_FILE
  eng "Redo log writer is waiting for a new redo log file. Consider increasing innodb_redo_log_capacity."

ER_IB_MSG_RECOVERY_CHECKPOINT_OUTSIDE_LOG_FILE
  eng "Found checkpoint LSN %llu in a redo log file %s, but the file represents range of LSN values [%llu, %llu), so the file is corrupted."

ER_IB_MSG_LOG_WRITER_ENTERED_EXTRA_MARGIN
  eng "Redo log is running out of free space, pausing user threads... Consider increasing innodb_redo_log_capacity."

ER_IB_MSG_LOG_WRITER_EXITED_EXTRA_MARGIN
  eng "Redo log reclaimed some free space, resuming user threads."

ER_IB_MSG_LOG_PARAMS_FILE_SIZE_UNUSED
  eng "Ignored deprecated configuration parameter innodb_log_file_size. Used innodb_redo_log_capacity instead."

ER_IB_MSG_LOG_PARAMS_N_FILES_UNUSED
  eng "Ignored deprecated configuration parameter innodb_log_files_in_group. Used innodb_redo_log_capacity instead."

ER_IB_MSG_LOG_UPGRADE_FORCED_RECV
  eng "Cannot upgrade format (v%lu) of redo log files when innodb-force-recovery > 0."

ER_IB_MSG_LOG_UPGRADE_IN_READ_ONLY_MODE
  eng "Cannot upgrade format (v%lu) of redo log files in read-only mode (--innodb-read-only)."

ER_IB_MSG_LOG_UPGRADE_CLONED_DB
  eng "Cannot upgrade format (v%lu) of redo log files on cloned data directory. Please use an older version of MySQL - recover and shutdown (with innodb-fast-shutdown < 2)."

ER_IB_MSG_LOG_UPGRADE_UNINITIALIZED_FILES
  eng "Cannot upgrade format (v%lu) of redo log files because they are marked as uninitialized. Please use an older version of MySQL - recover and shutdown (with innodb-fast-shutdown < 2)."

ER_IB_MSG_LOG_UPGRADE_CORRUPTION__UNEXPECTED
  eng "Cannot upgrade format (v%lu) of redo log files when the redo log is corrupted. Please use an older version of MySQL - recover and shutdown (with innodb-fast-shutdown < 2)."

OBSOLETE_ER_IB_MSG_LOG_UPGRADE_NON_PERSISTED_DD_METADATA
  eng "Cannot upgrade format (v%lu) of redo log files when there is non-persisted DD metadata in redo. Please use an older version of MySQL - recover and shutdown (with innodb-fast-shutdown < 2)."

OBSOLETE_ER_IB_MSG_LOG_UPGRADE_FLUSH_FAILED__UNEXPECTED
  eng "Cannot upgrade format (v%lu) of redo log files, because InnoDB failed to reach state in which redo log is logically empty. Please use an older version of MySQL - recover and shutdown (with innodb-fast-shutdown < 2)."

OBSOLETE_ER_IB_MSG_LOG_FILES_RESIZE_ON_START_FAILED__UNEXPECTED
  eng "Failed to resize the redo log synchronously, because InnoDB failed to reach state in which redo log is logically empty. Please use an older version of MySQL - recover and shutdown (with innodb-fast-shutdown < 2)."

ER_IB_MSG_LOG_FILE_FOREIGN_UUID
  eng "The redo log file %s comes from other data directory than redo log file %s."

ER_IB_MSG_LOG_FILE_INVALID_START_LSN
  eng "The redo log file %s has invalid start_lsn %llu."

ER_IB_MSG_LOG_FILE_INVALID_LSN_RANGES
  eng "The redo log file %s has start_lsn %llu but expected %llu (end_lsn of the previous redo log file)."

ER_IB_MSG_LOG_FILE_MISSING_FOR_ID
  eng "Missing redo log file %s (with start_lsn = %llu)."

ER_IB_MSG_LOG_CHECKPOINT_FOUND
  eng "The latest found checkpoint is at lsn = %llu in redo log file %s."

ER_IB_MSG_LOG_FILES_CAPACITY_CHANGED
  eng "User has set innodb_redo_log_capacity to %lluM."

ER_IB_MSG_LOG_FILES_RESIZE_REQUESTED
  eng "Redo log has been requested to resize from %lluM to %lluM."

ER_IB_MSG_LOG_FILES_RESIZE_CANCELLED
  eng "Redo log resize has been cancelled."

ER_IB_MSG_LOG_FILES_RESIZE_FINISHED
  eng "Redo log has been resized to %lluM."

ER_IB_MSG_LOG_FILES_UPGRADE
  eng "Upgrading redo log: %lluM, LSN=%llu."

ER_IB_MSG_LOG_FILE_MARK_CURRENT_AS_INCOMPLETE
  eng "Marked the current redo log file %s as incomplete."

ER_IB_MSG_LOG_FILE_REMOVE_FAILED
  eng "Failed to remove redo log file %s."

ER_IB_MSG_LOG_FILE_RENAME_ON_CREATE_FAILED
  eng "Failed to rename %s when creating redo log file %s (error: %d)"

ER_IB_MSG_LOG_FILES_CREATED_BY_UNKNOWN_CREATOR
  eng "Redo log files created by unknown creator %s."

ER_IB_MSG_LOG_FILES_FOUND_MISSING
  eng "Found existing redo log files, but at least one is missing. It is unknown if recovery could reach physically consistent state. Please consider restoring from backup or providing --innodb-force-recovery > 0."

ER_IB_MSG_LOG_FILE_FORMAT_TOO_NEW
  eng "Found redo log file %s which has format (v%lu) and is stored outside #innodb_redo."

ER_IB_MSG_LOG_FILE_FORMAT_TOO_OLD
  eng "Found redo log file %s which has format (v%lu) and is stored inside #innodb_redo."

ER_IB_MSG_LOG_FILE_DIFFERENT_FORMATS
  eng "Found redo log files with different formats: %s has format v%lu, %s has format v%lu."

ER_IB_MSG_LOG_PRE_8_0_30_MISSING_FILE0
  eng "Missing ib_logfile0 in the directory %s."

ER_IB_MSG_LOG_PFS_ACQUIRE_SERVICES_FAILED
  eng "Failed to initialize services required to handle redo log PFS tables."

ER_IB_MSG_LOG_PFS_CREATE_TABLES_FAILED
  eng "Failed to create redo log PFS tables."

ER_IB_MSG_LOG_FILE_TRUNCATE
  eng "Truncating redo log file %s..."

ER_IB_MSG_LOG_FILE_UNUSED_RESIZE_FAILED
  eng "Failed to resize unused redo log file %s to %llu MB (%s)."

ER_IB_MSG_LOG_FILE_UNUSED_REMOVE_FAILED
  eng "Failed to remove unused redo log file %s."

ER_IB_MSG_LOG_FILE_UNUSED_RENAME_FAILED
  eng "Failed to rename unused redo log file %s to %s."

ER_IB_MSG_LOG_FILE_UNUSED_MARK_AS_IN_USE_FAILED
  eng "Failed to mark unused redo log file %s as in use (by renaming to %s)."

ER_IB_MSG_LOG_FILE_MARK_AS_UNUSED_FAILED
  eng "Failed to mark redo log file %s as unused (by renaming to %s)."

ER_IB_MSG_LOG_PARAMS_DEDICATED_SERVER_IGNORED
  eng "Option innodb_dedicated_server is ignored for innodb_redo_log_capacity, because innodb_redo_log_capacity, innodb_log_file_size or innodb_log_files_in_group is specified explicitly. Redo log capacity: %lluM."

ER_IB_MSG_LOG_PARAMS_LEGACY_USAGE
  eng "Deprecated configuration parameters innodb_log_file_size and/or innodb_log_files_in_group have been used to compute innodb_redo_log_capacity=%llu. Please use innodb_redo_log_capacity instead."

ER_GRP_RPL_FAILED_TO_LOG_VIEW_CHANGE
  eng "This member was unable to log the View_change_log_event into the binary log, hence it will leave the group. Please check that there is available disk space and add the member back to the group."

ER_BINLOG_CRASH_RECOVERY_MALFORMED_LOG
  eng "Found invalid event sequence while recovering from binary log file '%s', between positions %llu and %llu: %s. The recovery process was stopped early and no transaction was recovered. Side effects may be transactions in an inconsistent state between the binary log and the storage engines, or transactions kept by storage engines in a prepared state (possibly holding locks). Either fix the issues with the binary log or, to release possibly acquired locks, disable the binary log during server recovery. Note that disabling the binary log may lead to loss of transactions that were already acknowledged as successful to client connections and may have been replicated to other servers in the topology."

ER_BINLOG_CRASH_RECOVERY_ERROR_RETURNED_SE
  eng "Storage engines failed to recover one or more transactions. The recovery process was stopped early, check previous messages for the details on failed transactions. Side effects may be transactions in an inconsistent state between the binary log and the storage engines, or transactions kept by storage engines in a prepared state (possibly holding locks). Either fix the issues with the storage engine (out-of-memory, no disk space, etc) or, to release possibly acquired locks held by XA transactions, disable the binary log during server recovery and check consistency between storage engines and binary log files."

ER_BINLOG_CRASH_RECOVERY_ENGINE_RESULTS
  eng "Crash recovery finished in %s engine. %s"

ER_BINLOG_CRASH_RECOVERY_COMMIT_FAILED
  eng "Failed to commit %s in %s, with failure code %s."

ER_BINLOG_CRASH_RECOVERY_ROLLBACK_FAILED
  eng "Failed to rollback %s in %s, with failure code %s."

ER_BINLOG_CRASH_RECOVERY_PREPARE_FAILED
  eng "Failed to prepare %s in %s, with failure code %s."
ER_COMPONENT_EE_SYS_VAR_REGISTRATION_FAILURE
  eng "Cannot register variable '%s'. Please check if it is not already registered by another component."

ER_COMPONENT_EE_SYS_VAR_DEREGISTRATION_FAILURE
  eng "Cannot unregister variable '%s'. Please check if it was registered properly in the first place."

ER_COMPONENT_EE_FUNCTION_REGISTRATION_FAILURE
  eng "Cannot register function '%s'. Please check if it is not already registered."

ER_COMPONENT_EE_FUNCTION_DEREGISTRATION_FAILURE
  eng "Cannot unregister function '%s'. Please check if it was registered properly in the first place."

ER_COMPONENT_EE_FUNCTION_INVALID_ARGUMENTS
  eng "Invalid arguments to function '%s'. Expected values: %s."

ER_COMPONENT_EE_FUNCTION_INVALID_ALGORITHM
  eng "Invalid algorithm value '%.20s' in function %s."

ER_COMPONENT_EE_FUNCTION_KEY_LENGTH_OUT_OF_RANGE
  eng "Invalid key length '%d' for function '%s' with algorithm '%.20s'. Please provide a value between [%d, %d]."

ER_COMPONENT_EE_FUNCTION_PRIVATE_KEY_GENERATION_FAILURE
  eng "Failed to generate private key of length '%d' with algorithm '%.20s' in function '%s'. More details about the error would have been logged before this message."

ER_COMPONENT_EE_FUNCTION_PUBLIC_KEY_GENERATION_FAILURE
  eng "Failed to generate public key with algorithm '%.20s' for given private key in function '%s'. More details about the error would have been logged before this message."

ER_COMPONENT_EE_DATA_LENGTH_OUT_OF_RAGE
  eng "Invalid data length '%d' (in bytes) for function '%s' with algorithm '%.20s'. The maximum support data length for given private key is %d (in bytes)."

ER_COMPONENT_EE_DATA_ENCRYPTION_ERROR
  eng "Could not encrypt data in function '%s' using algorithm '%.20s'. Data length was '%d' bytes. More details about the error would have been logged before this message."

ER_COMPONENT_EE_DATA_DECRYPTION_ERROR
  eng "Could not decrypt data in function '%s' using algorithm '%.20s'. Data length was '%d' bytes. More details about the error would have been logged before this message."

ER_COMPONENT_EE_DATA_SIGN_ERROR
  eng "Could not sign data in function '%s' using algorithm '%.20s' and digest type '%.20s'. More details about the error would have logged before this message."

ER_COMPONENT_EE_OPENSSL_ERROR
  eng "Received OpenSSL error in function '%s' for algorithm '%s': '%s'"

ER_COMPONENT_EE_INSUFFICIENT_LENGTH
  eng "In function '%s' with algorithm '%s': insufficient output buffer length '%zu'. Required length '%zu'."

ER_SYSTEMD_NOTIFY_DEBUG
  eng "%s%s"

ER_TMP_SESSION_FOR_VAR
  eng "Setting session values for system variables only makes sense in a user session (failed to set '%.256s')."

ER_BUILD_ID
  eng "BuildID[sha1]=%s"

ER_THREAD_POOL_CANNOT_REGISTER_DYNAMIC_PRIVILEGE
  eng "Failed to register dynamic privilege %s."

ER_IB_MSG_LOG_WRITER_WAIT_ON_CONSUMER
  eng "Redo log writer is waiting for %s redo log consumer which is currently reading LSN=%llu preventing reclamation of subsequent portion of the redo log. Consider increasing innodb_redo_log_capacity."

ER_CONDITIONAL_DEBUG
  eng "%s"

ER_IB_MSG_PARSE_OLD_REDO_INDEX_VERSION
  eng "Recovery: Generating index information for INSTANT DDL Table in 8.0.29 format"

OBSOLETE_ER_RES_GRP_FAILED_TO_SWITCH_RESOURCE_GROUP
  eng "Failed to switch resource group. %s".

OBSOLETE_ER_RES_GRP_SWITCH_FAILED_COULD_NOT_ACQUIRE_GLOBAL_LOCK
  eng "Failed to switch resource group. Could not acquire resource groups global lock."

OBSOLETE_ER_RES_GRP_SWITCH_FAILED_COULD_NOT_ACQUIRE_LOCK
  eng "Failed to switch resource group. Could not acquire lock on resource group %s."

OBSOLETE_ER_RES_GRP_SWITCH_FAILED_UNABLE_TO_APPLY_RES_GRP
  eng "Failed to switch resource group. Unable to apply resource group controller %s."

ER_IB_MSG_CLEAR_INSTANT_DROP_COLUMN_METADATA
  eng "Failed to clear instant drop column metadata for table %s"

ER_COMPONENT_KEYRING_OCI_OPEN_KEY_FILE
  eng "Cannot open signing key file %s."

ER_COMPONENT_KEYRING_OCI_CREATE_PRIVATE_KEY
  eng "Out of memory! Cannot create private key."

ER_COMPONENT_KEYRING_OCI_READ_KEY_FILE
  eng "Cannot read signing key file %s."

ER_NOTE_COMPONENT_KEYRING_OCI_MISSING_NAME_OR_TYPE
  eng "Incomplete key: missing Name/Type for the Key: %s."

ER_WARN_COMPONENT_KEYRING_OCI_DUPLICATE_KEY
  eng "Duplicate key found in keyring with Name: %s and Owner: %s."

ER_KEYRING_OCI_PARSE_JSON
  eng "Error parsing JSON response %s."

ER_KEYRING_OCI_INVALID_JSON
  eng "Invalid JSON response!"

ER_KEYRING_OCI_HTTP_REQUEST
  eng "HTTP request failed with error: '%s'"

ER_THREAD_POOL_SYSVAR_CHANGE
  eng "Variable thread_pool.%s was updated: %s"

ER_STACK_BACKTRACE
  eng "%s"

ER_IB_MSG_BUF_POOL_RESIZE_COMPLETE_CUR_CODE
  eng "Status code %u: Completed"

ER_IB_MSG_BUF_POOL_RESIZE_PROGRESS_UPDATE
  eng "Status code %u: %u%% complete"

ER_IB_MSG_BUF_POOL_RESIZE_CODE_STATUS
  eng "Status code %u: %s"

ER_THREAD_POOL_QUERY_THREADS_PER_GROUP_INVALID
  eng "Invalid value set to thread_pool_query_threads_per_group. Valid value range is %u - %u."

ER_THREAD_POOL_QUERY_THRS_PER_GRP_EXCEEDS_TXN_THR_LIMIT
  eng "Query threads count(%u) exceeds transaction threads limit(%u) per group. Please use query threads count per group smaller or equal to max transaction threads limit per group"

ER_IB_MSG_INVALID_PAGE_TYPE
  eng "Found page type mismatch. Expected %u, found %u, in page: space_id=%lu page_no= %lu"

ER_IB_PARALLEL_READER_WORKER_INFO
  eng "preader: space_id=%zu, table=%s, index=%s, scan_ctx=%zu, ctx=%zu, thread_id=%zu, n_threads=%zu"

ER_IB_BULK_LOAD_SUBTREE_INFO
  eng "bulkload: space_id=%zu, table=%s, index=%s, height=%zu, n_extents=%zu, n_pages=%zu"

ER_IB_BULK_FLUSHER_INFO
  eng "bulkflusher: sleep_count=%zu, sleep_duration=%zu milliseconds, total_sleep=%zu milliseconds, pages_flushed=%zu"

ER_IB_BUFFER_POOL_OVERUSE
  eng "%s: Over 67 percent of the buffer pool (curr_size=%zu MB) is occupied by lock heaps or the adaptive hash index or BUF_BLOCK_MEMORY pages. Check that your transactions do not set too many row locks. Maybe you should make the buffer pool bigger?. Starting the InnoDB Monitor to print diagnostics."

ER_IB_BUFFER_POOL_FULL
  eng "%s: Over 95 percent of the buffer pool (curr_size=%zu MB) is occupied by lock heaps or the adaptive hash index or BUF_BLOCK_MEMORY pages. Check that your transactions do not set too many row locks. Maybe you should make the buffer pool bigger?. We intentionally generate a seg fault to print a stack trace on Linux!";

ER_IB_DUPLICATE_KEY
  eng "%s: table=%s, index=%s, n_uniq=%zu, n_fields=%zu, lhs=(%s), rhs=(%s)"

ER_REPLICATION_INCOMPATIBLE_TABLE_WITH_GIPK
  eng "Failed to apply row event with %d columns, originating from a server of version %s on table '%-.192s.%-.192s', which has %d columns, one of which is a generated implicit primary key. Replication is unsupported when the source server is older than 8.0.30, the replica table has a generated implicit primary key, and there is a difference in column count, not counting the replica's generated implicit primary key. Align the table schemas on source and replica, and restart replication."

ER_BULK_EXECUTOR_INFO
  eng "Bulk executor: %s"

ER_BULK_LOADER_INFO
  eng "Bulk loader: %s"

ER_BULK_LOADER_FILE_CONTAINS_LESS_LINES_THAN_IGNORE_CLAUSE_LOG
  eng "The first file being loaded contained less lines than the ignore clause"

ER_BULK_READER_INFO
  eng "Bulk reader: %s"

ER_BULK_READER_LIBCURL_INIT_FAILED_LOG
  eng "Bulk reader failed to initialize libcurl"

ER_BULK_READER_LIBCURL_ERROR_LOG
  eng "Bulk reader got libcurl error: %s"

ER_BULK_READER_SERVER_ERROR_LOG
  eng "Bulk reader got error response from server: %ld"

ER_BULK_READER_COMMUNICATION_ERROR_LOG
  eng "Bulk reader got error in communication with source server, check the error log for additional details"

ER_BULK_PARSER_MISSING_ENCLOSED_BY_LOG
  eng "Missing ENCLOSED BY character at row %ld in file %s. Add OPTIONALLY to the ENCLOSED BY clause to allow this input."

ER_BULK_PARSER_ROW_BUFFER_MAX_TOTAL_COLS_EXCEEDED_LOG
  eng "The number of input columns that need to be buffered for parsing exceeded predefined buffer max size for file '%s'."

ER_BULK_PARSER_COPY_BUFFER_SIZE_EXCEEDED_LOG
  eng "The column data that needed to be copied due to escaped characters exceeded the size of the internal copy buffer for file '%s'."

ER_BULK_PARSER_UNEXPECTED_END_OF_INPUT_LOG
  eng "Unexpected end of input found at row %ld in file '%s'. Data for some columns is missing."

ER_BULK_PARSER_UNEXPECTED_ROW_TERMINATOR_LOG
  eng "Unexpected row terminator found at row %ld in file '%s'. Data for some columns is missing."

ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_ENDING_ENCLOSED_BY_LOG
  eng "Unexpected characters after ending ENCLOSED BY character found at row %ld in file '%s'."

ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_NULL_ESCAPE_LOG
  eng "Unexpected characters after NULL escape (\N) found at row %ld in file '%s'."

ER_BULK_PARSER_UNEXPECTED_CHAR_AFTER_COLUMN_TERMINATOR_LOG
  eng "Unexpected characters after column terminator found at row %ld in file '%s'."

ER_BULK_PARSER_INCOMPLETE_ESCAPE_SEQUENCE_LOG
  eng "Unexpected end of input found at row %ld in file '%s' resulting in incomplete escape sequence."

ER_LOAD_BULK_DATA_WRONG_VALUE_FOR_FIELD_LOG
  eng "Incorrect %-.32s value: '%-.128s' for column '%-.192s' at row %ld in file '%-.192s'"

ER_LOAD_BULK_DATA_WARN_NULL_TO_NOTNULL_LOG
  eng "NULL supplied to NOT NULL column '%s' at row %ld in file '%-.192s'"

ER_IB_BULK_LOAD_THREAD_FAIL
   eng "%s: LOAD BULK DATA thread failure (err=%lu), table=%s, index=%s"

ER_IB_BULK_LOAD_MERGE_FAIL
   eng "%s: Failed to merge sub-trees in LOAD BULK DATA, table=%s, index=%s, details=%s"

ER_IB_LOAD_BULK_CONCURRENCY_REDUCED
   eng "LOAD BULK DATA: Buffer pool size is %zu. Reducing concurrency from %zu to %zu. table=%s."

ER_PLUGIN_EXCEPTION_OPERATION_FAILED
   eng "C++ Exception caught while %s the %s plugin: [%s]"

ER_REQUIRE_TABLE_PRIMARY_KEY_CHECK_GENERATE_WITH_GR_IN_REPO
   eng "Replication configuration appears to be corrupted. On Group Replication channel '%s', setting REQUIRE_TABLE_PRIMARY_KEY_CHECK to 'GENERATE' is not allowed. Run RESET REPLICA ALL to reset it."

ER_CHECK_TABLE_INSTANT_VERSION_BIT_SET
   eng "Record has both instant and version bit set."

ER_GRP_RPL_PAXOS_SINGLE_LEADER_DIFF_FROM_OLD_GRP
  eng "This member is configured with a group_replication_paxos_single_leader option value of 1 and it is trying to join a group with Communication Protocol Version below 8.0.27. In order to join this group, group_replication_paxos_single_leader value must be configured to the value 0. This member will now exit the group."

ER_IB_WRN_IGNORE_REDO_LOG_CAPACITY
  eng "Ignored the innodb-redo-log-capacity option in the Innodb read-only mode. Server is using active redo log files."

ER_IB_PRIMARY_KEY_IS_INSTANT
  eng "Unexpected INSTANTLY Added / Dropped column: '%s' at position %lu for Table '%s', Index '%s' while getting Primary Key for row logging"

ER_THREAD_POOL_IDLE_CONNECTION_CLOSED
   eng "%s"

ER_IB_HIDDEN_NAME_CONFLICT
   eng "Column name %s and internally generated INSTANT DROP column name %s is causing a conflict."

ER_IB_DICT_INVALID_COLUMN_POSITION
   eng "Field number: %llu too large, Total fields in Record: %zu."

ER_IB_DICT_LOG_TABLE_INFO
   eng "%s"

ER_RPL_ASYNC_NEXT_FAILOVER_CHANNEL_SELECTED
  eng "The connection has timed out after %lu retries connecting to '%s@%s:%d'%s, and therefore the MySQL server is going to attempt an asynchronous replication connection failover, to '%s@%s:%d'"

ER_RPL_REPLICA_SOURCE_UUID_HAS_NOT_CHANGED
  eng "The Replica which was connected to source \'%s:%d\', is now connected to new source \'%s:%d\', but still has the same server_uuid %s."

ER_RPL_REPLICA_SOURCE_UUID_HAS_CHANGED_HOST_PORT_UNCHANGED
  eng "The server_uuid for source server \'%s:%d\' has changed from %s to %s. This should not happen unless you have changed it manually."

ER_RPL_REPLICA_SOURCE_UUID_HOST_PORT_HAS_CHANGED
  eng "The source server has changed from \'%s:%d\' with server_uuid %s, to \'%s:%d\' with server_uuid %s."

ER_RPL_REPLICA_CONNECTED_TO_SOURCE_RPL_STARTED_FILE_BASED
  eng "Replica receiver thread%s: connected to source \'%s@%s:%d\' with server_uuid=%s, server_id=%d. Starting replication from file '%s', position '%s'."

ER_RPL_REPLICA_CONNECTED_TO_SOURCE_RPL_STARTED_GTID_BASED
  eng "Replica receiver thread%s: connected to source \'%s@%s:%d\' with server_uuid=%s, server_id=%d. Starting GTID-based replication."

ER_IB_INDEX_LOADER_DONE
   eng "Loader::build_all(): Completed building %zu indexes for old_table=%s, new_table=%s, err=%zu."

ER_IB_INDEX_BUILDER_DONE
   eng "Builder::finish(): Completed building index=%s of table=%s, err=%zu."

ER_WARN_DEPRECATED_USER_DEFINED_COLLATIONS_OPTION
   eng "%s: '%s' is a user defined collation. User defined collations are deprecated and will be removed in a future release. Consider using a compiled collation instead."

ER_IB_INDEX_BUILDER_INIT
   eng "Builder::init(): Initialize for building index=%s of table=%s, n_threads=%zu, sort buffer size=%zu bytes, i/o buffer size=%zu bytes."

ER_IB_SELECT_COUNT_STAR
   eng "SELECT COUNT(*) FROM table=%s, n_threads=%zu, n_partitions=%zu."

ER_IB_INDEX_LOG_VERSION_MISMATCH
   eng "Index log version %u did not match. Max index version is %u. Recovery can't continue. Please make sure server is not starting up with the datadir generated in future version."

ER_WARN_COMPONENTS_INFRASTRUCTURE_MANIFEST_MULTIPLE_KEYRING
   eng "Multiple keyring component URNs '%s' found in manifest file '%s'. Only one keyring component can be loaded at a time."

ER_GRP_RPL_HAS_STARTED
  eng "Plugin 'group_replication' has been started."

ER_CHECK_TABLE_MIN_REC_FLAG_SET
   eng "Minimum record flag is wrongly set to rec on page '%lu' at level '%lu' for index '%s' of table '%s'."

ER_CHECK_TABLE_MIN_REC_FLAG_NOT_SET
   eng "Minimum record flag is not set to first rec on page '%lu' at level '%lu' for index '%s' of table '%s'."

ER_NOTE_COMPONENT_SLOT_REGISTRATION_SUCCESS
  eng "Successfully registered slot '%d' for component '%s'."

ER_NOTE_COMPONENT_SLOT_DEREGISTRATION_SUCCESS
  eng "Successfully de-registered slot '%d' from component '%s'."

ER_WARN_CANNOT_FREE_COMPONENT_DATA_DEALLOCATION_FAILED
  eng "Cannot free resources stored by component '%s' in thread with ID: '%d'. Component failed to free required resources."

ER_IB_RESURRECT_TRX_INSERT
  eng "Transaction ID: %lu found for resurrecting inserts"

ER_IB_RESURRECT_TRX_UPDATE
  eng "Transaction ID: %lu found for resurrecting updates"

ER_IB_RESURRECT_IDENTIFY_TABLE_TO_LOCK
  eng "Identified table ID: %lu to acquire lock"

ER_IB_RESURRECT_ACQUIRE_TABLE_LOCK
  eng "Acquired lock on table ID: %lu, name: %s"

ER_IB_RESURRECT_RECORD_PROGRESS
  eng "Records read: %lu - Pages read: %lu"

ER_IB_RESURRECT_RECORD_COMPLETE
  eng "Total records resurrected: %lu - Total pages read: %lu - Total tables acquired: %lu"

ER_IB_RESURRECT_TRX_INSERT_COMPLETE
  eng "Resurrected %lu transactions doing inserts."

ER_IB_RESURRECT_TRX_UPDATE_COMPLETE
  eng "Resurrected %lu transactions doing updates."

ER_AUTHENTICATION_OCI_INVALID_TOKEN
  eng "Invalid security token provided by '%s' for '%s'@'%s': %s."

ER_AUTHENTICATION_OCI_TOKEN_DETAILS_MISMATCH
  eng "Unknown user/tenancy (user: %s tenancy: %s) found in security token provided by '%s' for '%s'@'%s'."

ER_AUTHENTICATION_OCI_TOKEN_NOT_VERIFIED
  eng "Verification failed for security token provided by '%s' for '%s'@'%s' : %s."

ER_AUTHENTICATION_OCI_DOWNLOAD_IDDP_PUBLIC_KEY
  eng "Could not download IDDP Public key. Please check if security token provided by '%s' for '%s'@'%s' is valid."

OBSOLETE_ER_AUTHENTICATION_OCI_NO_MATCHING_GROUPS_IN_TOKEN
  eng "None of the groups extracted from security token matches any of the entries from authentication string."

ER_SYS_VAR_REGISTRATION
  eng "Cannot register variable '%s'. Please check if it is not already registered by another component."

ER_SYS_VAR_DEREGISTRATION
  eng "Cannot unregister variable '%s'. Please check if it was registered properly in the first place."

ER_UDF_REGISTRATION
  eng "Cannot register function '%s'. Please check if it is not already registered by another component."

ER_UDF_DEREGISTRATION
  eng "Cannot unregister function '%s'. Please check if it was registered properly in the first place."

ER_PRIVILEGE_REGISTRATION
  eng "Cannot register privilege '%s'. Please check if it is not already registered."

ER_PRIVILEGE_DEREGISTRATION
  eng "Cannot unregister privilege '%s'. Please check if it was registered properly in the first place."

ER_UDF_EXEC_FAILURE
  eng "Error while executing UDF %s."

ER_UDF_EXEC_FAILURE_REASON
  eng "Error while executing UDF %s: %s."

ER_COMPONENT_SERVICE_CALL
  eng "%s failed."

ER_COMPONENT_SERVICE_CALL_RESULT
  eng "%s failed with %u."

ER_COMPONENT_LOCK
  eng "Lock failed at %s : %u."

ER_COMPONENT_UNLOCK
  eng "Lock from %s : %u failed to unlock."

ER_COMPONENT_MASKING_OTHER_ERROR
  eng "Masking component error: '%s'."

ER_COMPONENT_MASKING_ABI
  eng "%s failed."

ER_COMPONENT_MASKING_ABI_REASON
  eng "%s failed because %s."

ER_COMPONENT_MASKING_RANDOM_CREATE
  eng "Creation of random context failed."

ER_COMPONENT_MASKING_RANDOM_CREATE_REASON
  eng "Creation of random context failed because %s."

ER_COMPONENT_MASKING_CANNOT_ACCESS_TABLE
  eng "Cannot access %s.%s table."

ER_REDUCED_DBLWR_FILE_CORRUPTED
  eng "Cannot recover from detect_only doublewrite buffer as page %u from batch doublewrite file is corrupted."

ER_REDUCED_DBLWR_PAGE_FOUND
  eng "Database page corruption of tablespace %s space_id: %u page_num: %u. Cannot recover it from the doublewrite buffer because it was written in detect_only-doublewrite mode."

ER_CONN_INIT_CONNECT_IGNORED_MFA
  eng "init_connect variable is ignored for user: %s host: %s due to multi-factor registration required sandboxing"

ER_SECONDARY_ENGINE_DDL_FAILED
  eng "Execution of alter table %s %s failed because %s."

ER_THREAD_POOL_CONNECTION_REPORT
   eng "TP conn (port: managed(delta), active, opened, closed, added, dropped): %s"

ER_WARN_SCHEDULED_TASK_RUN_FAILED
   eng "Scheduled run of task %s failed"

ER_AUDIT_LOG_INVALID_FLUSH_INTERVAL_VALUE
   eng "Invalid flush interval specified: %lu. Valid values are 0 (off) or greater than or equal to %d. Adjusting to %d"

ER_LOG_CANNOT_PURGE_BINLOG_WITH_BACKUP_LOCK
  eng "Could not purge binary logs since another session is executing LOCK INSTANCE FOR BACKUP. Wait for that session to release the lock."

ER_CONVERT_MULTI_VALUE
   eng "Multi-values cannot be converted to MySQL format: %s"

ER_IB_DDL_CONVERT_HEAP_NOT_FOUND
  eng "Heap required to convert columns is not present."

ER_SERVER_DOWNGRADE_FROM_VERSION
  eng "MySQL server downgrading from version '%u' to '%u'."

ER_BEYOND_SERVER_DOWNGRADE_THRESHOLD
  eng "Invalid MySQL server downgrade: Cannot downgrade from %u to %u. Target MySQL server version is lower than the server downgrade threshold %u."

ER_BEYOND_SERVER_UPGRADE_THRESHOLD
  eng "Invalid MySQL server upgrade: Cannot upgrade from %u to %u. Target MySQL server version is lower than the server upgrade threshold %u."

ER_INVALID_SERVER_UPGRADE_NOT_LTS
  eng "Invalid MySQL server upgrade: Cannot upgrade from %u to %u. Upgrade to next major version is only allowed from the last LTS release, which version %u is not."

ER_INVALID_SERVER_DOWNGRADE_NOT_PATCH
  eng "Invalid MySQL server downgrade: Cannot downgrade from %u to %u. Downgrade is only permitted between patch releases."

ER_FAILED_GET_DD_PROPERTY
  eng "Failed to get the data dictionary property %s."

ER_FAILED_SET_DD_PROPERTY
  eng "Failed to set the data dictionary property %s."

ER_SERVER_DOWNGRADE_STATUS
  eng "Server downgrade from '%d' to '%d' %s."

ER_INFORMATION_SCHEMA_VERSION_CHANGE
  eng "%s information schema from version %u to %u."

ER_PERFORMANCE_SCHEMA_VERSION_CHANGE
  eng "%s performance schema from version %u to %u."

ER_WARN_DEPRECATED_OR_BLOCKED_CIPHER
   eng "Value for option '%s' contains cipher '%s' that is either blocked or deprecated (and will be removed in a future release). Please refer to the documentation for more details."

# DO NOT add server-to-client messages here;
# they go in messages_to_clients.txt
# in the same directory as this file.
#
# This file is for messages intended for the error log only.
#
# See the FAQ in errmsg_readme.txt in the
# same directory as this file for more
# information.

#
# End of 8.0 error messages intended to be written to the server error log.
#

################################################################################
# Error numbers 50000 to 51999 are reserved. Please do not use them for
# other error messages.
################################################################################
reserved-error-section 50000 51999


# DO NOT append error messages here at the end.
#
#   If you came here to add a message, please read the FAQ in
#   errmsg_readme.txt to see where it should go.
#
#   Messages the server sends to the error log and messages
#   the server sends to its clients go to different places.
#   Messages for different server versions go to different places.
#   Messages that are built into the client library (libmysql/C-API)
#   go into yet a different place.
