#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型
"""

import pymysql
import json
from datetime import datetime
from typing import List, Dict, Optional, Tuple
import os

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, host='localhost', port=3306, user='root', password='', database='kling_ai'):
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = pymysql.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database,
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True
            )
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询"""
        if not self.connection:
            if not self.connect():
                return []
        
        try:
            with self.connection.cursor() as cursor:
                cursor.execute(sql, params)
                return cursor.fetchall()
        except Exception as e:
            print(f"查询执行失败: {e}")
            return []
    
    def execute_update(self, sql: str, params: tuple = None) -> int:
        """执行更新/插入/删除"""
        if not self.connection:
            if not self.connect():
                return 0
        
        try:
            with self.connection.cursor() as cursor:
                affected_rows = cursor.execute(sql, params)
                return cursor.lastrowid if 'INSERT' in sql.upper() else affected_rows
        except Exception as e:
            print(f"更新执行失败: {e}")
            return 0

class GenerationRecord:
    """生成记录模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_record(self, original_image_path: str, original_image_name: str, 
                     prompt: str, ai_provider: str, style_name: str) -> int:
        """创建生成记录"""
        sql = """
        INSERT INTO generation_records 
        (original_image_path, original_image_name, prompt, ai_provider, style_name, status)
        VALUES (%s, %s, %s, %s, %s, 'pending')
        """
        return self.db.execute_update(sql, (original_image_path, original_image_name, 
                                          prompt, ai_provider, style_name))
    
    def update_status(self, record_id: int, status: str) -> bool:
        """更新生成状态"""
        sql = "UPDATE generation_records SET status = %s WHERE id = %s"
        return self.db.execute_update(sql, (status, record_id)) > 0
    
    def get_record(self, record_id: int) -> Optional[Dict]:
        """获取生成记录"""
        sql = "SELECT * FROM generation_records WHERE id = %s"
        results = self.db.execute_query(sql, (record_id,))
        return results[0] if results else None
    
    def get_recent_records(self, limit: int = 50) -> List[Dict]:
        """获取最近的生成记录"""
        sql = """
        SELECT * FROM generation_records 
        ORDER BY created_at DESC 
        LIMIT %s
        """
        return self.db.execute_query(sql, (limit,))

class GeneratedImage:
    """生成图片模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_image(self, record_id: int, image_path: str, image_name: str, 
                  image_url: str = None, file_size: int = None, 
                  width: int = None, height: int = None) -> int:
        """添加生成的图片"""
        sql = """
        INSERT INTO generated_images 
        (record_id, image_path, image_name, image_url, file_size, width, height)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        return self.db.execute_update(sql, (record_id, image_path, image_name, 
                                          image_url, file_size, width, height))
    
    def update_likes(self, image_id: int, likes: int, dislikes: int) -> bool:
        """更新点赞点踩数"""
        sql = "UPDATE generated_images SET likes = %s, dislikes = %s WHERE id = %s"
        return self.db.execute_update(sql, (likes, dislikes, image_id)) > 0
    
    def approve_image(self, image_id: int, is_approved: bool, approved_by: str) -> bool:
        """审核图片"""
        sql = """
        UPDATE generated_images 
        SET is_approved = %s, approved_by = %s, approved_at = NOW() 
        WHERE id = %s
        """
        return self.db.execute_update(sql, (is_approved, approved_by, image_id)) > 0
    
    def get_images_by_record(self, record_id: int) -> List[Dict]:
        """获取记录的所有图片"""
        sql = "SELECT * FROM generated_images WHERE record_id = %s ORDER BY created_at"
        return self.db.execute_query(sql, (record_id,))
    
    def get_pending_approval(self) -> List[Dict]:
        """获取待审核的图片"""
        sql = """
        SELECT gi.*, gr.original_image_name, gr.prompt, gr.style_name
        FROM generated_images gi
        JOIN generation_records gr ON gi.record_id = gr.id
        WHERE gi.is_approved IS NULL
        ORDER BY gi.created_at DESC
        """
        return self.db.execute_query(sql)
    
    def get_approved_images(self, limit: int = 100) -> List[Dict]:
        """获取已审核通过的图片"""
        sql = """
        SELECT gi.*, gr.original_image_name, gr.prompt, gr.style_name
        FROM generated_images gi
        JOIN generation_records gr ON gi.record_id = gr.id
        WHERE gi.is_approved = 1
        ORDER BY gi.created_at DESC
        LIMIT %s
        """
        return self.db.execute_query(sql, (limit,))

class UserAction:
    """用户操作模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def record_action(self, image_id: int, action_type: str, user_ip: str, user_agent: str = None) -> bool:
        """记录用户操作"""
        sql = """
        INSERT INTO user_actions (image_id, action_type, user_ip, user_agent)
        VALUES (%s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE created_at = NOW()
        """
        return self.db.execute_update(sql, (image_id, action_type, user_ip, user_agent)) > 0
    
    def get_user_action(self, image_id: int, user_ip: str, action_type: str) -> Optional[Dict]:
        """获取用户操作记录"""
        sql = """
        SELECT * FROM user_actions 
        WHERE image_id = %s AND user_ip = %s AND action_type = %s
        """
        results = self.db.execute_query(sql, (image_id, user_ip, action_type))
        return results[0] if results else None

class SystemConfig:
    """系统配置模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_config(self, key: str, default_value: str = None) -> str:
        """获取配置值"""
        sql = "SELECT config_value FROM system_config WHERE config_key = %s"
        results = self.db.execute_query(sql, (key,))
        return results[0]['config_value'] if results else default_value
    
    def set_config(self, key: str, value: str, description: str = None) -> bool:
        """设置配置值"""
        sql = """
        INSERT INTO system_config (config_key, config_value, description)
        VALUES (%s, %s, %s)
        ON DUPLICATE KEY UPDATE config_value = %s, description = %s
        """
        return self.db.execute_update(sql, (key, value, description, value, description)) > 0

# 数据库管理器实例
db_manager = DatabaseManager()

# 模型实例
generation_record = GenerationRecord(db_manager)
generated_image = GeneratedImage(db_manager)
user_action = UserAction(db_manager)
system_config = SystemConfig(db_manager)
