#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证注入逻辑 - 检查首帧和尾帧是否注入到了正确的位置
"""

import asyncio
import os
import sys
import base64
import mimetypes
from pathlib import Path
from playwright.async_api import async_playwright


async def verify_injection_logic():
    """验证注入逻辑"""
    
    print("🔍 验证注入逻辑")
    print("=" * 50)
    
    # 检查Chrome调试端口
    debug_port = 9222
    print("🔍 检查Chrome调试端口...")
    
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("❌ Chrome调试端口未开启")
        return False
    
    print("✅ Chrome调试端口已开启")
    
    async with async_playwright() as p:
        try:
            print("🔗 连接到Chrome浏览器...")
            
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            contexts = browser.contexts
            
            if not contexts:
                print("❌ 未找到浏览器上下文")
                return False
            
            # 查找即梦AI页面
            jimeng_page = None
            for context in contexts:
                for page in context.pages:
                    if 'jimeng.jianying.com' in page.url:
                        jimeng_page = page
                        print(f"✅ 找到即梦AI页面: {page.url}")
                        break
                if jimeng_page:
                    break
            
            if not jimeng_page:
                print("❌ 未找到即梦AI页面")
                return False
            
            # 执行验证
            await perform_verification(jimeng_page)
            
            return True
                
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False


async def perform_verification(page):
    """执行验证"""
    try:
        print("🔍 开始验证当前页面状态...")
        
        # 等待页面稳定
        await asyncio.sleep(2)
        
        # 详细分析页面状态
        page_analysis = await page.evaluate("""
            () => {
                console.log('🔍 详细分析页面状态');
                
                const analysis = {
                    fileInputs: [],
                    images: [],
                    uploadMechanism: 'unknown'
                };
                
                // 分析所有文件输入
                const inputs = document.querySelectorAll('input[type="file"]');
                inputs.forEach((input, index) => {
                    const rect = input.getBoundingClientRect();
                    const style = getComputedStyle(input);
                    
                    analysis.fileInputs.push({
                        index: index,
                        visible: input.offsetParent !== null,
                        display: style.display,
                        visibility: style.visibility,
                        opacity: style.opacity,
                        accept: input.accept,
                        multiple: input.multiple,
                        className: input.className,
                        id: input.id,
                        hasFiles: input.files.length > 0,
                        filesCount: input.files.length,
                        parentClass: input.parentElement ? input.parentElement.className : null,
                        rect: {
                            width: rect.width,
                            height: rect.height,
                            x: rect.x,
                            y: rect.y
                        }
                    });
                });
                
                // 分析预览图片
                const blobImages = document.querySelectorAll('img[src*="blob:"]');
                blobImages.forEach((img, index) => {
                    analysis.images.push({
                        index: index,
                        src: img.src.substring(0, 50) + '...',
                        width: img.width,
                        height: img.height,
                        className: img.className,
                        alt: img.alt,
                        parentClass: img.parentElement ? img.parentElement.className : null
                    });
                });
                
                // 判断上传机制
                if (analysis.fileInputs.length === 0) {
                    analysis.uploadMechanism = 'no_inputs';
                } else if (analysis.fileInputs.length === 1) {
                    analysis.uploadMechanism = 'single_input_reuse';
                } else if (analysis.fileInputs.length >= 2) {
                    analysis.uploadMechanism = 'multiple_inputs';
                }
                
                console.log('页面分析结果:', analysis);
                return analysis;
            }
        """)
        
        print(f"\n📊 页面分析结果:")
        print(f"   文件输入数量: {len(page_analysis['fileInputs'])} 个")
        print(f"   预览图片数量: {len(page_analysis['images'])} 个")
        print(f"   上传机制: {page_analysis['uploadMechanism']}")
        
        # 详细显示文件输入信息
        print(f"\n📁 文件输入详细信息:")
        for i, input_info in enumerate(page_analysis['fileInputs']):
            print(f"   输入 {i}:")
            print(f"     可见: {input_info['visible']}")
            print(f"     display: {input_info['display']}")
            print(f"     visibility: {input_info['visibility']}")
            print(f"     opacity: {input_info['opacity']}")
            print(f"     accept: {input_info['accept']}")
            print(f"     multiple: {input_info['multiple']}")
            print(f"     已有文件: {input_info['hasFiles']} (数量: {input_info['filesCount']})")
            print(f"     className: {input_info['className']}")
            print(f"     父元素类: {input_info['parentClass']}")
        
        # 详细显示预览图片信息
        print(f"\n🖼️ 预览图片详细信息:")
        for i, img_info in enumerate(page_analysis['images']):
            print(f"   图片 {i}:")
            print(f"     尺寸: {img_info['width']}x{img_info['height']}")
            print(f"     className: {img_info['className']}")
            print(f"     alt: {img_info['alt']}")
            print(f"     父元素类: {img_info['parentClass']}")
        
        # 分析上传机制
        print(f"\n🔧 上传机制分析:")
        
        if page_analysis['uploadMechanism'] == 'no_inputs':
            print("   ❌ 没有文件输入元素")
            print("   💡 页面可能使用其他上传方式")
            
        elif page_analysis['uploadMechanism'] == 'single_input_reuse':
            print("   ⚠️ 只有一个文件输入元素")
            print("   💡 可能的情况:")
            print("      1. 单个输入重复使用（每次上传后重置）")
            print("      2. 需要特定操作激活第二个输入")
            print("      3. 页面不支持首尾帧功能")
            
            # 检查是否有文件已上传
            if any(inp['hasFiles'] for inp in page_analysis['fileInputs']):
                print("   ✅ 检测到已上传的文件")
            else:
                print("   ❌ 没有检测到已上传的文件")
                
        elif page_analysis['uploadMechanism'] == 'multiple_inputs':
            print("   ✅ 检测到多个文件输入元素")
            print("   💡 支持首尾帧分别上传")
            
            # 检查每个输入的状态
            for i, inp in enumerate(page_analysis['fileInputs']):
                if inp['hasFiles']:
                    print(f"      输入 {i}: 已有 {inp['filesCount']} 个文件")
                else:
                    print(f"      输入 {i}: 无文件")
        
        # 验证当前注入逻辑的问题
        print(f"\n🚨 当前注入逻辑问题分析:")
        
        if len(page_analysis['fileInputs']) == 1:
            print("   ⚠️ 发现问题：只有一个文件输入")
            print("   🔍 当前脚本逻辑:")
            print("      - 首帧注入：使用 inputs[0] ✅")
            print("      - 尾帧注入：使用 inputs[1] ❌ (不存在)")
            print("   💡 可能的解决方案:")
            print("      1. 修改逻辑：两次都使用 inputs[0]，但注入不同图片")
            print("      2. 先激活尾帧区域，再注入")
            print("      3. 使用拖拽或其他方式上传")
            
        elif len(page_analysis['fileInputs']) >= 2:
            print("   ✅ 文件输入数量正常")
            print("   🔍 当前脚本逻辑应该正确")
            
        else:
            print("   ❌ 没有文件输入元素")
            print("   💡 需要重新分析页面结构")
        
        # 提供修复建议
        print(f"\n💡 修复建议:")
        
        if page_analysis['uploadMechanism'] == 'single_input_reuse':
            print("   🔧 建议修改注入策略:")
            print("      1. 第一次运行：注入首帧到 inputs[0]")
            print("      2. 等待页面处理")
            print("      3. 第二次运行：注入尾帧到 inputs[0]（覆盖模式）")
            print("      4. 或者：实现智能检测，根据当前状态决定注入内容")
            
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


async def main():
    """主函数"""
    
    success = await verify_injection_logic()
    
    if success:
        print("\n✅ 验证完成")
    else:
        print("\n❌ 验证失败")


if __name__ == "__main__":
    print("=" * 60)
    print("🔍 注入逻辑验证工具")
    print("=" * 60)
    print("🎯 目标：分析当前页面的上传机制和注入逻辑问题")
    print("=" * 60)
    print()
    
    asyncio.run(main())
