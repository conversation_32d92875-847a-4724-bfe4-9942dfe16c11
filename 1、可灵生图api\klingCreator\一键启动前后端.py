#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI图生图项目 - 一键启动前后端脚本
支持自动检测依赖、启动服务、打开浏览器
"""

import os
import sys
import time
import subprocess
import webbrowser
import threading
from pathlib import Path

class KlingProjectLauncher:
    """可灵AI项目启动器"""
    
    def __init__(self):
        self.project_dir = Path(__file__).parent
        self.app_file = self.project_dir / "app.py"
        self.requirements_file = self.project_dir / "requirements.txt"
        self.server_process = None
        self.server_url = "http://localhost:5000"
        
    def print_banner(self):
        """打印启动横幅"""
        print("=" * 80)
        print("🚀 可灵AI图生图项目 - 一键启动器")
        print("=" * 80)
        print("📁 项目目录:", self.project_dir)
        print("🌐 服务地址:", self.server_url)
        print("📝 主页面:", f"{self.server_url}/")
        print("🖼️ 画廊页面:", f"{self.server_url}/gallery")
        print("=" * 80)
        print()
    
    def check_python_version(self):
        """检查Python版本"""
        print("🔍 检查Python版本...")
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 7):
            print("❌ Python版本过低，需要Python 3.7+")
            print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
            return False
        else:
            print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
            return True
    
    def check_files(self):
        """检查必要文件"""
        print("🔍 检查项目文件...")
        
        if not self.app_file.exists():
            print(f"❌ 找不到主应用文件: {self.app_file}")
            return False
        else:
            print(f"✅ 主应用文件: {self.app_file}")
        
        if not self.requirements_file.exists():
            print(f"⚠️ 找不到依赖文件: {self.requirements_file}")
            print("   将跳过依赖检查")
        else:
            print(f"✅ 依赖文件: {self.requirements_file}")
        
        return True

    def check_port(self, port=5000):
        """检查端口是否被占用"""
        import socket

        print(f"🔍 检查端口 {port} 是否可用...")

        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.settimeout(1)
                result = sock.connect_ex(('localhost', port))
                if result == 0:
                    print(f"⚠️ 端口 {port} 已被占用")

                    # 尝试获取占用进程信息
                    try:
                        import psutil
                        for proc in psutil.process_iter(['pid', 'name', 'connections']):
                            try:
                                for conn in proc.info['connections']:
                                    if conn.laddr.port == port:
                                        print(f"   占用进程: {proc.info['name']} (PID: {proc.info['pid']})")
                                        return False
                            except:
                                continue
                    except ImportError:
                        pass

                    print("   请关闭占用端口的程序或使用其他端口")
                    return False
                else:
                    print(f"✅ 端口 {port} 可用")
                    return True
        except Exception as e:
            print(f"⚠️ 端口检查失败: {e}")
            return True  # 检查失败时假设端口可用

    def install_dependencies(self):
        """安装依赖"""
        if not self.requirements_file.exists():
            print("⏭️ 跳过依赖安装（无requirements.txt）")
            return True
        
        print("📦 检查并安装依赖...")
        try:
            # 检查是否需要安装依赖
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", str(self.requirements_file)
            ], capture_output=True, text=True, cwd=self.project_dir)
            
            if result.returncode == 0:
                print("✅ 依赖安装完成")
                return True
            else:
                print("❌ 依赖安装失败:")
                print(result.stderr)
                return False
        except Exception as e:
            print(f"❌ 依赖安装异常: {e}")
            return False
    
    def start_server(self):
        """启动后端服务器"""
        print("🚀 启动后端服务器...")
        try:
            # 启动Flask应用 - 不重定向输出，这样可以看到错误信息
            self.server_process = subprocess.Popen([
                sys.executable, str(self.app_file)
            ], cwd=self.project_dir)

            print("✅ 后端服务器启动中...")
            print("📊 正在检查服务器状态...")
            return True
        except Exception as e:
            print(f"❌ 启动后端服务器失败: {e}")
            return False
    
    def wait_for_server(self, timeout=60):
        """等待服务器启动"""
        print("⏳ 等待服务器启动...")
        print("💡 提示：如果长时间无响应，请检查控制台错误信息")

        import requests
        start_time = time.time()
        dot_count = 0

        while time.time() - start_time < timeout:
            # 检查进程是否还在运行
            if self.server_process and self.server_process.poll() is not None:
                print(f"\n❌ 服务器进程已退出，退出码: {self.server_process.returncode}")
                return False

            try:
                response = requests.get(self.server_url, timeout=3)
                if response.status_code == 200:
                    print("\n✅ 服务器启动成功!")
                    return True
            except requests.exceptions.RequestException:
                pass

            time.sleep(2)
            dot_count += 1
            print(".", end="", flush=True)

            # 每10个点换行并显示等待时间
            if dot_count % 10 == 0:
                elapsed = int(time.time() - start_time)
                print(f" ({elapsed}s)")

        print(f"\n❌ 服务器启动超时（{timeout}秒）")
        print("💡 可能的原因：")
        print("   1. 端口5000被占用")
        print("   2. 依赖包缺失或版本不兼容")
        print("   3. 数据库文件权限问题")
        print("   4. Python环境问题")
        return False
    
    def open_browser(self):
        """打开浏览器"""
        print("🌐 打开浏览器...")
        try:
            # 打开主页
            webbrowser.open(self.server_url)
            print(f"✅ 浏览器已打开: {self.server_url}")
            
            # 3秒后打开画廊页面
            def open_gallery():
                time.sleep(3)
                webbrowser.open(f"{self.server_url}/gallery")
                print(f"✅ 画廊页面已打开: {self.server_url}/gallery")
            
            threading.Thread(target=open_gallery, daemon=True).start()
            return True
        except Exception as e:
            print(f"❌ 打开浏览器失败: {e}")
            print(f"   请手动访问: {self.server_url}")
            return False
    
    def monitor_server(self):
        """监控服务器状态"""
        print("\n" + "=" * 80)
        print("🎯 项目启动完成!")
        print("=" * 80)
        print("📋 可用页面:")
        print(f"   🏠 主页: {self.server_url}/")
        print(f"   🖼️ 画廊: {self.server_url}/gallery")
        print("=" * 80)
        print("💡 使用说明:")
        print("   1. 在主页上传图片并生成AI图片")
        print("   2. 在画廊查看所有生成的图片（按最新生成时间排序）")
        print("   3. 按 Ctrl+C 停止服务器")
        print("=" * 80)
        print()
        
        try:
            # 监控服务器进程
            while True:
                if self.server_process and self.server_process.poll() is not None:
                    print("❌ 服务器进程已退出")
                    break
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 收到停止信号...")
            self.stop_server()
    
    def stop_server(self):
        """停止服务器"""
        print("🛑 停止服务器...")
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=5)
                print("✅ 服务器已停止")
            except subprocess.TimeoutExpired:
                print("⚠️ 强制终止服务器...")
                self.server_process.kill()
                print("✅ 服务器已强制停止")
            except Exception as e:
                print(f"❌ 停止服务器失败: {e}")
    
    def run(self):
        """运行启动流程"""
        try:
            # 打印横幅
            self.print_banner()
            
            # 检查环境
            if not self.check_python_version():
                return False
            
            if not self.check_files():
                return False

            # 检查端口
            if not self.check_port():
                return False

            # 安装依赖
            if not self.install_dependencies():
                print("⚠️ 依赖安装失败，但继续尝试启动...")

            # 启动服务器
            if not self.start_server():
                return False
            
            # 等待服务器启动
            if not self.wait_for_server():
                self.stop_server()
                return False
            
            # 打开浏览器
            self.open_browser()
            
            # 监控服务器
            self.monitor_server()
            
            return True
            
        except KeyboardInterrupt:
            print("\n🛑 用户中断启动...")
            self.stop_server()
            return False
        except Exception as e:
            print(f"\n❌ 启动失败: {e}")
            self.stop_server()
            return False


def main():
    """主函数"""
    launcher = KlingProjectLauncher()
    
    try:
        success = launcher.run()
        if success:
            print("\n✅ 项目已成功启动并运行!")
        else:
            print("\n❌ 项目启动失败!")
            input("按回车键退出...")
    except Exception as e:
        print(f"\n💥 启动器异常: {e}")
        input("按回车键退出...")
    finally:
        launcher.stop_server()


if __name__ == "__main__":
    main()
