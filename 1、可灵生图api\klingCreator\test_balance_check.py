#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试积分检查功能
"""

import requests
import json

def test_balance_check():
    """测试积分检查功能"""
    base_url = "http://localhost:5000"
    
    print("🔍 测试积分检查功能")
    print("=" * 50)
    
    # 1. 测试获取积分余额
    print("1. 测试获取积分余额...")
    try:
        response = requests.get(f"{base_url}/api/balance")
        if response.status_code == 200:
            data = response.json()
            balance = data.get('balance', 0)
            print(f"✅ 当前积分余额: {balance}")
        else:
            print(f"❌ 获取积分失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return
    except Exception as e:
        print(f"❌ 获取积分异常: {e}")
        return
    
    # 2. 测试积分不足的情况（模拟大量生成任务）
    print("\n2. 测试积分不足检查...")
    test_data = {
        "filename": "test.jpg",
        "original_name": "test.jpg", 
        "upload_id": 1,
        "prompts": [
            {"prompt": f"测试提示词 {i}", "style_name": "默认"}
            for i in range(int(balance) + 5)  # 超过当前积分的任务数
        ]
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate",
            headers={'Content-Type': 'application/json'},
            json=test_data
        )
        
        if response.status_code == 400:
            data = response.json()
            if data.get('insufficient_balance'):
                print("✅ 积分不足检查正常工作")
                print(f"   当前余额: {data.get('current_balance')}")
                print(f"   需要积分: {data.get('required_points')}")
                print(f"   错误信息: {data.get('error')}")
            else:
                print(f"❌ 返回400但不是积分不足错误: {data}")
        else:
            print(f"❌ 预期返回400，实际返回: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 测试积分不足异常: {e}")
    
    # 3. 测试正常的积分检查（少量任务）
    print("\n3. 测试正常积分检查...")
    normal_test_data = {
        "filename": "test.jpg",
        "original_name": "test.jpg",
        "upload_id": 1, 
        "prompts": [
            {"prompt": "测试提示词", "style_name": "默认"}
        ]
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/generate",
            headers={'Content-Type': 'application/json'},
            json=normal_test_data
        )
        
        if response.status_code == 400:
            data = response.json()
            if 'Cookie无效' in data.get('error', ''):
                print("⚠️ Cookie无效，这是正常的（需要有效的可灵AI Cookie）")
            elif 'file not found' in data.get('error', '').lower():
                print("⚠️ 文件不存在，这是正常的（测试文件不存在）")
            else:
                print(f"❌ 其他错误: {data}")
        else:
            print(f"✅ 积分检查通过，返回状态: {response.status_code}")
    except Exception as e:
        print(f"❌ 测试正常积分检查异常: {e}")
    
    print("\n" + "=" * 50)
    print("🎯 积分检查功能测试完成")

if __name__ == "__main__":
    test_balance_check()
