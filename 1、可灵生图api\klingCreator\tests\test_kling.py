# 导入系统模块，用于路径操作
import sys
# 导入操作系统模块，用于文件路径处理
import os

# 将父目录添加到Python路径中，以便导入kling模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 从kling模块导入需要测试的类和函数
from kling import ImageGen, VideoGen, BaseGen, call_for_daily_check, TaskStatus

# 导入pytest测试框架
import pytest
# 导入unittest.mock模块，用于创建模拟对象和补丁
from unittest.mock import patch, MagicMock, mock_open


# pytest fixture装饰器，创建模拟的requests.Session对象
@pytest.fixture
def mock_session():
    # 使用patch模拟requests.Session类
    with patch("requests.Session") as mock:
        # 返回模拟的Session实例
        yield mock.return_value


# pytest fixture装饰器，创建BaseGen实例用于测试
@pytest.fixture
def base_gen(mock_session):
    # 返回使用模拟cookie的BaseGen实例
    return BaseGen("mock_cookie")


# pytest fixture装饰器，创建ImageGen实例用于测试
@pytest.fixture
def image_gen(mock_session):
    # 返回使用模拟cookie的ImageGen实例
    return ImageGen("mock_cookie")


# pytest fixture装饰器，创建VideoGen实例用于测试
@pytest.fixture
def video_gen(mock_session):
    # 返回使用模拟cookie的VideoGen实例
    return VideoGen("mock_cookie")


# 测试BaseGen类的初始化功能
def test_base_gen_init(base_gen):
    # 断言对象是BaseGen的实例
    assert isinstance(base_gen, BaseGen)
    # 断言cookie属性设置正确
    assert base_gen.cookie == "mock_cookie"


# 测试ImageGen类的初始化功能
def test_image_gen_init(image_gen):
    # 断言对象是ImageGen的实例
    assert isinstance(image_gen, ImageGen)
    # 断言cookie属性设置正确
    assert image_gen.cookie == "mock_cookie"


# 测试VideoGen类的初始化功能
def test_video_gen_init(video_gen):
    # 断言对象是VideoGen的实例
    assert isinstance(video_gen, VideoGen)
    # 断言cookie属性设置正确
    assert video_gen.cookie == "mock_cookie"


# 测试cookie字符串解析功能
def test_parse_cookie_string():
    # 创建测试用的cookie字符串，包含快手相关的cookie
    cookie_string = "key1=value1; key2=value2; kuaishou_key=value3"
    # 调用解析方法
    cookiejar, is_cn = BaseGen.parse_cookie_string(cookie_string)
    # 断言正确识别为中国版（因为包含kuaishou前缀的cookie）
    assert is_cn == True
    # 断言cookie解析结果正确
    assert dict(cookiejar) == {
        "key1": "value1",
        "key2": "value2",
        "kuaishou_key": "value3",
    }


# 使用参数化测试，测试ImageGen和VideoGen的获取账户积分功能
@pytest.mark.parametrize("gen_class", [ImageGen, VideoGen])
def test_get_account_point(gen_class, mock_session):
    # 创建生成器实例
    gen = gen_class("mock_cookie")
    # 模拟两次GET请求的响应：第一次获取奖励信息，第二次获取积分信息
    mock_session.get.return_value.json.side_effect = [
        {"status": 200},  # 奖励信息响应
        {"status": 200, "data": {"total": 1000}},  # 积分信息响应，总积分1000
    ]
    # 断言积分计算正确（1000/100=10.0）
    assert gen.get_account_point() == 10.0


# 使用patch装饰器模拟文件打开操作，测试图片上传功能
@patch("builtins.open", new_callable=MagicMock)
def test_image_uploader(mock_open, image_gen, mock_session):
    # 模拟文件读取返回图片数据
    mock_open.return_value.__enter__.return_value.read.return_value = b"image_data"
    # 模拟三次GET请求的响应：获取token、恢复上传、验证上传
    mock_session.get.return_value.json.side_effect = [
        {"status": 200, "data": {"token": "mock_token"}},  # 获取上传token
        {"result": 1},  # 恢复上传成功
        {"status": 200, "data": {"url": "mock_url"}},  # 获取最终URL
    ]
    # 模拟两次POST请求的响应：分片上传、完成上传
    mock_session.post.return_value.json.side_effect = [{"result": 1}, {"result": 1}]

    # 调用图片上传方法
    result = image_gen.image_uploader("mock_image_path")
    # 断言返回正确的URL
    assert result == "mock_url"


# 测试获取任务元数据功能
def test_fetch_metadata(base_gen, mock_session):
    # 测试任务完成状态（status >= 90）
    mock_session.get.return_value.json.return_value = {
        "data": {"status": 100, "key": "value"}
    }
    result, status = base_gen.fetch_metadata("mock_task_id")
    assert result == {"status": 100, "key": "value"}
    assert status == TaskStatus.COMPLETED

    # 测试任务失败状态（status = 50）
    mock_session.get.return_value.json.return_value = {"data": {"status": 50}}
    result, status = base_gen.fetch_metadata("mock_task_id")
    assert result == {"status": 50}
    assert status == TaskStatus.FAILED

    # 测试任务等待状态（其他status值）
    mock_session.get.return_value.json.return_value = {"data": {"status": 80}}
    result, status = base_gen.fetch_metadata("mock_task_id")
    assert result == {"status": 80}
    assert status == TaskStatus.PENDING


# 使用参数化测试，测试ImageGen的get_images和VideoGen的get_video方法
@pytest.mark.parametrize(
    "gen_class,method_name", [(ImageGen, "get_images"), (VideoGen, "get_video")]
)
def test_get_content(gen_class, method_name, mock_session):
    # 创建生成器实例
    gen = gen_class("mock_cookie")
    # 模拟POST请求响应，返回任务ID
    mock_session.post.return_value.json.return_value = {
        "data": {"task": {"id": "mock_id"}}
    }
    # 模拟GET请求响应，返回完成的任务结果
    mock_session.get.return_value.json.return_value = {
        "data": {"status": 100, "works": [{"resource": {"resource": "mock_resource"}}]}
    }

    # 动态获取要测试的方法
    method = getattr(gen, method_name)
    # 调用方法并断言返回结果
    result = method("mock_prompt")
    assert result == ["mock_resource"]


# 使用参数化测试，测试ImageGen的save_images和VideoGen的save_video方法
@pytest.mark.parametrize(
    "gen_class,method_name", [(ImageGen, "save_images"), (VideoGen, "save_video")]
)
@patch("os.path.exists", return_value=False)  # 模拟目录不存在
@patch("os.mkdir")  # 模拟创建目录
def test_save_content(mock_mkdir, mock_exists, gen_class, method_name, mock_session):
    # 创建生成器实例
    gen = gen_class("mock_cookie")
    # 模拟POST请求响应，返回任务ID
    mock_session.post.return_value.json.return_value = {
        "data": {"task": {"id": "mock_id"}}
    }
    # 模拟GET请求响应，返回完成的任务结果
    mock_session.get.return_value.json.return_value = {
        "data": {"status": 100, "works": [{"resource": {"resource": "mock_resource"}}]}
    }

    # 模拟HTTP状态码为200
    mock_session.get.return_value.status_code = 200

    # 模拟文件打开操作
    with patch("builtins.open", new_callable=MagicMock):
        # 动态获取要测试的方法
        method = getattr(gen, method_name)
        # 调用保存方法
        method("mock_prompt", "mock_output_dir")

    # 断言创建了输出目录
    mock_mkdir.assert_called_once_with("mock_output_dir")


# 测试每日签到检查功能
def test_call_for_daily_check():
    # 创建模拟的session对象
    mock_session = MagicMock()
    # 模拟请求成功的情况
    mock_session.get.return_value.ok = True
    mock_session.get.return_value.json.return_value = {"status": "success"}

    # 调用每日签到检查，传入中国版标识
    result = call_for_daily_check(mock_session, True)
    # 断言返回True表示签到成功
    assert result == True

    # 模拟请求失败的情况
    mock_session.get.return_value.ok = False
    # 断言会抛出异常
    with pytest.raises(Exception):
        call_for_daily_check(mock_session, False)


# 模拟文件打开操作，读取模拟图片数据
@patch("builtins.open", new_callable=mock_open, read_data=b"mock_image_data")
# 模拟VideoGen的image_uploader方法，返回模拟图片URL
@patch.object(VideoGen, "image_uploader", return_value="mock_image_url")
def test_video_gen_get_video(mock_uploader, mock_file, video_gen, mock_session):
    # 模拟POST请求响应，返回任务ID
    mock_session.post.return_value.json.return_value = {
        "data": {"task": {"id": "mock_id"}}
    }
    # 模拟GET请求响应，返回视频生成结果
    mock_session.get.return_value.json.return_value = {
        "data": {"status": 100, "works": [{"resource": {"resource": "mock_video_url"}}]}
    }

    # 测试基本的视频生成功能
    result = video_gen.get_video("mock_prompt")
    assert result == ["mock_video_url"]

    # 测试使用图片路径生成视频
    result = video_gen.get_video("mock_prompt", image_path="mock_image.jpg")
    assert result == ["mock_video_url"]

    # 测试使用图片URL生成视频
    result = video_gen.get_video("mock_prompt", image_url="http://mock.com/image.jpg")
    assert result == ["mock_video_url"]

    # 测试高质量视频生成
    result = video_gen.get_video("mock_prompt", is_high_quality=True)
    assert result == ["mock_video_url"]


# 模拟目录不存在的情况
@patch("os.path.exists", return_value=False)
# 模拟创建目录操作
@patch("os.mkdir")
# 模拟文件打开操作
@patch("builtins.open", new_callable=mock_open)
def test_video_gen_save_video(
    mock_file, mock_mkdir, mock_exists, video_gen, mock_session
):
    # 模拟POST请求响应，返回任务ID
    mock_session.post.return_value.json.return_value = {
        "data": {"task": {"id": "mock_id"}}
    }
    # 模拟GET请求响应，返回视频生成结果
    mock_session.get.return_value.json.return_value = {
        "data": {"status": 100, "works": [{"resource": {"resource": "mock_video_url"}}]}
    }
    # 模拟下载视频内容
    mock_session.get.return_value.content = b"mock_video_content"
    # 模拟HTTP状态码为200
    mock_session.get.return_value.status_code = 200

    # 调用保存视频方法
    video_gen.save_video("mock_prompt", "mock_output_dir")

    # 断言创建了输出目录
    mock_mkdir.assert_called_once_with("mock_output_dir")
    # 断言以二进制写入模式打开了视频文件
    mock_file.assert_called_once_with("mock_output_dir/0.mp4", "wb")
    # 断言写入了视频内容
    mock_file().write.assert_called_once_with(b"mock_video_content")


# 模拟文件打开操作，读取模拟图片数据
@patch("builtins.open", new_callable=mock_open, read_data=b"mock_image_data")
# 模拟ImageGen的image_uploader方法，返回模拟图片URL
@patch.object(ImageGen, "image_uploader", return_value="mock_image_url")
def test_image_gen_get_images(mock_uploader, mock_file, image_gen, mock_session):
    # 模拟POST请求响应，返回任务ID
    mock_session.post.return_value.json.return_value = {
        "data": {"task": {"id": "mock_id"}}
    }
    # 模拟GET请求响应，返回图片生成结果
    mock_session.get.return_value.json.return_value = {
        "data": {"status": 100, "works": [{"resource": {"resource": "mock_image_url"}}]}
    }

    # 测试基本的图片生成功能
    result = image_gen.get_images("mock_prompt")
    assert result == ["mock_image_url"]

    # 测试使用图片路径生成图片（图生图）
    result = image_gen.get_images("mock_prompt", image_path="mock_image.jpg")
    assert result == ["mock_image_url"]

    # 测试使用图片URL生成图片（图生图）
    result = image_gen.get_images("mock_prompt", image_url="http://mock.com/image.jpg")
    assert result == ["mock_image_url"]


# 模拟目录不存在的情况
@patch("os.path.exists", return_value=False)
# 模拟创建目录操作
@patch("os.mkdir")
# 模拟文件打开操作
@patch("builtins.open", new_callable=mock_open)
# 模拟线程操作
@patch("threading.Thread")
def test_image_gen_save_images(
    mock_thread, mock_file, mock_mkdir, mock_exists, image_gen, mock_session
):
    # 模拟POST请求响应，返回任务ID
    mock_session.post.return_value.json.return_value = {
        "data": {"task": {"id": "mock_id"}}
    }
    # 模拟GET请求响应，返回图片生成结果
    mock_session.get.return_value.json.return_value = {
        "data": {"status": 100, "works": [{"resource": {"resource": "mock_image_url"}}]}
    }
    # 模拟下载图片内容
    mock_session.get.return_value.content = b"mock_image_content"

    # 调用保存图片方法
    image_gen.save_images("mock_prompt", "mock_output_dir")

    # 断言创建了输出目录
    mock_mkdir.assert_called_once_with("mock_output_dir")
    # 断言创建了线程
    mock_thread.assert_called_once()
    # 断言启动了线程
    mock_thread.return_value.start.assert_called_once()
    # 断言等待线程完成
    mock_thread.return_value.join.assert_called_once()


# 模拟VideoGen的fetch_metadata方法
@patch.object(VideoGen, "fetch_metadata")
def test_extend_video_completed(mock_fetch_metadata, video_gen):
    # 模拟fetch_metadata返回已完成的任务状态和包含资源的作品列表
    mock_fetch_metadata.return_value = (
        {"works": [{"resource": {"resource": "mock_resource"}}]},
        TaskStatus.COMPLETED,
    )

    # 调用视频延长方法
    result = video_gen.extend_video(123, "mock_prompt")

    # 断言返回正确的资源列表
    assert result == ["mock_resource"]


# 模拟VideoGen的fetch_metadata方法
@patch.object(VideoGen, "fetch_metadata")
def test_extend_video_pending(mock_fetch_metadata, video_gen):
    # 模拟fetch_metadata返回等待中的任务状态和空的作品列表
    mock_fetch_metadata.return_value = (
        {"works": []},
        TaskStatus.PENDING,
    )

    # 断言会抛出异常（因为任务状态不是已完成）
    with pytest.raises(Exception):
        video_gen.extend_video(123, "mock_prompt")


# 模拟VideoGen的fetch_metadata方法
@patch.object(VideoGen, "fetch_metadata")
def test_extend_video_failed(mock_fetch_metadata, video_gen):
    # 模拟fetch_metadata返回失败的任务状态和空的作品列表
    mock_fetch_metadata.return_value = (
        {"works": []},
        TaskStatus.FAILED,
    )

    # 断言会抛出异常（因为任务状态是失败）
    with pytest.raises(Exception):
        video_gen.extend_video(123, "mock_prompt")


# 模拟VideoGen的fetch_metadata方法
@patch.object(VideoGen, "fetch_metadata")
def test_extend_video_no_works(mock_fetch_metadata, video_gen):
    # 模拟fetch_metadata返回已完成的任务状态但没有作品数据
    mock_fetch_metadata.return_value = (
        {},  # 空字典，没有works字段
        TaskStatus.COMPLETED,
    )

    # 断言会抛出异常（因为没有找到视频作品）
    with pytest.raises(Exception):
        video_gen.extend_video(123, "mock_prompt")


# 当此文件作为主程序运行时，执行pytest测试
if __name__ == "__main__":
    # 启动pytest测试框架，运行所有测试用例
    pytest.main()
