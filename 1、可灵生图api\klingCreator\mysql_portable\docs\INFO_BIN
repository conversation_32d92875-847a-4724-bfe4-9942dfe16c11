===== Information about the build process: =====
Build was run at 2023-10-12 14:21:51Z
Build was done on Windows-10.0.20348 processor AMD64
Build was done using cmake 3.15.3

===== CMAKE_GENERATOR =====
Visual Studio 16 2019

===== Feature flags used: =====
-- Cache values
ADD_GDB_INDEX:BOOL=OFF
BOOST_INCLUDE_DIR:PATH=C:/boost/boost_1_77_0
BUILD_EXAMPLES:BOOL=OFF
BUILD_MANPAGES:BOOL=OFF
BUILD_SHARED_LIBS:BOOL=OFF
BUILD_STATIC_LIBS:BOOL=OFF
BUILD_TESTING:BOOL=ON
BUILD_TESTS:BOOL=OFF
BUILD_TOOLS:BOOL=OFF
BUNDLE_MECAB:BOOL=ON
BUNDLE_RUNTIME_LIBRARIES:BOOL=OFF
CBOR_BUFFER_GROWTH:STRING=2
CBOR_CUSTOM_ALLOC:BOOL=OFF
CBOR_MAX_STACK_SIZE:STRING=2048
CBOR_PRETTY_PRINTER:BOOL=OFF
CCAPISERVER_EXECUTABLE:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/bin/ccapiserver.exe
CHECK_ERRMSG_FORMAT:BOOL=OFF
CMAKE_CONFIGURATION_TYPES:STRING=DebugReleaseMinSizeRelRelWithDebInfo
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/MySQL
COVERAGE:BOOL=OFF
CRYPTO_LIBRARY:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep3/lib/libcrypto.lib
DOWNLOAD_BOOST:BOOL=OFF
DOWNLOAD_BOOST_TIMEOUT:STRING=600
DUMPBIN_EXECUTABLE:FILEPATH=C:/Program Files (x86)/Microsoft Visual Studio/2019/Enterprise/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/dumpbin.exe
ENABLED_PROFILING:BOOL=ON
ENABLE_EXPERIMENT_SYSVARS:BOOL=OFF
ENABLE_GCOV:BOOL=OFF
ENABLE_GPROF:BOOL=OFF
EVENT__COVERAGE:BOOL=OFF
EVENT__DISABLE_BENCHMARK:BOOL=OFF
EVENT__DISABLE_CLOCK_GETTIME:BOOL=OFF
EVENT__DISABLE_DEBUG_MODE:BOOL=OFF
EVENT__DISABLE_MM_REPLACEMENT:BOOL=OFF
EVENT__DISABLE_OPENSSL:BOOL=OFF
EVENT__DISABLE_REGRESS:BOOL=OFF
EVENT__DISABLE_SAMPLES:BOOL=OFF
EVENT__DISABLE_TESTS:BOOL=OFF
EVENT__DISABLE_THREAD_SUPPORT:BOOL=OFF
EVENT__ENABLE_VERBOSE_DEBUG:BOOL=OFF
EVENT__FORCE_KQUEUE_CHECK:BOOL=OFF
EVENT__LIBRARY_TYPE:STRING=DEFAULT
EVENT__MSVC_STATIC_RUNTIME:BOOL=OFF
EXTRA_NAME_SUFFIX:STRING=
FLEX_EXECUTABLE:FILEPATH=C:/bin/bin/win_flex.exe
FLEX_INCLUDE_DIR:PATH=C:/bin/lib/winflexbison3/tools
FORCE_COLORED_OUTPUT:BOOL=OFF
FORCE_INSOURCE_BUILD:BOOL=ON
FUZZ:BOOL=OFF
GLOB_RECURSE_SQL_HEADERS:BOOL=OFF
GSSAPI_INCLUDE_DIR:PATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/include
HANDLE_FATAL_SIGNALS:BOOL=ON
HARNESS_NAME:STRING=mysqlrouter
HAVE_CRYPTO_DLL:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep3/bin/libcrypto-3-x64.dll
HAVE_JEMALLOC_DLL:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep2/bin/jemalloc.dll
HAVE_LIBEVENT2:BOOL=1
HAVE_OPENSSL_DLL:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep3/bin/libssl-3-x64.dll
HAVE_jemalloc_PDB:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep2/bin/jemalloc.pdb
HAVE_libcrypto-3-x64_PDB:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep3/bin/libcrypto-3-x64.pdb
HAVE_libssl-3-x64_PDB:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep3/bin/libssl-3-x64.pdb
HUGE_FUZZ:BOOL=OFF
INSTALL_LAYOUT:STRING=STANDALONE
INSTALL_STATIC_LIBRARIES:BOOL=ON
KERBEROS_CUSTOM_DLL:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/bin/krb5_64.dll
KERBEROS_CUSTOM_DLL_comerr64.dll:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/bin/comerr64.dll
KERBEROS_CUSTOM_DLL_gssapi64.dll:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/bin/gssapi64.dll
KERBEROS_CUSTOM_DLL_k5sprt64.dll:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/bin/k5sprt64.dll
KERBEROS_CUSTOM_DLL_krbcc64.dll:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/bin/krbcc64.dll
KERBEROS_CUSTOM_DLL_xpprof64.dll:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/bin/xpprof64.dll
KERBEROS_CUSTOM_LIBRARY:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/lib/krb5_64.lib
KERBEROS_CUSTOM_LIBRARY_comerr64.lib:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/lib/comerr64.lib
KERBEROS_CUSTOM_LIBRARY_gssapi64.lib:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/lib/gssapi64.lib
KERBEROS_CUSTOM_LIBRARY_k5sprt64.lib:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/lib/k5sprt64.lib
KERBEROS_CUSTOM_LIBRARY_krbcc64.lib:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/lib/krbcc64.lib
KERBEROS_CUSTOM_LIBRARY_xpprof64.lib:FILEPATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/lib/xpprof64.lib
KERBEROS_INCLUDE_DIR:PATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit/include
KERBEROS_ROOT_DIR:PATH=C:/build/sb_1-12964488-1697119036.71/krb5-1.21.2-windows-vs16-64bit
LINK_STATIC_RUNTIME_LIBRARIES:BOOL=OFF
LOCAL_BOOST_DIR:FILEPATH=C:/boost/boost_1_77_0
LOCAL_BOOST_ZIP:FILEPATH=LOCAL_BOOST_ZIP-NOTFOUND
MECAB_INCLUDE_DIR:PATH=C:/build/sb_1-12964488-1697119036.71/mecab-0.996-windows-x86-64bit/sdk
MECAB_LIBRARY:FILEPATH=C:/build/sb_1-12964488-1697119036.71/mecab-0.996-windows-x86-64bit/sdk/libmecab.lib
MSVC_CPPCHECK:BOOL=OFF
MUTEXTYPE:STRING=event
MYSQLX_ADDITIONAL_TESTS_ENABLE:BOOL=OFF
MYSQLX_GENERATE_DIR:STRING=C:/build/sb_1-12964488-1697119036.71/release/plugin/x/generated
MYSQL_DATADIR:PATH=C:/Program Files/MySQL/MySQL Server 8.0/data
MYSQL_ICU_DATADIR:PATH=C:/Program Files/MySQL/MySQL Server 8.0/lib/private
MYSQL_KEYRINGDIR:PATH=C:/Program Files/MySQL/MySQL Server 8.0/keyring
MYSQL_MAINTAINER_MODE:BOOL=OFF
MYSQL_ROUTER_INI:STRING=mysqlrouter.conf
MYSQL_ROUTER_NAME:STRING=MySQL Router
NFC_LINUX:BOOL=OFF
OPENSSL_APPLINK_C:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep3/include/openssl/applink.c
OPENSSL_EXECUTABLE:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep3/bin/openssl.exe
OPENSSL_INCLUDE_DIR:PATH=C:/build/sb_1-12964488-1697119036.71/dep3/include
OPENSSL_LIBRARY:FILEPATH=C:/build/sb_1-12964488-1697119036.71/dep3/lib/libssl.lib
OPENSSL_ROOT_DIR:PATH=C:/build/sb_1-12964488-1697119036.71/dep3
OPTIMIZE_SANITIZER_BUILDS:BOOL=ON
PERL_EXECUTABLE:FILEPATH=C:/Perl64/bin/perl.exe
PRINT_FUZZ:BOOL=OFF
REDIRECT_DOXYGEN_STDOUT:BOOL=OFF
ROUTER_DATADIR:STRING=ENV{APPDATA}\\data
ROUTER_INSTALL_LAYOUT:STRING=STANDALONE
ROUTER_LOGDIR:STRING=ENV{APPDATA}\\log
ROUTER_PLUGINDIR:STRING=C:/Program Files (x86)/MySQL/lib
ROUTER_RUNTIMEDIR:STRING=ENV{APPDATA}
SANE_MALLOC:BOOL=OFF
SASL_CUSTOM_LIBRARY:FILEPATH=C:/build/sb_1-12964488-1697119036.71/cyrus-sasl-2.1.28-MD-windows-vs16-64bit/lib/libsasl.lib
SASL_INCLUDE_DIR:PATH=C:/build/sb_1-12964488-1697119036.71/cyrus-sasl-2.1.28-MD-windows-vs16-64bit/include
SASL_LIBRARY_DLL:FILEPATH=C:/build/sb_1-12964488-1697119036.71/cyrus-sasl-2.1.28-MD-windows-vs16-64bit/lib/libsasl.dll
SASL_ROOT_DIR:PATH=C:/build/sb_1-12964488-1697119036.71/cyrus-sasl-2.1.28-MD-windows-vs16-64bit
SASL_SCRAM_PLUGIN:FILEPATH=C:/build/sb_1-12964488-1697119036.71/cyrus-sasl-2.1.28-MD-windows-vs16-64bit/lib/saslSCRAM.dll
SASL_STATIC_LIBRARY:FILEPATH=C:/build/sb_1-12964488-1697119036.71/cyrus-sasl-2.1.28-MD-windows-vs16-64bit/lib/libsasl.lib
SUNRPC_INCLUDE_DIRS:PATH=C:/build/sb_1-12964488-1697119036.71/mysql-8.0.35/plugin/group_replication/libmysqlgcs/src/bindings/xcom/xcom/windeps/sunrpcC:/build/sb_1-12964488-1697119036.71/mysql-8.0.35/plugin/group_replication/libmysqlgcs/src/bindings/xcom/xcom/windeps/sunrpc/rpc
TMPDIR:PATH=
USE_BISON_RESULTS_FROM_MAKE_DIST:BOOL=OFF
USE_HIDAPI:BOOL=OFF
USE_LD_LLD:BOOL=OFF
USE_PCSC:BOOL=OFF
USE_WINHELLO:BOOL=OFF
WINDEPS_INCLUDE_DIRS:PATH=C:/build/sb_1-12964488-1697119036.71/mysql-8.0.35/plugin/group_replication/libmysqlgcs/src/bindings/xcom/xcom/windeps/include
WIN_DEBUG_NO_INLINE:BOOL=OFF
WIN_DEBUG_RTC:BOOL=OFF
WIN_INCREMENTAL_LINK:BOOL=OFF
WIN_STL_DEBUG_ITERATORS:STRING=2
WITH_ARCHIVE_STORAGE_ENGINE:BOOL=ON
WITH_ASAN:BOOL=OFF
WITH_AUTHENTICATION_CLIENT_PLUGINS:BOOL=ON
WITH_AUTHENTICATION_FIDO:BOOL=OFF
WITH_AUTHENTICATION_KERBEROS:BOOL=OFF
WITH_AUTHENTICATION_LDAP:BOOL=1
WITH_BLACKHOLE_STORAGE_ENGINE:BOOL=ON
WITH_BOOST:PATH=C:/boost
WITH_CLIENT_PROTOCOL_TRACING:BOOL=ON
WITH_CS_PROTOBUF:BOOL=OFF
WITH_CURL:STRING=none
WITH_DEBUG:BOOL=OFF
WITH_DEFAULT_COMPILER_OPTIONS:BOOL=ON
WITH_ERROR_INSERT:BOOL=OFF
WITH_EXAMPLES:BOOL=ON
WITH_FEDERATED_STORAGE_ENGINE:BOOL=ON
WITH_FIDO:STRING=bundled
WITH_HYPERGRAPH_OPTIMIZER:BOOL=OFF
WITH_ICU:STRING=bundled
WITH_INNODB_EXTRA_DEBUG:BOOL=OFF
WITH_INNODB_MEMCACHED:BOOL=OFF
WITH_JEMALLOC:BOOL=OFF
WITH_JSON_BINLOG_LIBRARY:BOOL=OFF
WITH_KERBEROS:STRING=C:\build\sb_1-12964488-1697119036.71\krb5-1.21.2-windows-vs16-64bit
WITH_LDAP:STRING=system
WITH_LIBEVENT:STRING=bundled
WITH_LOCK_ORDER:BOOL=OFF
WITH_LSAN:BOOL=OFF
WITH_LTO:BOOL=OFF
WITH_LZ4:STRING=bundled
WITH_MECAB:STRING=C:\build\sb_1-12964488-1697119036.71\mecab-0.996-windows-x86-64bit
WITH_MECAB_PATH:PATH=C:/build/sb_1-12964488-1697119036.71/mecab-0.996-windows-x86-64bit
WITH_MSAN:BOOL=OFF
WITH_MSCRT_DEBUG:BOOL=OFF
WITH_MYSQLX:BOOL=ON
WITH_MYSQLX_USE_PROTOBUF_FULL:BOOL=OFF
WITH_NDB:BOOL=OFF
WITH_NDBCLUSTER_STORAGE_ENGINE:BOOL=ON
WITH_NDBMTD:BOOL=ON
WITH_NDB_CCFLAGS:STRING=
WITH_NDB_DEBUG:BOOL=OFF
WITH_NDB_JAVA:BOOL=OFF
WITH_NDB_PORT:STRING=
WITH_NGRAM_PARSER:BOOL=ON
WITH_NUMA:BOOL=OFF
WITH_PROTOBUF:STRING=bundled
WITH_RAPIDJSON:STRING=bundled
WITH_ROUTER:BOOL=ON
WITH_SHARED_UNITTEST_LIBRARY:BOOL=OFF
WITH_SSL_PATH:PATH=C:/build/sb_1-12964488-1697119036.71/dep3
WITH_SYSTEM_LIBS:BOOL=OFF
WITH_TCMALLOC:BOOL=OFF
WITH_TCMALLOC_DEBUG:BOOL=OFF
WITH_TESTS:BOOL=OFF
WITH_TEST_TRACE_PLUGIN:BOOL=OFF
WITH_TSAN:BOOL=OFF
WITH_UBSAN:BOOL=OFF
WITH_UNIT_TESTS:BOOL=ON
WITH_ZLIB:STRING=bundled
WITH_ZSTD:STRING=bundled
WIX_DIR:PATH=C:/Users/<USER>/.dotnet/tools
WIX_EXECUTABLE:FILEPATH=C:/Users/<USER>/.dotnet/tools/wix.exe
WSL_EXECUTABLE:FILEPATH=C:/Windows/System32/wsl.exe
XPLUGIN_LOG_PROTOBUF:STRING=1
ZSTD_BUILD_CONTRIB:BOOL=OFF
ZSTD_BUILD_PROGRAMS:BOOL=ON
ZSTD_BUILD_TESTS:BOOL=OFF
ZSTD_LEGACY_SUPPORT:BOOL=OFF
ZSTD_MULTITHREAD_SUPPORT:BOOL=OFF
ZSTD_USE_STATIC_RUNTIME:BOOL=OFF
protobuf_BUILD_CONFORMANCE:BOOL=OFF
protobuf_BUILD_LIBPROTOC:BOOL=OFF
protobuf_BUILD_PROTOC_BINARIES:BOOL=ON
protobuf_DISABLE_RTTI:BOOL=OFF

===== Compiler flags used: =====
CMAKE_BUILD_TYPE: RelWithDebInfo
Compiler: MSVC 19.29.30151.0
COMPILE_DEFINITIONS: _WIN32_WINNT=0x0601;WIN32_LEAN_AND_MEAN;NOGDI;NOMINMAX;HAVE_CONFIG_H;__STDC_LIMIT_MACROS;__STDC_FORMAT_MACROS;_USE_MATH_DEFINES;LZ4_DISABLE_DEPRECATE_WARNINGS;HAVE_TLSv13
CMAKE_C_FLAGS: /DWIN32 /D_WINDOWS /W3 /MP -D_CRT_SECURE_NO_WARNINGS -D_CRT_NONSTDC_NO_DEPRECATE -D_WINSOCK_DEPRECATED_NO_WARNINGS /wd4267 /wd4244
CMAKE_CXX_FLAGS: /DWIN32 /D_WINDOWS /W3 /GR /EHsc /MP -D_CRT_SECURE_NO_WARNINGS -D_CRT_NONSTDC_NO_DEPRECATE -D_WINSOCK_DEPRECATED_NO_WARNINGS /wd4267 /wd4244 /permissive-
CMAKE_C_FLAGS_RELWITHDEBINFO: /MD /Z7 /O2 /Ob1 /DNDEBUG /EHsc /FC
CMAKE_CXX_FLAGS_RELWITHDEBINFO: /MD /Z7 /O2 /Ob1 /DNDEBUG /EHsc /FC /std:c++17
===== EOF =====
