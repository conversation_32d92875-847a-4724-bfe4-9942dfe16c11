#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie工具模块
提供统一的cookie读取功能
"""

import os

def get_cookie():
    """
    从cookie.txt文件读取cookie
    
    Returns:
        str: cookie字符串，如果读取失败返回空字符串
    """
    cookie_file = os.path.join(os.path.dirname(__file__), 'cookie.txt')
    
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookie = f.read().strip()
            if cookie:
                return cookie
            else:
                print("❌ cookie.txt文件为空")
                return ""
    except FileNotFoundError:
        print("❌ 未找到cookie.txt文件")
        print("请在项目根目录创建cookie.txt文件并将你的cookie字符串放入其中")
        return ""
    except Exception as e:
        print(f"❌ 读取cookie.txt文件失败: {e}")
        return ""

def check_cookie():
    """
    检查cookie是否有效
    
    Returns:
        bool: cookie是否有效
    """
    cookie = get_cookie()
    if not cookie:
        return False
    
    # 检查cookie是否包含必要的字段
    required_fields = ['userId', 'kuaishou.ai.portal_st']
    for field in required_fields:
        if field not in cookie:
            print(f"❌ Cookie中缺少必要字段: {field}")
            return False
    
    return True

def update_cookie(new_cookie):
    """
    更新cookie.txt文件
    
    Args:
        new_cookie (str): 新的cookie字符串
        
    Returns:
        bool: 更新是否成功
    """
    cookie_file = os.path.join(os.path.dirname(__file__), 'cookie.txt')
    
    try:
        with open(cookie_file, 'w', encoding='utf-8') as f:
            f.write(new_cookie.strip())
        print("✅ Cookie更新成功")
        return True
    except Exception as e:
        print(f"❌ 更新cookie失败: {e}")
        return False
