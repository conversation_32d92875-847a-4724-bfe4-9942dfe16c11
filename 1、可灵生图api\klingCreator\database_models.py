#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库模型 - 支持MySQL和SQLite
"""

import os
import json
import hashlib
import sqlite3
from datetime import datetime
from typing import List, Dict, Optional, Union

# 尝试导入MySQL依赖
try:
    import pymysql
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    print("⚠️ pymysql未安装，将使用SQLite数据库")

class DatabaseManager:
    """数据库管理器 - 支持MySQL和SQLite"""
    
    def __init__(self, config_file='db_config.json'):
        self.config = self.load_config(config_file)
        self.connection = None
        self.db_type = self.config.get('type', 'sqlite')
        
    def load_config(self, config_file):
        """加载数据库配置"""
        if os.path.exists(config_file):
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            # 默认使用SQLite
            return {
                'type': 'sqlite',
                'database': 'kling_ai.db'
            }
    
    def connect(self):
        """连接数据库"""
        try:
            if self.db_type == 'mysql' and MYSQL_AVAILABLE:
                self.connection = pymysql.connect(
                    host=self.config['host'],
                    port=self.config['port'],
                    user=self.config['user'],
                    password=self.config['password'],
                    database=self.config['database'],
                    charset='utf8mb4',
                    cursorclass=pymysql.cursors.DictCursor,
                    autocommit=True
                )
                print("✅ MySQL数据库连接成功")
            else:
                self.connection = sqlite3.connect(
                    self.config['database'], 
                    check_same_thread=False
                )
                self.connection.row_factory = sqlite3.Row
                print("✅ SQLite数据库连接成功")
            
            self.init_tables()
            return True
            
        except Exception as e:
            print(f"❌ 数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询"""
        if not self.connection:
            if not self.connect():
                return []
        
        try:
            if self.db_type == 'mysql':
                with self.connection.cursor() as cursor:
                    cursor.execute(sql, params or ())
                    return cursor.fetchall()
            else:
                cursor = self.connection.cursor()
                cursor.execute(sql, params or ())
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except Exception as e:
            print(f"查询执行失败: {e}")
            return []
    
    def execute_update(self, sql: str, params: tuple = None) -> int:
        """执行更新/插入/删除"""
        if not self.connection:
            if not self.connect():
                return 0
        
        try:
            if self.db_type == 'mysql':
                with self.connection.cursor() as cursor:
                    cursor.execute(sql, params or ())
                    return cursor.lastrowid if 'INSERT' in sql.upper() else cursor.rowcount
            else:
                cursor = self.connection.cursor()
                cursor.execute(sql, params or ())
                self.connection.commit()
                return cursor.lastrowid if 'INSERT' in sql.upper() else cursor.rowcount
                
        except Exception as e:
            print(f"更新执行失败: {e}")
            return 0
    
    def init_tables(self):
        """初始化数据表"""
        if self.db_type == 'mysql':
            self.init_mysql_tables()
        else:
            self.init_sqlite_tables()
    
    def init_mysql_tables(self):
        """初始化MySQL表"""
        tables = [
            # 上传图片表
            """
            CREATE TABLE IF NOT EXISTS uploaded_images (
                id INT AUTO_INCREMENT PRIMARY KEY,
                filename VARCHAR(255) NOT NULL COMMENT '原始文件名',
                safe_filename VARCHAR(255) NOT NULL COMMENT '安全文件名',
                file_path VARCHAR(500) NOT NULL COMMENT '文件路径',
                file_md5 VARCHAR(32) NOT NULL UNIQUE COMMENT '文件MD5值',
                file_size INT NOT NULL COMMENT '文件大小',
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '上传时间',
                INDEX idx_md5 (file_md5),
                INDEX idx_upload_time (upload_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='上传图片表'
            """,
            
            # 生成图片表
            """
            CREATE TABLE IF NOT EXISTS generated_images (
                id INT AUTO_INCREMENT PRIMARY KEY,
                upload_id INT NOT NULL COMMENT '上传图片ID',
                generated_filename VARCHAR(255) NOT NULL COMMENT '生成的文件名',
                generated_path VARCHAR(500) NOT NULL COMMENT '生成图片路径',
                style_name VARCHAR(100) NOT NULL COMMENT '生成风格',
                prompt TEXT COMMENT '提示词',
                likes INT DEFAULT 0 COMMENT '点赞数',
                dislikes INT DEFAULT 0 COMMENT '点踩数',
                generation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
                FOREIGN KEY (upload_id) REFERENCES uploaded_images(id) ON DELETE CASCADE,
                INDEX idx_upload_id (upload_id),
                INDEX idx_style (style_name),
                INDEX idx_generation_time (generation_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生成图片表'
            """,
            
            # 用户操作表
            """
            CREATE TABLE IF NOT EXISTS user_actions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                generated_id INT NOT NULL COMMENT '生成图片ID',
                action_type ENUM('like', 'dislike') NOT NULL COMMENT '操作类型',
                user_ip VARCHAR(45) NOT NULL COMMENT '用户IP',
                action_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
                FOREIGN KEY (generated_id) REFERENCES generated_images(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_action (generated_id, user_ip, action_type),
                INDEX idx_generated_id (generated_id),
                INDEX idx_action_time (action_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作表'
            """
        ]
        
        for table_sql in tables:
            try:
                self.execute_update(table_sql)
                print(f"✅ MySQL表创建成功")
            except Exception as e:
                print(f"❌ MySQL表创建失败: {e}")
    
    def init_sqlite_tables(self):
        """初始化SQLite表"""
        tables = [
            # 上传图片表
            """
            CREATE TABLE IF NOT EXISTS uploaded_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT NOT NULL,
                safe_filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_md5 TEXT NOT NULL UNIQUE,
                file_size INTEGER NOT NULL,
                upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            """,
            
            # 生成图片表
            """
            CREATE TABLE IF NOT EXISTS generated_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                upload_id INTEGER NOT NULL,
                generated_filename TEXT NOT NULL,
                generated_path TEXT NOT NULL,
                style_name TEXT NOT NULL,
                prompt TEXT,
                likes INTEGER DEFAULT 0,
                dislikes INTEGER DEFAULT 0,
                generation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (upload_id) REFERENCES uploaded_images(id) ON DELETE CASCADE
            )
            """,
            
            # 用户操作表
            """
            CREATE TABLE IF NOT EXISTS user_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                generated_id INTEGER NOT NULL,
                action_type TEXT NOT NULL CHECK (action_type IN ('like', 'dislike')),
                user_ip TEXT NOT NULL,
                action_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (generated_id) REFERENCES generated_images(id) ON DELETE CASCADE,
                UNIQUE(generated_id, user_ip, action_type)
            )
            """,

            # 视频生成任务表
            """
            CREATE TABLE IF NOT EXISTS video_generation_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_name TEXT NOT NULL,
                upload_id INTEGER NOT NULL,
                image_ids TEXT NOT NULL,
                total_segments INTEGER NOT NULL,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
                final_video_path TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (upload_id) REFERENCES uploaded_images(id) ON DELETE CASCADE
            )
            """,

            # 视频片段表
            """
            CREATE TABLE IF NOT EXISTS video_segments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                task_id INTEGER NOT NULL,
                first_image_id INTEGER NOT NULL,
                last_image_id INTEGER NOT NULL,
                segment_order INTEGER NOT NULL,
                first_image_filename TEXT NOT NULL,
                last_image_filename TEXT NOT NULL,
                status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
                video_path TEXT,
                created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (task_id) REFERENCES video_generation_tasks(id) ON DELETE CASCADE,
                FOREIGN KEY (first_image_id) REFERENCES generated_images(id) ON DELETE CASCADE,
                FOREIGN KEY (last_image_id) REFERENCES generated_images(id) ON DELETE CASCADE
            )
            """
        ]
        
        # 创建索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_uploaded_md5 ON uploaded_images(file_md5)",
            "CREATE INDEX IF NOT EXISTS idx_uploaded_time ON uploaded_images(upload_time)",
            "CREATE INDEX IF NOT EXISTS idx_generated_upload_id ON generated_images(upload_id)",
            "CREATE INDEX IF NOT EXISTS idx_generated_style ON generated_images(style_name)",
            "CREATE INDEX IF NOT EXISTS idx_generated_time ON generated_images(generation_time)",
            "CREATE INDEX IF NOT EXISTS idx_actions_generated_id ON user_actions(generated_id)",
            "CREATE INDEX IF NOT EXISTS idx_actions_time ON user_actions(action_time)",
            "CREATE INDEX IF NOT EXISTS idx_video_tasks_upload_id ON video_generation_tasks(upload_id)",
            "CREATE INDEX IF NOT EXISTS idx_video_tasks_status ON video_generation_tasks(status)",
            "CREATE INDEX IF NOT EXISTS idx_video_segments_task_id ON video_segments(task_id)",
            "CREATE INDEX IF NOT EXISTS idx_video_segments_status ON video_segments(status)"
        ]
        
        for table_sql in tables:
            try:
                self.execute_update(table_sql)
                print(f"✅ SQLite表创建成功")
            except Exception as e:
                print(f"❌ SQLite表创建失败: {e}")
        
        for index_sql in indexes:
            try:
                self.execute_update(index_sql)
            except Exception as e:
                print(f"⚠️ 索引创建警告: {e}")

def calculate_file_md5(file_path: str) -> str:
    """计算文件MD5值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception as e:
        print(f"计算MD5失败: {e}")
        return ""

class UploadedImageModel:
    """上传图片模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_image(self, filename: str, safe_filename: str, file_path: str) -> int:
        """添加上传图片记录"""
        # 计算文件MD5
        file_md5 = calculate_file_md5(file_path)
        if not file_md5:
            raise Exception("无法计算文件MD5")
        
        # 检查是否已存在相同MD5的文件
        existing = self.get_by_md5(file_md5)
        if existing:
            print(f"发现重复文件，MD5: {file_md5}")
            return existing['id']
        
        # 获取文件大小
        file_size = os.path.getsize(file_path)
        
        sql = """
        INSERT INTO uploaded_images (filename, safe_filename, file_path, file_md5, file_size)
        VALUES (?, ?, ?, ?, ?)
        """
        
        return self.db.execute_update(sql, (filename, safe_filename, file_path, file_md5, file_size))
    
    def get_by_md5(self, file_md5: str) -> Optional[Dict]:
        """根据MD5获取图片记录"""
        sql = "SELECT * FROM uploaded_images WHERE file_md5 = ?"
        results = self.db.execute_query(sql, (file_md5,))
        return results[0] if results else None
    
    def get_by_id(self, upload_id: int) -> Optional[Dict]:
        """根据ID获取图片记录"""
        sql = "SELECT * FROM uploaded_images WHERE id = ?"
        results = self.db.execute_query(sql, (upload_id,))
        return results[0] if results else None
    
    def get_recent_uploads(self, limit: int = 50) -> List[Dict]:
        """获取最近上传的图片"""
        sql = "SELECT * FROM uploaded_images ORDER BY upload_time DESC LIMIT ?"
        return self.db.execute_query(sql, (limit,))

    def get_by_filename(self, filename: str) -> Optional[Dict]:
        """根据文件名获取上传记录"""
        sql = "SELECT * FROM uploaded_images WHERE filename = ?"
        results = self.db.execute_query(sql, (filename,))
        return results[0] if results else None

    def get_all_uploads(self) -> List[Dict]:
        """获取所有上传记录"""
        sql = "SELECT * FROM uploaded_images ORDER BY upload_time DESC"
        return self.db.execute_query(sql)

class GeneratedImageModel:
    """生成图片模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_generated_image(self, upload_id: int, generated_filename: str, 
                          generated_path: str, style_name: str, prompt: str = None) -> int:
        """添加生成图片记录"""
        sql = """
        INSERT INTO generated_images (upload_id, generated_filename, generated_path, style_name, prompt)
        VALUES (?, ?, ?, ?, ?)
        """
        
        return self.db.execute_update(sql, (upload_id, generated_filename, generated_path, style_name, prompt))
    
    def get_by_upload_id(self, upload_id: int) -> List[Dict]:
        """获取指定上传图片的所有生成结果"""
        sql = """
        SELECT g.*, u.filename as original_filename, u.file_md5
        FROM generated_images g
        JOIN uploaded_images u ON g.upload_id = u.id
        WHERE g.upload_id = ?
        ORDER BY g.generation_time DESC
        """
        return self.db.execute_query(sql, (upload_id,))
    
    def get_recent_generated(self, limit: int = 50) -> List[Dict]:
        """获取最近生成的图片"""
        sql = """
        SELECT g.*, u.filename as original_filename, u.file_md5
        FROM generated_images g
        JOIN uploaded_images u ON g.upload_id = u.id
        ORDER BY g.generation_time DESC
        LIMIT ?
        """
        return self.db.execute_query(sql, (limit,))
    
    def update_likes(self, generated_id: int, likes: int, dislikes: int) -> bool:
        """更新点赞点踩数"""
        sql = "UPDATE generated_images SET likes = ?, dislikes = ? WHERE id = ?"
        return self.db.execute_update(sql, (likes, dislikes, generated_id)) > 0
    
    def get_by_id(self, generated_id: int) -> Optional[Dict]:
        """根据ID获取生成图片记录"""
        sql = """
        SELECT g.*, u.filename as original_filename, u.file_md5
        FROM generated_images g
        JOIN uploaded_images u ON g.upload_id = u.id
        WHERE g.id = ?
        """
        results = self.db.execute_query(sql, (generated_id,))
        return results[0] if results else None

    def get_by_upload_filename(self, filename: str) -> List[Dict]:
        """根据上传文件名获取所有生成图片"""
        sql = """
        SELECT g.*, u.filename as original_filename, u.file_md5,
               g.generated_filename as filename, g.style_name, g.generation_time as created_at
        FROM generated_images g
        JOIN uploaded_images u ON g.upload_id = u.id
        WHERE u.filename = ?
        ORDER BY g.generation_time DESC
        """
        return self.db.execute_query(sql, (filename,))

class UserActionModel:
    """用户操作模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def record_action(self, generated_id: int, action_type: str, user_ip: str) -> bool:
        """记录用户操作"""
        sql = """
        INSERT OR REPLACE INTO user_actions (generated_id, action_type, user_ip)
        VALUES (?, ?, ?)
        """
        
        if self.db.db_type == 'mysql':
            sql = """
            INSERT INTO user_actions (generated_id, action_type, user_ip)
            VALUES (%s, %s, %s)
            ON DUPLICATE KEY UPDATE action_time = CURRENT_TIMESTAMP
            """
        
        return self.db.execute_update(sql, (generated_id, action_type, user_ip)) > 0
    
    def get_user_action(self, generated_id: int, user_ip: str, action_type: str) -> Optional[Dict]:
        """获取用户操作记录"""
        sql = """
        SELECT * FROM user_actions
        WHERE generated_id = ? AND user_ip = ? AND action_type = ?
        """
        results = self.db.execute_query(sql, (generated_id, user_ip, action_type))
        return results[0] if results else None

    def remove_action(self, generated_id: int, user_ip: str, action_type: str) -> bool:
        """删除用户操作记录"""
        sql = """
        DELETE FROM user_actions
        WHERE generated_id = ? AND user_ip = ? AND action_type = ?
        """
        return self.db.execute_update(sql, (generated_id, user_ip, action_type)) > 0

    def remove_all_actions(self, generated_id: int, user_ip: str) -> bool:
        """删除用户对某图片的所有操作记录"""
        sql = """
        DELETE FROM user_actions
        WHERE generated_id = ? AND user_ip = ?
        """
        return self.db.execute_update(sql, (generated_id, user_ip)) > 0

    def get_actions_by_generated_id(self, generated_id: int) -> List[Dict]:
        """获取某个生成图片的所有操作记录"""
        sql = """
        SELECT * FROM user_actions
        WHERE generated_id = ?
        ORDER BY action_time DESC
        """
        return self.db.execute_query(sql, (generated_id,))

class VideoGenerationTaskModel:
    """视频生成任务模型"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self._table_created = False

    def _ensure_table_created(self):
        """确保表已创建"""
        if not self._table_created:
            # 重新初始化表（包含视频表）
            self.db.init_tables()
            self._table_created = True

    def create_task(self, task_name: str, upload_id: int, image_ids: List[int], total_segments: int) -> int:
        """创建视频生成任务"""
        self._ensure_table_created()
        import json
        sql = """
        INSERT INTO video_generation_tasks (task_name, upload_id, image_ids, total_segments)
        VALUES (?, ?, ?, ?)
        """
        return self.db.execute_update(sql, (task_name, upload_id, json.dumps(image_ids), total_segments))

    def get_task_by_id(self, task_id: int) -> Optional[Dict]:
        """根据ID获取任务"""
        self._ensure_table_created()
        sql = "SELECT * FROM video_generation_tasks WHERE id = ?"
        results = self.db.execute_query(sql, (task_id,))
        if results:
            task = results[0]
            # 解析image_ids JSON
            import json
            task['image_ids'] = json.loads(task['image_ids'])
            return task
        return None

    def get_tasks_by_upload(self, upload_id: int) -> List[Dict]:
        """获取指定上传ID的所有任务"""
        self._ensure_table_created()
        sql = "SELECT * FROM video_generation_tasks WHERE upload_id = ? ORDER BY created_time DESC"
        results = self.db.execute_query(sql, (upload_id,))
        # 解析image_ids JSON
        import json
        for task in results:
            task['image_ids'] = json.loads(task['image_ids'])
        return results

    def update_task_status(self, task_id: int, status: str, final_video_path: str = None) -> bool:
        """更新任务状态"""
        self._ensure_table_created()
        if final_video_path:
            sql = """
            UPDATE video_generation_tasks
            SET status = ?, final_video_path = ?, updated_time = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            return self.db.execute_update(sql, (status, final_video_path, task_id)) > 0
        else:
            sql = """
            UPDATE video_generation_tasks
            SET status = ?, updated_time = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            return self.db.execute_update(sql, (status, task_id)) > 0

class VideoSegmentModel:
    """视频片段模型"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self._table_created = False

    def _ensure_table_created(self):
        """确保表已创建"""
        if not self._table_created:
            # 重新初始化表（包含视频表）
            self.db.init_tables()
            self._table_created = True

    def create_segment(self, task_id: int, first_image_id: int, last_image_id: int,
                      segment_order: int, first_image_filename: str, last_image_filename: str) -> int:
        """创建视频片段"""
        self._ensure_table_created()
        sql = """
        INSERT INTO video_segments
        (task_id, first_image_id, last_image_id, segment_order, first_image_filename, last_image_filename)
        VALUES (?, ?, ?, ?, ?, ?)
        """
        return self.db.execute_update(sql, (
            task_id, first_image_id, last_image_id, segment_order,
            first_image_filename, last_image_filename
        ))

    def get_segments_by_task(self, task_id: int) -> List[Dict]:
        """获取任务的所有片段"""
        self._ensure_table_created()
        sql = "SELECT * FROM video_segments WHERE task_id = ? ORDER BY segment_order"
        return self.db.execute_query(sql, (task_id,))

    def update_segment_status(self, segment_id: int, status: str, video_path: str = None) -> bool:
        """更新片段状态"""
        self._ensure_table_created()
        if video_path:
            sql = """
            UPDATE video_segments
            SET status = ?, video_path = ?, updated_time = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            return self.db.execute_update(sql, (status, video_path, segment_id)) > 0
        else:
            sql = """
            UPDATE video_segments
            SET status = ?, updated_time = CURRENT_TIMESTAMP
            WHERE id = ?
            """
            return self.db.execute_update(sql, (status, segment_id)) > 0

    def get_completed_segments_count(self, task_id: int) -> int:
        """获取任务已完成的片段数量"""
        self._ensure_table_created()
        sql = "SELECT COUNT(*) as count FROM video_segments WHERE task_id = ? AND status = 'completed'"
        result = self.db.execute_query(sql, (task_id,))
        return result[0]['count'] if result else 0

# 模型别名
UploadModel = UploadedImageModel

# 全局数据库实例
db_manager = DatabaseManager()
db = db_manager  # 别名，兼容旧代码
uploaded_image_model = UploadedImageModel(db_manager)
generated_image_model = GeneratedImageModel(db_manager)
user_action_model = UserActionModel(db_manager)
video_task_model = VideoGenerationTaskModel(db_manager)
video_segment_model = VideoSegmentModel(db_manager)
