@echo off
chcp 65001 >nul
title 可灵AI图生图项目 - 一键启动器

echo ================================================================================
echo 🚀 可灵AI图生图项目 - 一键启动器
echo ================================================================================
echo 📁 项目目录: %~dp0
echo 🌐 服务地址: http://localhost:5000
echo 📝 主页面: http://localhost:5000/
echo 🖼️ 画廊页面: http://localhost:5000/gallery
echo ================================================================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo    请先安装Python 3.7+并添加到系统PATH
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 🔍 检查项目文件...
if not exist "app.py" (
    echo ❌ 找不到app.py文件
    echo    请确保在正确的项目目录中运行此脚本
    pause
    exit /b 1
)

echo ✅ 项目文件检查通过

echo.
echo 📦 安装/更新依赖...
if exist "requirements.txt" (
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ⚠️ 依赖安装失败，但继续尝试启动...
    ) else (
        echo ✅ 依赖安装完成
    )
) else (
    echo ⚠️ 未找到requirements.txt，跳过依赖安装
)

echo.
echo 🚀 启动后端服务器...
echo ================================================================================
echo 💡 使用说明:
echo    1. 等待服务器启动完成（看到"访问地址: http://localhost:5000"）
echo    2. 浏览器会自动打开项目页面
echo    3. 在主页上传图片并生成AI图片
echo    4. 在画廊查看所有生成的图片（按最新生成时间排序）
echo    5. 按 Ctrl+C 停止服务器
echo ================================================================================
echo.

REM 启动Python应用
python app.py

echo.
echo 🛑 服务器已停止
pause
