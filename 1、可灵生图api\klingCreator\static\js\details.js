// 详情页面JavaScript

let uploadId = '';
let originalFilename = '';
let allGenerated = [];
let filteredGenerated = [];
let currentFilter = 'all';

// 多选功能相关变量
let isMultiSelectMode = false;
let selectedImages = []; // 存储选中的图片，按选择顺序
let shiftPressed = false;
let videoTasks = []; // 存储视频任务

// DOM元素
const originalImage = document.getElementById('original-image');
const originalTitle = document.getElementById('original-title');
const uploadTime = document.getElementById('upload-time');
const generatedCount = document.getElementById('generated-count');
const totalLikes = document.getElementById('total-likes');
const totalDislikes = document.getElementById('total-dislikes');
const totalUnprocessed = document.getElementById('total-unprocessed');

const filterTabs = document.querySelectorAll('.filter-tab');
const sortSelect = document.getElementById('sort-select');
const generatedGrid = document.getElementById('generated-grid');
const loading = document.getElementById('loading');
const emptyState = document.getElementById('empty-state');

const countAll = document.getElementById('count-all');
const countLiked = document.getElementById('count-liked');
const countDisliked = document.getElementById('count-disliked');
const countUnprocessed = document.getElementById('count-unprocessed');

const imageModal = document.getElementById('image-modal');
const modalImage = document.getElementById('modal-image');
const modalTitle = document.getElementById('modal-title');
const modalStyle = document.getElementById('modal-style');
const modalTime = document.getElementById('modal-time');
const modalDownload = document.getElementById('modal-download');
const modalClose = document.getElementById('modal-close');

// 多选相关DOM元素
const multiSelectSection = document.getElementById('multi-select-section');
const selectedCount = document.getElementById('selected-count');
const generateVideoBtn = document.getElementById('generate-transition-video-btn');
const cancelMultiSelectBtn = document.getElementById('cancel-multi-select-btn');
const videoTasksContainer = document.getElementById('video-tasks-container');

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    // 从URL获取upload_id
    const pathParts = window.location.pathname.split('/');
    uploadId = pathParts[pathParts.length - 1];

    console.log('从URL获取的upload_id:', uploadId);

    loadDetailsData();

    // 绑定重新使用图片按钮事件
    const reuseImageBtn = document.getElementById('reuse-image-btn');
    if (reuseImageBtn) {
        reuseImageBtn.addEventListener('click', handleReuseImage);
    }

    setupEventListeners();
    setupMultiSelectListeners();
});

// 设置事件监听器
function setupEventListeners() {
    // 筛选标签
    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            setActiveFilter(filter);
            filterAndDisplayGenerated();
        });
    });
    
    // 排序选择
    sortSelect.addEventListener('change', function() {
        filterAndDisplayGenerated();
    });
    
    // 模态框关闭
    modalClose.addEventListener('click', closeModal);
    imageModal.addEventListener('click', function(e) {
        if (e.target === imageModal) {
            closeModal();
        }
    });
    
    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeModal();
        }
    });
}

// 加载详情数据
async function loadDetailsData() {
    try {
        showLoading();

        console.log('正在加载详情数据，upload_id:', uploadId);
        const response = await fetch(`/api/details/${uploadId}`);
        console.log('API响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('详情数据:', data);
        
        if (data.success) {
            displayOriginalInfo(data.original);
            allGenerated = data.generated || [];
            updateCounts();
            filterAndDisplayGenerated();

            // 加载视频任务
            loadVideoTasks();
        } else {
            throw new Error(data.error || '加载数据失败');
        }
        
    } catch (error) {
        console.error('加载详情数据失败:', error);
        showError('加载数据失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// 显示原图信息
function displayOriginalInfo(original) {
    originalImage.src = `/api/upload/${encodeURIComponent(original.filename)}`;
    originalTitle.textContent = original.filename;
    // 处理UTC时间转换为北京时间
    const utcUploadDate = new Date(original.upload_time + 'Z'); // 确保被识别为UTC时间
    uploadTime.textContent = utcUploadDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: 'Asia/Shanghai'
    });
    generatedCount.textContent = original.generated_count || 0;
    totalLikes.textContent = original.like_count || 0;
    totalDislikes.textContent = original.dislike_count || 0;

    // 计算未处理数量 = 总数 - 收藏数 - 标记删除数
    const unprocessedCount = (original.generated_count || 0) - (original.like_count || 0) - (original.dislike_count || 0);
    totalUnprocessed.textContent = Math.max(0, unprocessedCount);
}

// 设置活动筛选器
function setActiveFilter(filter) {
    currentFilter = filter;
    filterTabs.forEach(tab => {
        tab.classList.remove('active');
        if (tab.getAttribute('data-filter') === filter) {
            tab.classList.add('active');
        }
    });
}

// 更新计数
function updateCounts() {
    const likedCount = allGenerated.filter(item => item.user_action === 'like').length;
    const dislikedCount = allGenerated.filter(item => item.user_action === 'dislike').length;
    const unprocessedCount = allGenerated.filter(item => !item.user_action || item.user_action === null).length;

    countAll.textContent = allGenerated.length;
    countLiked.textContent = likedCount;
    countDisliked.textContent = dislikedCount;
    countUnprocessed.textContent = unprocessedCount;
}

// 筛选和显示生成图片
function filterAndDisplayGenerated() {
    const sortBy = sortSelect.value;
    
    // 筛选
    filteredGenerated = allGenerated.filter(item => {
        switch (currentFilter) {
            case 'liked':
                return item.user_action === 'like';
            case 'disliked':
                return item.user_action === 'dislike';
            case 'unprocessed':
                return !item.user_action || item.user_action === null;
            case 'all':
            default:
                return true;
        }
    });
    
    // 排序
    filteredGenerated.sort((a, b) => {
        switch (sortBy) {
            case 'newest':
                return new Date(b.created_at) - new Date(a.created_at);
            case 'oldest':
                return new Date(a.created_at) - new Date(b.created_at);
            case 'style':
                return (a.style_name || '').localeCompare(b.style_name || '');
            default:
                return 0;
        }
    });
    
    displayGenerated();
}

// 显示生成图片
function displayGenerated() {
    if (filteredGenerated.length === 0) {
        showEmptyState();
        return;
    }
    
    hideEmptyState();
    
    generatedGrid.innerHTML = '';
    
    filteredGenerated.forEach(item => {
        const card = createGeneratedCard(item);
        generatedGrid.appendChild(card);
    });
}

// 创建生成图片卡片
function createGeneratedCard(item) {
    const card = document.createElement('div');
    card.className = 'generated-card';
    card.setAttribute('data-generated-id', item.id);
    
    // 处理UTC时间转换为北京时间
    const utcDate = new Date(item.created_at + 'Z'); // 确保被识别为UTC时间
    const generatedTime = utcDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: 'Asia/Shanghai'
    });
    // 确定按钮状态和图标
    const isLiked = item.user_action === 'like';
    const isDisliked = item.user_action === 'dislike';

    const likeActive = isLiked ? 'active' : '';
    const dislikeActive = isDisliked ? 'active' : '';

    // 根据状态选择图标
    const likeIcon = isLiked ? '⭐' : '☆';  // 实心星 vs 空心星
    const dislikeIcon = isDisliked ? '🗑️' : '🗂️';  // 实心垃圾桶 vs 空心文件夹

    card.innerHTML = `
        <div class="generated-image-container">
            <img src="/api/image/${encodeURIComponent(item.filename)}"
                 alt="${item.style_name}"
                 class="generated-image clickable-image"
                 data-filename="${encodeURIComponent(item.filename)}"
                 data-style-name="${encodeURIComponent(item.style_name)}"
                 data-generated-time="${generatedTime}"
                 onerror="this.style.display='none'; this.parentElement.innerHTML='<div style=\\'display:flex;align-items:center;justify-content:center;height:200px;background:#f7fafc;color:#a0aec0;\\'>📷 图片加载失败</div>'"
            />
            <div class="generated-corner-actions">
                <button class="btn-favorite ${likeActive}" data-generated-id="${item.id}" title="${isLiked ? '取消收藏' : '收藏这张图片'}">${likeIcon}</button>
                <button class="btn-remove ${dislikeActive}" data-generated-id="${item.id}" title="${isDisliked ? '取消标记删除' : '标记删除'}">${dislikeIcon}</button>
            </div>
        </div>
        <div class="generated-info">
            <div class="generated-style">${item.style_name}</div>
            <div class="generated-time">📅 ${generatedTime}</div>
        </div>
    `;
    
    // 绑定图片点击事件
    const imageElement = card.querySelector('.clickable-image');
    if (imageElement) {
        imageElement.addEventListener('click', function(event) {
            // 检查是否按住Shift键
            if (event.shiftKey) {
                event.preventDefault();
                event.stopPropagation();

                // 如果不在多选模式，启动多选模式
                if (!isMultiSelectMode) {
                    enterMultiSelectMode();
                }

                // 处理图片选择
                handleImageSelectionFromCard(card);
                return;
            }

            const filename = decodeURIComponent(this.getAttribute('data-filename'));
            const styleName = decodeURIComponent(this.getAttribute('data-style-name'));
            const generatedTime = this.getAttribute('data-generated-time');
            openImageModal(filename, styleName, generatedTime);
        });

        // 添加鼠标指针样式
        imageElement.style.cursor = 'pointer';
    }

    // 绑定收藏和标记删除事件
    const likeBtn = card.querySelector('.btn-favorite');
    const dislikeBtn = card.querySelector('.btn-remove');

    if (likeBtn) {
        likeBtn.addEventListener('click', function(event) {
            event.stopPropagation();
            const generatedId = this.getAttribute('data-generated-id');
            likeImage(generatedId, this, event);
        });
    }

    if (dislikeBtn) {
        dislikeBtn.addEventListener('click', function(event) {
            event.stopPropagation();
            const generatedId = this.getAttribute('data-generated-id');
            dislikeImage(generatedId, this, event);
        });
    }
    
    return card;
}

// 打开图片模态框
function openImageModal(filename, styleName, time) {
    const imageUrl = `/api/image/${encodeURIComponent(filename)}`;

    // 设置图片源
    modalImage.src = imageUrl;

    // 设置标题为风格名称（更友好的显示）
    modalTitle.textContent = styleName;

    // 设置详细信息
    modalStyle.textContent = `📁 文件名: ${filename}`;
    modalTime.textContent = `📅 生成时间: ${time}`;

    // 设置下载功能 - 传递相同的URL以利用缓存
    modalDownload.onclick = () => downloadImage(filename, imageUrl);

    // 显示模态框
    imageModal.style.display = 'flex';

    // 预加载图片以提升下载速度
    if (!modalImage.complete) {
        modalImage.onload = () => {
            console.log('✅ 图片已加载到缓存，下载将更快');
        };
    }

    // 添加键盘事件监听，按ESC关闭
    document.addEventListener('keydown', handleEscapeKey);
}

// 处理ESC键关闭
function handleEscapeKey(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
}

// 关闭模态框
function closeModal() {
    imageModal.style.display = 'none';
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleEscapeKey);
}

// 收藏图片
async function likeImage(generatedId, button, event) {
    if (button.disabled) return;
    button.disabled = true;

    try {
        console.log(`发送收藏请求: 图片ID=${generatedId}`);

        const response = await fetch(`/api/like/${generatedId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('收藏响应:', data);
        
        if (data.success) {
            if (data.action === 'liked') {
                button.classList.add('active');
                button.innerHTML = '⭐';  // 实心星
                button.title = '取消收藏';

                // 取消标记删除状态
                const dislikeBtn = button.parentElement.querySelector('.btn-remove');
                if (dislikeBtn) {
                    dislikeBtn.classList.remove('active');
                    dislikeBtn.innerHTML = '🗂️';  // 空心文件夹
                    dislikeBtn.title = '标记删除';
                }
            } else if (data.action === 'removed_like') {
                button.classList.remove('active');
                button.innerHTML = '☆';  // 空心星
                button.title = '收藏这张图片';
            }

            // 重新加载数据以更新计数
            setTimeout(() => loadDetailsData(), 500);
        }
    } catch (error) {
        console.error('收藏异常:', error);
    } finally {
        setTimeout(() => { button.disabled = false; }, 500);
    }
}

// 标记删除图片
async function dislikeImage(generatedId, button, event) {
    if (button.disabled) return;
    button.disabled = true;

    try {
        console.log(`发送标记删除请求: 图片ID=${generatedId}`);

        const response = await fetch(`/api/dislike/${generatedId}`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('标记删除响应:', data);
        
        if (data.success) {
            if (data.action === 'disliked') {
                button.classList.add('active');
                button.innerHTML = '🗑️';  // 实心垃圾桶
                button.title = '取消标记删除';

                // 取消收藏状态
                const likeBtn = button.parentElement.querySelector('.btn-favorite');
                if (likeBtn) {
                    likeBtn.classList.remove('active');
                    likeBtn.innerHTML = '☆';  // 空心星
                    likeBtn.title = '收藏这张图片';
                }
            } else if (data.action === 'removed_dislike') {
                button.classList.remove('active');
                button.innerHTML = '🗂️';  // 空心文件夹
                button.title = '标记删除';
            }

            // 重新加载数据以更新计数
            setTimeout(() => loadDetailsData(), 500);
        }
    } catch (error) {
        console.error('标记删除异常:', error);
    } finally {
        setTimeout(() => { button.disabled = false; }, 500);
    }
}

// 下载图片
async function downloadImage(filename, imageSrc) {
    console.log('开始下载文件:', filename);

    // 更新下载按钮状态
    const downloadBtn = document.getElementById('modal-download');
    const originalText = downloadBtn.innerHTML;
    downloadBtn.innerHTML = '⏳ 下载中...';
    downloadBtn.disabled = true;

    try {
        // 检查图片是否已经在缓存中（通过模态框的图片元素）
        const modalImg = document.getElementById('modal-image');
        let downloadUrl = imageSrc;

        // 如果模态框图片已加载，使用其src（可能已缓存）
        if (modalImg && modalImg.src && modalImg.complete) {
            downloadUrl = modalImg.src;
            console.log('使用已缓存的图片进行下载');
        }

        // 直接下载（快速方法）
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = filename;
        link.style.display = 'none';

        // 添加到DOM并触发下载
        document.body.appendChild(link);
        link.click();

        // 立即清理
        setTimeout(() => {
            if (document.body.contains(link)) {
                document.body.removeChild(link);
            }
        }, 100);

        console.log('✅ 下载已触发:', filename);

        // 短暂延迟后恢复按钮状态
        setTimeout(() => {
            downloadBtn.innerHTML = originalText;
            downloadBtn.disabled = false;
        }, 800);

    } catch (error) {
        console.error('❌ 下载失败:', error);

        // 恢复按钮状态
        downloadBtn.innerHTML = '❌ 下载失败';
        downloadBtn.disabled = false;

        // 3秒后恢复原始状态
        setTimeout(() => {
            downloadBtn.innerHTML = originalText;
        }, 3000);
    }
}

// 显示加载状态
function showLoading() {
    loading.style.display = 'block';
    generatedGrid.style.display = 'none';
    emptyState.style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    loading.style.display = 'none';
    generatedGrid.style.display = 'grid';
}

// 显示空状态
function showEmptyState() {
    generatedGrid.style.display = 'none';
    emptyState.style.display = 'block';
}

// 隐藏空状态
function hideEmptyState() {
    emptyState.style.display = 'none';
    generatedGrid.style.display = 'grid';
}

// 显示错误
function showError(message) {
    generatedGrid.innerHTML = `
        <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: #e53e3e;">
            <h3>❌ 加载失败</h3>
            <p>${message}</p>
            <button onclick="loadDetailsData()" class="btn btn-primary">🔄 重试</button>
        </div>
    `;
}

// 重新使用图片
function handleReuseImage() {
    console.log('重新使用图片，upload_id:', uploadId);

    // 获取原图信息
    const originalImage = document.getElementById('original-image');
    const originalTitle = document.getElementById('original-title');

    if (!originalImage || !originalImage.src) {
        alert('❌ 无法获取图片信息');
        return;
    }

    const imageUrl = originalImage.src;
    const imageName = originalTitle.textContent || '重新使用的图片';

    console.log('图片URL:', imageUrl);
    console.log('图片名称:', imageName);

    // 构建跳转URL，将图片信息作为参数传递
    const params = new URLSearchParams({
        reuse: 'true',
        imageUrl: imageUrl,
        imageName: imageName,
        uploadId: uploadId
    });

    const targetUrl = `/?${params.toString()}`;
    console.log('跳转URL:', targetUrl);

    // 在新标签页中打开
    window.open(targetUrl, '_blank');
}

// 多选功能相关函数
function setupMultiSelectListeners() {
    // 生成视频按钮点击事件
    console.log('🔧 设置多选监听器...');
    console.log('🔧 generateVideoBtn:', generateVideoBtn);

    if (generateVideoBtn) {
        console.log('✅ 找到生成视频按钮，绑定事件...');
        generateVideoBtn.addEventListener('click', function(e) {
            console.log('🎬 生成视频按钮被点击！');
            e.preventDefault();
            handleGenerateTransitionVideo();
        });
    } else {
        console.error('❌ 未找到生成视频按钮！');
    }

    // 取消多选按钮点击事件
    if (cancelMultiSelectBtn) {
        cancelMultiSelectBtn.addEventListener('click', exitMultiSelectMode);
    }

    // 键盘事件监听
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Shift') {
            shiftPressed = true;
        }
        if (e.key === 'Escape' && isMultiSelectMode) {
            exitMultiSelectMode();
        }
    });

    document.addEventListener('keyup', function(e) {
        if (e.key === 'Shift') {
            shiftPressed = false;
        }
    });
}

function toggleMultiSelectMode() {
    if (isMultiSelectMode) {
        exitMultiSelectMode();
    } else {
        enterMultiSelectMode();
    }
}

function enterMultiSelectMode() {
    isMultiSelectMode = true;
    selectedImages = [];

    // 显示多选工具栏
    if (multiSelectSection) {
        multiSelectSection.style.display = 'block';
    }

    // 为所有图片添加多选样式和事件
    const imageCards = document.querySelectorAll('.generated-card');
    imageCards.forEach(card => {
        card.classList.add('multi-select-mode');
        // 使用capture阶段来确保多选事件优先处理
        card.addEventListener('click', handleImageSelection, true);
    });

    updateSelectedCount();
    updateMultiSelectToolbar();
}

function exitMultiSelectMode() {
    isMultiSelectMode = false;
    selectedImages = [];

    // 隐藏多选工具栏
    if (multiSelectSection) {
        multiSelectSection.style.display = 'none';
    }

    // 移除所有图片的多选样式和事件
    const imageCards = document.querySelectorAll('.generated-card');
    imageCards.forEach(card => {
        card.classList.remove('multi-select-mode', 'selected');
        // 移除capture阶段的事件监听器
        card.removeEventListener('click', handleImageSelection, true);

        // 移除选择顺序标记
        const orderBadge = card.querySelector('.selection-order');
        if (orderBadge) {
            orderBadge.remove();
        }
    });

    updateSelectedCount();
}

function updateMultiSelectToolbar() {
    if (!generateVideoBtn) return;

    // 根据选择的图片数量启用/禁用生成视频按钮
    if (selectedImages.length >= 2) {
        generateVideoBtn.disabled = false;
        generateVideoBtn.classList.remove('disabled');
    } else {
        generateVideoBtn.disabled = true;
        generateVideoBtn.classList.add('disabled');
    }
}

function handleImageSelectionFromCard(card) {
    const imageId = parseInt(card.dataset.generatedId);
    if (!imageId) return;

    const existingIndex = selectedImages.findIndex(img => img.id === imageId);

    if (existingIndex >= 0) {
        // 取消选择
        selectedImages.splice(existingIndex, 1);
        card.classList.remove('selected');

        // 移除当前图片的序号标记
        const orderElement = card.querySelector('.selection-order');
        if (orderElement) {
            orderElement.remove();
        }

        // 重新编号所有选中的图片
        updateSelectionOrder();
    } else {
        // 添加选择
        const imageData = {
            id: imageId,
            filename: card.querySelector('.clickable-image').getAttribute('data-filename'),
            style_name: decodeURIComponent(card.querySelector('.clickable-image').getAttribute('data-style-name')),
            order: selectedImages.length + 1
        };

        selectedImages.push(imageData);
        card.classList.add('selected');

        // 显示选择序号
        const orderBadge = document.createElement('div');
        orderBadge.className = 'selection-order';
        orderBadge.textContent = imageData.order;
        card.appendChild(orderBadge);
    }

    updateSelectedCount();
    updateMultiSelectToolbar();
}

function handleImageSelection(event) {
    event.preventDefault();
    event.stopPropagation();

    if (!isMultiSelectMode) return;

    const card = event.currentTarget;
    handleImageSelectionFromCard(card);
}

function showSelectionOrder(card, order) {
    // 移除现有的顺序标记
    const existingOrder = card.querySelector('.selection-order');
    if (existingOrder) {
        existingOrder.remove();
    }

    // 添加新的顺序标记
    const orderElement = document.createElement('div');
    orderElement.className = 'selection-order';
    orderElement.textContent = order;
    card.appendChild(orderElement);
}

function updateSelectionOrder() {
    // 首先清除所有图片的序号标记
    const allCards = document.querySelectorAll('.generated-card');
    allCards.forEach(card => {
        const orderElement = card.querySelector('.selection-order');
        if (orderElement) {
            orderElement.remove();
        }
    });

    // 然后重新为选中的图片添加序号
    selectedImages.forEach((img, index) => {
        img.order = index + 1;
        const card = document.querySelector(`[data-generated-id="${img.id}"]`);
        if (card) {
            showSelectionOrder(card, img.order);
        }
    });
}

function clearSelection() {
    selectedImages = [];

    const imageCards = document.querySelectorAll('.generated-card');
    imageCards.forEach(card => {
        card.classList.remove('selected');
        const orderElement = card.querySelector('.selection-order');
        if (orderElement) {
            orderElement.remove();
        }
    });

    updateSelectedCount();
}

function updateSelectedCount() {
    if (selectedCount) {
        selectedCount.textContent = selectedImages.length;
    }

    if (generateVideoBtn) {
        generateVideoBtn.disabled = selectedImages.length < 2;
    }
}

async function handleGenerateTransitionVideo() {
    console.log('🎬 handleGenerateTransitionVideo 函数被调用！');
    console.log('📋 当前选中图片数量:', selectedImages.length);
    console.log('📋 selectedImages:', selectedImages);
    console.log('📋 uploadId:', uploadId);

    if (selectedImages.length < 2) {
        console.log('❌ 图片数量不足，需要至少2张');
        showNotification('请至少选择2张图片', 'warning');
        return;
    }

    try {
        console.log('🎬 开始生成过渡视频...');
        console.log('📋 选中的图片:', selectedImages);

        generateVideoBtn.disabled = true;
        generateVideoBtn.innerHTML = '🔄 生成中...';

        const requestData = {
            task_name: `过渡视频_${new Date().toLocaleString()}`,
            upload_id: parseInt(uploadId),
            selected_image_ids: selectedImages.map(img => img.id)
        };

        console.log('📋 请求数据:', requestData);

        const response = await fetch('/api/create-video-task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        console.log('📋 响应状态:', response.status);

        const data = await response.json();
        console.log('📋 响应数据:', data);

        if (data.success) {
            showNotification(data.message || '视频生成任务已创建，正在后台处理中...', 'success');
            console.log('✅ 任务创建成功，任务ID:', data.task_id);

            exitMultiSelectMode();

            // 立即刷新视频任务列表
            loadVideoTasks();

            // 设置定时刷新，监控进度
            const refreshInterval = setInterval(() => {
                console.log('🔄 定时刷新任务状态...');
                loadVideoTasks();
            }, 3000); // 每3秒刷新一次

            // 30秒后停止定时刷新
            setTimeout(() => {
                clearInterval(refreshInterval);
                console.log('⏹️ 停止定时刷新');
            }, 30000);

        } else {
            const errorMsg = data.message || data.error || '创建视频任务失败';
            console.error('❌ 任务创建失败:', errorMsg);
            console.error('📋 完整响应数据:', data);

            if (data.details) {
                console.error('📋 错误详情:', data.details);
            }

            throw new Error(errorMsg);
        }

    } catch (error) {
        console.error('❌ 生成视频失败:', error);

        let errorMessage = '生成视频失败: ' + error.message;

        // 如果是网络错误，提供更友好的提示
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            errorMessage = '网络连接失败，请检查服务器是否正常运行';
        }

        showNotification(errorMessage, 'error');

    } finally {
        generateVideoBtn.disabled = false;
        generateVideoBtn.innerHTML = '🎬 生成过渡视频';
    }
}

// 视频任务管理函数
async function loadVideoTasks() {
    try {
        const response = await fetch(`/api/video-tasks/${uploadId}`);
        const data = await response.json();

        if (data.success) {
            videoTasks = data.tasks;
            displayVideoTasks();
        } else {
            console.error('加载视频任务失败:', data.error);
        }
    } catch (error) {
        console.error('加载视频任务失败:', error);
    }
}

function displayVideoTasks() {
    if (!videoTasksContainer) return;

    if (videoTasks.length === 0) {
        videoTasksContainer.innerHTML = '<div class="empty-state">暂无视频生成任务</div>';
        return;
    }

    const tasksHtml = videoTasks.map(task => {
        const statusIcon = getTaskStatusIcon(task.status);
        const statusText = getTaskStatusText(task.status);
        const progress = task.total_segments > 0 ? (task.completed_segments / task.total_segments * 100) : 0;

        return `
            <div class="video-task-card" data-task-id="${task.id}">
                <div class="task-header">
                    <h4>${statusIcon} ${task.task_name}</h4>
                    <span class="task-status ${task.status}">${statusText}</span>
                </div>
                <div class="task-info">
                    <div class="task-detail">
                        <span>📅 创建时间: ${task.created_time ? new Date(task.created_time + 'Z').toLocaleString('zh-CN', {
                            year: 'numeric',
                            month: '2-digit',
                            day: '2-digit',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit',
                            timeZone: 'Asia/Shanghai'
                        }) : '未知时间'}</span>
                    </div>
                    <div class="task-detail">
                        <span>🎬 视频片段: ${task.completed_segments || 0}/${task.total_segments}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progress}%"></div>
                    </div>
                </div>
                <div class="task-actions">
                    ${task.status === 'completed' && task.final_video_path ? `
                        <button class="btn btn-primary" onclick="downloadVideo('${task.final_video_path}')">
                            💾 下载完整视频
                        </button>
                    ` : ''}
                    <button class="btn btn-secondary" onclick="viewTaskDetails(${task.id})">
                        📋 查看详情
                    </button>
                    ${task.status === 'processing' ? `
                        <button class="btn btn-info" onclick="refreshTaskStatus(${task.id})">
                            🔄 刷新状态
                        </button>
                    ` : ''}
                </div>
                ${task.status === 'failed' ? `
                    <div class="error-message">
                        ❌ 任务失败，点击"查看详情"了解具体原因
                    </div>
                ` : ''}
            </div>
        `;
    }).join('');

    videoTasksContainer.innerHTML = tasksHtml;
}

function getTaskStatusIcon(status) {
    switch (status) {
        case 'pending': return '⏳';
        case 'processing': return '🔄';
        case 'completed': return '✅';
        case 'failed': return '❌';
        default: return '❓';
    }
}

function getTaskStatusText(status) {
    switch (status) {
        case 'pending': return '等待中';
        case 'processing': return '处理中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        default: return '未知';
    }
}

function downloadVideo(videoPath) {
    const filename = videoPath.split('/').pop();
    const downloadUrl = `/api/video-file/${encodeURIComponent(filename)}`;

    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

async function viewTaskDetails(taskId) {
    try {
        console.log('查看任务详情:', taskId);

        const response = await fetch(`/api/video-task-details/${taskId}`);
        const data = await response.json();

        if (data.success) {
            const details = data.details;
            const task = details.task;
            const segments = details.segments;
            const stats = details.segment_stats;
            const progress = details.progress;

            let detailsHtml = `
                <div class="task-details-modal">
                    <div class="modal-header">
                        <h3>任务详情 - ${task.task_name}</h3>
                        <button onclick="closeTaskDetails()" class="close-btn">×</button>
                    </div>
                    <div class="modal-content">
                        <div class="task-summary">
                            <h4>任务概览</h4>
                            <p><strong>状态:</strong> ${getTaskStatusText(task.status)}</p>
                            <p><strong>创建时间:</strong> ${new Date(task.created_time + 'Z').toLocaleString('zh-CN', {
                                year: 'numeric',
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit',
                                second: '2-digit',
                                timeZone: 'Asia/Shanghai'
                            })}</p>
                            <p><strong>总进度:</strong> ${progress.completed}/${progress.total} (${progress.percentage}%)</p>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${progress.percentage}%"></div>
                            </div>
                        </div>

                        <div class="segments-details">
                            <h4>视频片段详情</h4>
                            <div class="segments-grid">
            `;

            segments.forEach((segment, index) => {
                const statusIcon = getSegmentStatusIcon(segment.status);
                detailsHtml += `
                    <div class="segment-card ${segment.status}">
                        <div class="segment-header">
                            <span>片段 ${segment.segment_order}</span>
                            <span class="segment-status">${statusIcon} ${getSegmentStatusText(segment.status)}</span>
                        </div>
                        <div class="segment-info">
                            <p>首帧ID: ${segment.first_image_id}</p>
                            <p>尾帧ID: ${segment.last_image_id}</p>
                            ${segment.video_path ? `<p>视频路径: ${segment.video_path}</p>` : ''}
                            ${segment.status === 'failed' ? '<p class="error-text">生成失败，请检查图片文件或重试</p>' : ''}
                        </div>
                    </div>
                `;
            });

            detailsHtml += `
                            </div>
                        </div>

                        <div class="modal-actions">
                            <button class="btn btn-primary" onclick="refreshTaskDetails(${taskId})">🔄 刷新</button>
                            <button class="btn btn-secondary" onclick="closeTaskDetails()">关闭</button>
                        </div>
                    </div>
                </div>
                <div class="modal-overlay" onclick="closeTaskDetails()"></div>
            `;

            // 显示详情模态框
            const modalContainer = document.createElement('div');
            modalContainer.id = 'task-details-modal';
            modalContainer.innerHTML = detailsHtml;
            document.body.appendChild(modalContainer);

        } else {
            showNotification('获取任务详情失败: ' + data.error, 'error');
        }

    } catch (error) {
        console.error('查看任务详情失败:', error);
        showNotification('查看任务详情失败: ' + error.message, 'error');
    }
}

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // 根据类型设置不同的显示时间
    const displayTime = type === 'error' ? 8000 : 3000; // 错误消息显示8秒，其他3秒

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, displayTime);
}

function closeTaskDetails() {
    const modal = document.getElementById('task-details-modal');
    if (modal) {
        modal.remove();
    }
}

async function refreshTaskDetails(taskId) {
    closeTaskDetails();
    await viewTaskDetails(taskId);
}

async function refreshTaskStatus(taskId) {
    try {
        console.log('刷新任务状态:', taskId);

        // 重新加载所有任务
        await loadVideoTasks();

        showNotification('任务状态已刷新', 'success');

    } catch (error) {
        console.error('刷新任务状态失败:', error);
        showNotification('刷新任务状态失败: ' + error.message, 'error');
    }
}

function getSegmentStatusIcon(status) {
    switch (status) {
        case 'pending': return '⏳';
        case 'processing': return '🔄';
        case 'completed': return '✅';
        case 'failed': return '❌';
        default: return '❓';
    }
}

function getSegmentStatusText(status) {
    switch (status) {
        case 'pending': return '等待中';
        case 'processing': return '处理中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        default: return '未知';
    }
}
