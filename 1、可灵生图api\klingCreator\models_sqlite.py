#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQLite数据库模型
"""

import sqlite3
import json
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class DatabaseManager:
    """SQLite数据库管理器"""
    
    def __init__(self, db_path='kling_ai.db'):
        self.db_path = db_path
        self.connection = None
        
    def connect(self):
        """连接数据库"""
        try:
            self.connection = sqlite3.connect(self.db_path, check_same_thread=False)
            self.connection.row_factory = sqlite3.Row  # 使结果可以像字典一样访问
            self.init_tables()
            return True
        except Exception as e:
            print(f"数据库连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开数据库连接"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def execute_query(self, sql: str, params: tuple = None) -> List[Dict]:
        """执行查询"""
        if not self.connection:
            if not self.connect():
                return []
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(sql, params or ())
            rows = cursor.fetchall()
            return [dict(row) for row in rows]
        except Exception as e:
            print(f"查询执行失败: {e}")
            return []
    
    def execute_update(self, sql: str, params: tuple = None) -> int:
        """执行更新/插入/删除"""
        if not self.connection:
            if not self.connect():
                return 0
        
        try:
            cursor = self.connection.cursor()
            cursor.execute(sql, params or ())
            self.connection.commit()
            return cursor.lastrowid if 'INSERT' in sql.upper() else cursor.rowcount
        except Exception as e:
            print(f"更新执行失败: {e}")
            return 0
    
    def init_tables(self):
        """初始化数据表"""
        tables = [
            # 生成记录表
            '''
            CREATE TABLE IF NOT EXISTS generation_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_image_path TEXT NOT NULL,
                original_image_name TEXT NOT NULL,
                prompt TEXT NOT NULL,
                ai_provider TEXT NOT NULL DEFAULT 'kling',
                style_name TEXT NOT NULL,
                status TEXT NOT NULL DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''',
            
            # 生成图片表
            '''
            CREATE TABLE IF NOT EXISTS generated_images (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                record_id INTEGER NOT NULL,
                image_path TEXT NOT NULL,
                image_name TEXT NOT NULL,
                image_url TEXT,
                file_size INTEGER,
                width INTEGER,
                height INTEGER,
                likes INTEGER DEFAULT 0,
                dislikes INTEGER DEFAULT 0,
                is_approved INTEGER DEFAULT NULL,
                approved_by TEXT,
                approved_at TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (record_id) REFERENCES generation_records(id) ON DELETE CASCADE
            )
            ''',
            
            # 用户操作表
            '''
            CREATE TABLE IF NOT EXISTS user_actions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                image_id INTEGER NOT NULL,
                action_type TEXT NOT NULL,
                user_ip TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (image_id) REFERENCES generated_images(id) ON DELETE CASCADE,
                UNIQUE(image_id, user_ip, action_type)
            )
            ''',
            
            # 系统配置表
            '''
            CREATE TABLE IF NOT EXISTS system_config (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_key TEXT NOT NULL UNIQUE,
                config_value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            '''
        ]
        
        for table_sql in tables:
            try:
                self.connection.execute(table_sql)
            except Exception as e:
                print(f"创建表失败: {e}")
        
        # 插入默认配置
        self.insert_default_config()
    
    def insert_default_config(self):
        """插入默认配置"""
        default_configs = [
            ('default_ai_provider', 'kling', '默认AI提供商'),
            ('max_daily_generations', '100', '每日最大生成数量'),
            ('auto_approve', '0', '是否自动审核通过'),
            ('storage_path', './web_output', '图片存储路径')
        ]
        
        for key, value, desc in default_configs:
            try:
                self.connection.execute(
                    "INSERT OR IGNORE INTO system_config (config_key, config_value, description) VALUES (?, ?, ?)",
                    (key, value, desc)
                )
            except Exception as e:
                print(f"插入默认配置失败: {e}")
        
        self.connection.commit()

class GenerationRecord:
    """生成记录模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def create_record(self, original_image_path: str, original_image_name: str, 
                     prompt: str, ai_provider: str, style_name: str) -> int:
        """创建生成记录"""
        sql = """
        INSERT INTO generation_records 
        (original_image_path, original_image_name, prompt, ai_provider, style_name, status)
        VALUES (?, ?, ?, ?, ?, 'pending')
        """
        return self.db.execute_update(sql, (original_image_path, original_image_name, 
                                          prompt, ai_provider, style_name))
    
    def update_status(self, record_id: int, status: str) -> bool:
        """更新生成状态"""
        sql = "UPDATE generation_records SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?"
        return self.db.execute_update(sql, (status, record_id)) > 0
    
    def get_record(self, record_id: int) -> Optional[Dict]:
        """获取生成记录"""
        sql = "SELECT * FROM generation_records WHERE id = ?"
        results = self.db.execute_query(sql, (record_id,))
        return results[0] if results else None
    
    def get_recent_records(self, limit: int = 50) -> List[Dict]:
        """获取最近的生成记录"""
        sql = """
        SELECT * FROM generation_records 
        ORDER BY created_at DESC 
        LIMIT ?
        """
        return self.db.execute_query(sql, (limit,))

class GeneratedImage:
    """生成图片模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def add_image(self, record_id: int, image_path: str, image_name: str, 
                  image_url: str = None, file_size: int = None, 
                  width: int = None, height: int = None) -> int:
        """添加生成的图片"""
        sql = """
        INSERT INTO generated_images 
        (record_id, image_path, image_name, image_url, file_size, width, height)
        VALUES (?, ?, ?, ?, ?, ?, ?)
        """
        return self.db.execute_update(sql, (record_id, image_path, image_name, 
                                          image_url, file_size, width, height))
    
    def update_likes(self, image_id: int, likes: int, dislikes: int) -> bool:
        """更新点赞点踩数"""
        sql = "UPDATE generated_images SET likes = ?, dislikes = ? WHERE id = ?"
        return self.db.execute_update(sql, (likes, dislikes, image_id)) > 0
    
    def approve_image(self, image_id: int, is_approved: bool, approved_by: str) -> bool:
        """审核图片"""
        sql = """
        UPDATE generated_images 
        SET is_approved = ?, approved_by = ?, approved_at = CURRENT_TIMESTAMP 
        WHERE id = ?
        """
        return self.db.execute_update(sql, (1 if is_approved else 0, approved_by, image_id)) > 0
    
    def get_images_by_record(self, record_id: int) -> List[Dict]:
        """获取记录的所有图片"""
        sql = "SELECT * FROM generated_images WHERE record_id = ? ORDER BY created_at"
        return self.db.execute_query(sql, (record_id,))
    
    def get_pending_approval(self) -> List[Dict]:
        """获取待审核的图片"""
        sql = """
        SELECT gi.*, gr.original_image_name, gr.prompt, gr.style_name
        FROM generated_images gi
        JOIN generation_records gr ON gi.record_id = gr.id
        WHERE gi.is_approved IS NULL
        ORDER BY gi.created_at DESC
        """
        return self.db.execute_query(sql)
    
    def get_approved_images(self, limit: int = 100) -> List[Dict]:
        """获取已审核通过的图片"""
        sql = """
        SELECT gi.*, gr.original_image_name, gr.prompt, gr.style_name
        FROM generated_images gi
        JOIN generation_records gr ON gi.record_id = gr.id
        WHERE gi.is_approved = 1
        ORDER BY gi.created_at DESC
        LIMIT ?
        """
        return self.db.execute_query(sql, (limit,))

class UserAction:
    """用户操作模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def record_action(self, image_id: int, action_type: str, user_ip: str, user_agent: str = None) -> bool:
        """记录用户操作"""
        sql = """
        INSERT OR REPLACE INTO user_actions (image_id, action_type, user_ip, user_agent)
        VALUES (?, ?, ?, ?)
        """
        return self.db.execute_update(sql, (image_id, action_type, user_ip, user_agent)) > 0
    
    def get_user_action(self, image_id: int, user_ip: str, action_type: str) -> Optional[Dict]:
        """获取用户操作记录"""
        sql = """
        SELECT * FROM user_actions 
        WHERE image_id = ? AND user_ip = ? AND action_type = ?
        """
        results = self.db.execute_query(sql, (image_id, user_ip, action_type))
        return results[0] if results else None

class SystemConfig:
    """系统配置模型"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
    
    def get_config(self, key: str, default_value: str = None) -> str:
        """获取配置值"""
        sql = "SELECT config_value FROM system_config WHERE config_key = ?"
        results = self.db.execute_query(sql, (key,))
        return results[0]['config_value'] if results else default_value
    
    def set_config(self, key: str, value: str, description: str = None) -> bool:
        """设置配置值"""
        sql = """
        INSERT OR REPLACE INTO system_config (config_key, config_value, description)
        VALUES (?, ?, ?)
        """
        return self.db.execute_update(sql, (key, value, description)) > 0

# SQLite数据库管理器实例
db_manager = DatabaseManager()

# 模型实例
generation_record = GenerationRecord(db_manager)
generated_image = GeneratedImage(db_manager)
user_action = UserAction(db_manager)
system_config = SystemConfig(db_manager)
