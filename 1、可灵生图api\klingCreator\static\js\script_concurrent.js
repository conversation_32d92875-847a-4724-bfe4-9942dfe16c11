// 并发生成版本的JavaScript

// 全局变量
let currentFilename = '';
let originalFilename = '';
let selectedPrompts = [];
let generationTasks = []; // 存储生成任务

// DOM元素
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const uploadedImageContainer = document.getElementById('uploadedImage');
const previewImage = document.getElementById('previewImage');
const promptCheckboxes = document.querySelectorAll('input[name="prompts"]');
const generateBtn = document.getElementById('generateBtn');
const progressInfo = document.getElementById('progressInfo');
const balanceInfo = document.getElementById('balanceInfo');
const resultsSection = document.getElementById('resultsSection');
const resultsGrid = document.getElementById('resultsGrid');
const historyGrid = document.getElementById('historyGrid');
const refreshHistoryBtn = document.getElementById('refreshHistory');
const clearHistoryBtn = document.getElementById('clearHistory');

// 模态框元素
const imageModal = document.getElementById('imageModal');
const modalClose = document.getElementById('modalClose');
const modalImage = document.getElementById('modalImage');
const modalTitle = document.getElementById('modalTitle');
const modalDownload = document.getElementById('modalDownload');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadBalance();
    loadHistory();
    
    // 定期检查生成状态
    setInterval(checkGenerationStatus, 3000);
});

function initializeEventListeners() {
    // 调试信息
    console.log('初始化事件监听器...');
    console.log('fileInput:', fileInput);
    console.log('uploadArea:', uploadArea);
    console.log('uploadedImageContainer:', uploadedImageContainer);
    console.log('previewImage:', previewImage);

    // 文件上传
    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
        console.log('✅ fileInput change事件已绑定');
    } else {
        console.error('❌ fileInput元素未找到');
    }

    if (uploadArea) {
        uploadArea.addEventListener('click', () => {
            console.log('uploadArea被点击');
            fileInput.click();
        });
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('drop', handleDrop);
        console.log('✅ uploadArea事件已绑定');
    } else {
        console.error('❌ uploadArea元素未找到');
    }
    
    // 提示词选择
    promptCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedPrompts);
    });
    
    // 生成按钮
    generateBtn.addEventListener('click', generateImages);
    
    // 模态框
    modalClose.addEventListener('click', closeModal);
    imageModal.addEventListener('click', (e) => {
        if (e.target === imageModal) closeModal();
    });
    
    // 历史记录
    refreshHistoryBtn.addEventListener('click', loadHistory);
    clearHistoryBtn.addEventListener('click', clearHistoryDisplay);
}

// 文件处理函数
function handleFileSelect(e) {
    console.log('handleFileSelect被调用', e);
    const file = e.target.files[0];
    console.log('选择的文件:', file);
    if (file) {
        uploadFile(file);
    } else {
        console.log('没有选择文件');
    }
}

function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const file = e.dataTransfer.files[0];
    if (file) {
        uploadFile(file);
    }
}

// 上传文件
async function uploadFile(file) {
    console.log('uploadFile被调用，文件:', file);
    const formData = new FormData();
    formData.append('file', file);

    try {
        console.log('开始上传文件...');
        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });

        console.log('上传响应:', response);
        const data = await response.json();
        console.log('上传结果:', data);

        if (data.success) {
            currentFilename = data.filename;
            originalFilename = data.original_name;

            console.log('设置文件名:', currentFilename, originalFilename);

            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                console.log('文件读取完成，显示预览');
                previewImage.src = e.target.result;
                uploadedImageContainer.style.display = 'block';
                updateGenerateButton();
            };
            reader.readAsDataURL(file);

            console.log('文件上传成功:', data);
        } else {
            console.error('上传失败:', data.error);
            alert('上传失败: ' + data.error);
        }
    } catch (error) {
        console.error('上传异常:', error);
        alert('上传失败: ' + error.message);
    }
}

// 更新选中的提示词
function updateSelectedPrompts() {
    selectedPrompts = [];
    promptCheckboxes.forEach(checkbox => {
        if (checkbox.checked) {
            const template = checkbox.closest('.prompt-template');
            const name = template.querySelector('.prompt-name').textContent;
            const prompt = template.querySelector('.prompt-full').textContent;
            selectedPrompts.push({
                name: name,
                prompt: prompt,
                style_name: name
            });
        }
    });
    updateGenerateButton();
}

// 更新生成按钮状态
function updateGenerateButton() {
    const canGenerate = currentFilename && selectedPrompts.length > 0;
    generateBtn.disabled = !canGenerate;
    
    if (canGenerate) {
        generateBtn.innerHTML = `🎨 并发生成 ${selectedPrompts.length} 张图片`;
    } else {
        generateBtn.innerHTML = '🎨 开始生成';
    }
}

// 并发生成图片
async function generateImages() {
    if (!currentFilename || selectedPrompts.length === 0) {
        alert('请先上传图片并选择风格');
        return;
    }
    
    try {
        generateBtn.disabled = true;
        progressInfo.style.display = 'block';
        progressInfo.innerHTML = `
            <div class="progress-text">🚀 正在提交 ${selectedPrompts.length} 个并发生成任务...</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 10%"></div>
            </div>
        `;
        
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                filename: currentFilename,
                original_name: originalFilename,
                prompts: selectedPrompts,
                ai_provider: 'kling'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // 存储生成任务
            generationTasks = data.record_ids.map((recordId, index) => ({
                recordId: recordId,
                prompt: selectedPrompts[index],
                status: 'generating',
                result: null
            }));
            
            progressInfo.innerHTML = `
                <div class="progress-text">✅ 已提交 ${data.total_tasks} 个任务，正在并发生成中...</div>
                <div class="progress-detail">
                    <div>完成: <span id="completedCount">0</span>/${data.total_tasks}</div>
                    <div>进行中: <span id="processingCount">${data.total_tasks}</span></div>
                    <div>失败: <span id="failedCount">0</span></div>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
            `;
            
            // 显示结果区域
            resultsSection.style.display = 'block';
            resultsGrid.innerHTML = '<p style="text-align: center; color: #a0aec0; grid-column: 1 / -1;">正在并发生成中，请稍候...</p>';
            
        } else {
            throw new Error(data.error || '生成失败');
        }
    } catch (error) {
        console.error('生成失败:', error);
        alert('生成失败: ' + error.message);
        progressInfo.style.display = 'none';
        generateBtn.disabled = false;
    }
}

// 检查生成状态
async function checkGenerationStatus() {
    if (generationTasks.length === 0) return;
    
    let completed = 0;
    let failed = 0;
    let processing = 0;
    
    for (let task of generationTasks) {
        if (task.status === 'completed' || task.status === 'failed') {
            continue;
        }
        
        try {
            const response = await fetch(`/api/status/${task.recordId}`);
            const data = await response.json();
            
            if (data.record) {
                const status = data.record.status;
                
                if (status === 'completed' && data.images.length > 0) {
                    task.status = 'completed';
                    task.result = data.images[0];
                    completed++;
                } else if (status === 'failed') {
                    task.status = 'failed';
                    failed++;
                } else {
                    processing++;
                }
            }
        } catch (error) {
            console.error('检查状态失败:', error);
        }
    }
    
    // 更新进度显示
    updateProgressDisplay(completed, processing, failed);
    
    // 如果所有任务完成，显示结果
    if (completed + failed === generationTasks.length) {
        displayConcurrentResults();
        progressInfo.style.display = 'none';
        generateBtn.disabled = false;
        loadBalance(); // 刷新余额
        
        // 刷新历史记录
        setTimeout(() => {
            loadHistory();
        }, 1000);
        
        // 清空任务列表
        generationTasks = [];
    }
}

// 更新进度显示
function updateProgressDisplay(completed, processing, failed) {
    const total = generationTasks.length;
    const progress = ((completed + failed) / total) * 100;
    
    document.getElementById('completedCount').textContent = completed;
    document.getElementById('processingCount').textContent = processing;
    document.getElementById('failedCount').textContent = failed;
    document.getElementById('progressFill').style.width = `${progress}%`;
}

// 显示并发生成结果
function displayConcurrentResults() {
    resultsGrid.innerHTML = '';
    
    generationTasks.forEach((task, index) => {
        const resultDiv = document.createElement('div');
        resultDiv.className = `result-item ${task.status === 'completed' ? 'result-success' : 'result-error'}`;
        
        if (task.status === 'completed' && task.result) {
            resultDiv.innerHTML = `
                <div class="result-image">
                    <img src="/api/image/${task.result.image_name}" alt="生成的图片" 
                         onclick="openImageModal('/api/image/${task.result.image_name}', '${task.result.image_name}')">
                </div>
                <div class="result-info">
                    <div class="result-prompt">${task.prompt.name}</div>
                    <div class="result-filename">${task.result.image_name}</div>
                    <div class="result-actions">
                        <button class="btn-like" onclick="likeImage(${task.result.id}, this)">
                            👍 ${task.result.likes || 0}
                        </button>
                        <button class="btn-dislike" onclick="dislikeImage(${task.result.id}, this)">
                            👎 ${task.result.dislikes || 0}
                        </button>
                    </div>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="result-error-content">
                    <div class="error-icon">❌</div>
                    <div class="result-info">
                        <div class="result-prompt">${task.prompt.name}</div>
                        <div class="error-message">生成失败</div>
                    </div>
                </div>
            `;
        }
        
        resultsGrid.appendChild(resultDiv);
    });
}

// 其他函数保持不变...
async function loadBalance() {
    try {
        const response = await fetch('/api/balance');
        const data = await response.json();
        
        if (data.balance !== undefined) {
            balanceInfo.textContent = `💰 ${data.balance}`;
        } else {
            balanceInfo.textContent = '💰 --';
        }
    } catch (error) {
        console.error('获取余额失败:', error);
        balanceInfo.textContent = '💰 --';
    }
}

async function loadHistory() {
    try {
        console.log('加载历史记录...');
        const response = await fetch('/api/history?limit=20');
        const data = await response.json();
        
        if (data.records && data.records.length > 0) {
            displayHistory(data.records);
        } else {
            historyGrid.innerHTML = '<p style="text-align: center; color: #a0aec0; grid-column: 1 / -1;">暂无生成历史</p>';
        }
    } catch (error) {
        console.error('加载历史记录异常:', error);
        historyGrid.innerHTML = '<p style="text-align: center; color: #a0aec0; grid-column: 1 / -1;">加载历史记录失败</p>';
    }
}

function displayHistory(records) {
    historyGrid.innerHTML = '';
    
    records.forEach(record => {
        if (record.images && record.images.length > 0) {
            const historyItem = createHistoryItem(record);
            historyGrid.appendChild(historyItem);
        }
    });
}

function createHistoryItem(record) {
    const item = document.createElement('div');
    item.className = 'history-item';
    
    const header = document.createElement('div');
    header.className = 'history-header';
    
    const title = document.createElement('div');
    title.className = 'history-title';
    title.textContent = `${record.style_name} - ${record.original_image_name}`;
    
    const time = document.createElement('div');
    time.className = 'history-time';
    time.textContent = new Date(record.created_at).toLocaleString();
    
    header.appendChild(title);
    header.appendChild(time);
    
    const imagesContainer = document.createElement('div');
    imagesContainer.className = 'history-images';
    
    record.images.forEach(image => {
        const imageDiv = createHistoryImage(image);
        imagesContainer.appendChild(imageDiv);
    });
    
    item.appendChild(header);
    item.appendChild(imagesContainer);
    
    return item;
}

function createHistoryImage(image) {
    const imageDiv = document.createElement('div');
    imageDiv.className = 'history-image';
    
    const img = document.createElement('img');
    img.src = `/api/image/${image.image_name}`;
    img.alt = '生成的图片';
    img.onclick = () => openImageModal(`/api/image/${image.image_name}`, image.image_name);
    
    const actions = document.createElement('div');
    actions.className = 'image-actions';
    
    const likeBtn = document.createElement('button');
    likeBtn.className = 'btn-like';
    likeBtn.innerHTML = `👍 ${image.likes || 0}`;
    likeBtn.onclick = (e) => {
        e.stopPropagation();
        likeImage(image.id, likeBtn);
    };
    
    const dislikeBtn = document.createElement('button');
    dislikeBtn.className = 'btn-dislike';
    dislikeBtn.innerHTML = `👎 ${image.dislikes || 0}`;
    dislikeBtn.onclick = (e) => {
        e.stopPropagation();
        dislikeImage(image.id, dislikeBtn);
    };
    
    actions.appendChild(likeBtn);
    actions.appendChild(dislikeBtn);
    
    imageDiv.appendChild(img);
    imageDiv.appendChild(actions);
    
    return imageDiv;
}

async function likeImage(imageId, button) {
    try {
        const response = await fetch(`/api/like/${imageId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            button.innerHTML = `👍 ${data.likes}`;
            button.classList.add('active');
            
            const dislikeBtn = button.parentElement.querySelector('.btn-dislike');
            if (dislikeBtn) {
                dislikeBtn.innerHTML = `👎 ${data.dislikes}`;
                dislikeBtn.classList.remove('active');
            }
        }
    } catch (error) {
        console.error('点赞异常:', error);
    }
}

async function dislikeImage(imageId, button) {
    try {
        const response = await fetch(`/api/dislike/${imageId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            button.innerHTML = `👎 ${data.dislikes}`;
            button.classList.add('active');
            
            const likeBtn = button.parentElement.querySelector('.btn-like');
            if (likeBtn) {
                likeBtn.innerHTML = `👍 ${data.likes}`;
                likeBtn.classList.remove('active');
            }
        }
    } catch (error) {
        console.error('点踩异常:', error);
    }
}

function clearHistoryDisplay() {
    historyGrid.innerHTML = '<p style="text-align: center; color: #a0aec0; grid-column: 1 / -1;">历史记录已清空显示</p>';
}

function openImageModal(imageSrc, filename) {
    modalImage.src = imageSrc;
    modalTitle.textContent = filename;
    modalDownload.onclick = () => downloadImage(filename);
    imageModal.style.display = 'flex';
}

function closeModal() {
    imageModal.style.display = 'none';
}

function downloadImage(filename) {
    const link = document.createElement('a');
    link.href = `/api/image/${filename}`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function togglePrompt(templateId) {
    const template = document.getElementById(templateId);
    const full = template.querySelector('.prompt-full');
    const btn = template.querySelector('.btn-expand');
    
    if (full.style.display === 'none') {
        full.style.display = 'block';
        btn.innerHTML = '▲ 收起';
    } else {
        full.style.display = 'none';
        btn.innerHTML = '▼ 展开';
    }
}
