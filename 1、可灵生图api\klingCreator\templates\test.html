<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>上传测试</title>
</head>
<body>
    <h1>文件上传测试</h1>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <input type="file" id="fileInput" accept="image/*">
        <button type="button" onclick="uploadFile()">上传</button>
    </form>
    
    <div id="result"></div>
    
    <script>
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('请选择文件');
                return;
            }
            
            console.log('选择的文件:', file);
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                console.log('开始上传...');
                
                const response = await fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                });
                
                console.log('响应状态:', response.status);
                
                const data = await response.json();
                console.log('响应数据:', data);
                
                document.getElementById('result').innerHTML = 
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
                    
            } catch (error) {
                console.error('错误:', error);
                document.getElementById('result').innerHTML = 
                    '<p style="color: red;">错误: ' + error.message + '</p>';
            }
        }
    </script>
</body>
</html>
