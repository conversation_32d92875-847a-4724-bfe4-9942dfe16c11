// 画廊页面JavaScript - 修复版本
let allUploads = [];
let currentSort = 'latest-generated';
let currentSearch = '';
let currentFilter = 'all';

// DOM元素
const galleryGrid = document.getElementById('gallery-grid');
const searchInput = document.getElementById('search-input');
const sortSelect = document.getElementById('sort-select');
const filterSelect = document.getElementById('filter-select');
const loading = document.getElementById('loading');
const emptyState = document.getElementById('empty-state');

// 统计元素
const totalUploadsEl = document.getElementById('total-uploads');
const totalGeneratedEl = document.getElementById('total-generated');
const totalLikesEl = document.getElementById('total-likes');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('画廊页面初始化...');
    initializeEventListeners();
    loadGalleryData();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 设置默认排序选择器值
    sortSelect.value = currentSort;

    // 搜索输入
    searchInput.addEventListener('input', function() {
        currentSearch = this.value.toLowerCase();
        filterAndDisplayUploads();
    });

    // 排序选择
    sortSelect.addEventListener('change', function() {
        currentSort = this.value;
        filterAndDisplayUploads();
    });

    // 筛选选择
    filterSelect.addEventListener('change', function() {
        currentFilter = this.value;
        filterAndDisplayUploads();
    });


}

// 加载画廊数据
async function loadGalleryData() {
    try {
        showLoading();
        
        console.log('开始加载画廊数据...');
        const response = await fetch('/api/gallery');
        console.log('API响应状态:', response.status);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('画廊数据:', data);
        
        if (data.success) {
            allUploads = data.uploads || [];
            console.log('加载的上传图片数量:', allUploads.length);
            updateStatistics(data.statistics || {});
            filterAndDisplayUploads();
        } else {
            throw new Error(data.error || '加载数据失败');
        }
        
    } catch (error) {
        console.error('加载画廊数据失败:', error);
        showError('加载数据失败: ' + error.message);
    } finally {
        hideLoading();
    }
}

// 筛选和显示上传图片
function filterAndDisplayUploads() {
    let filteredUploads = [...allUploads];

    // 搜索筛选
    if (currentSearch) {
        filteredUploads = filteredUploads.filter(upload =>
            upload.filename.toLowerCase().includes(currentSearch)
        );
    }

    // 状态筛选
    if (currentFilter !== 'all') {
        filteredUploads = filteredUploads.filter(upload => {
            switch (currentFilter) {
                case 'favorited':
                    return (upload.total_likes || 0) > 0;
                case 'marked':
                    return (upload.total_dislikes || 0) > 0;
                case 'unmarked':
                    return (upload.total_likes || 0) === 0 && (upload.total_dislikes || 0) === 0;
                default:
                    return true;
            }
        });
    }
    
    // 排序
    filteredUploads.sort((a, b) => {
        switch (currentSort) {
            case 'newest':
                return new Date(b.upload_time) - new Date(a.upload_time);
            case 'oldest':
                return new Date(a.upload_time) - new Date(b.upload_time);
            case 'latest-generated':
                // 按最新生成时间排序
                const timeA = a.latest_generation_time ? new Date(a.latest_generation_time) : new Date(0);
                const timeB = b.latest_generation_time ? new Date(b.latest_generation_time) : new Date(0);
                return timeB - timeA;
            case 'most-generated':
                return (b.generated_count || 0) - (a.generated_count || 0);
            case 'most-liked':
                return (b.total_likes || 0) - (a.total_likes || 0);
            default:
                return 0;
        }
    });
    
    displayUploads(filteredUploads);
}

// 显示上传图片
function displayUploads(uploads) {
    if (uploads.length === 0) {
        showEmptyState();
        return;
    }
    
    hideEmptyState();
    
    const html = uploads.map(upload => createUploadCard(upload)).join('');
    galleryGrid.innerHTML = html;
}

// 创建上传图片卡片
function createUploadCard(upload) {
    // 处理UTC时间转换为北京时间
    const utcUploadDate = new Date(upload.upload_time + 'Z'); // 确保被识别为UTC时间
    const uploadTime = utcUploadDate.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false,
        timeZone: 'Asia/Shanghai'
    });
    
    return `
        <div class="gallery-card" onclick="goToDetails('${upload.id}', '${upload.filename}')" style="cursor: pointer;">
            <div class="image-container">
                <img src="/api/upload/${encodeURIComponent(upload.filename)}" 
                     alt="${upload.filename}" 
                     class="gallery-image"
                     onerror="handleImageError(this)">
            </div>
            <div class="card-info">
                <h3 class="card-title">${upload.filename}</h3>
                <div class="card-stats">
                    <span class="stat">📅 ${uploadTime}</span>
                    <span class="stat">🎨 ${upload.generated_count || 0} 张生成</span>
                    <span class="stat">⭐ ${upload.total_likes || 0} 收藏</span>
                </div>
            </div>
        </div>
    `;
}

// 处理图片加载错误
function handleImageError(img) {
    // 防止无限循环，只处理一次
    if (img.dataset.errorHandled) return;
    img.dataset.errorHandled = 'true';
    
    // 替换为错误提示
    img.style.display = 'none';
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = 'display:flex;align-items:center;justify-content:center;height:200px;background:#f7fafc;color:#a0aec0;font-size:2rem;';
    errorDiv.innerHTML = '📷';
    img.parentElement.appendChild(errorDiv);
}

// 直接跳转到详情页面
function goToDetails(uploadId, filename) {
    // 直接跳转到详情页面，使用upload_id
    window.location.href = `/details/${uploadId}`;
}

// 更新统计信息
function updateStatistics(stats) {
    totalUploadsEl.textContent = stats.total_uploads || 0;
    totalGeneratedEl.textContent = stats.total_generated || 0;
    totalLikesEl.textContent = stats.total_likes || 0;
}

// 显示加载状态
function showLoading() {
    loading.style.display = 'flex';
    galleryGrid.style.display = 'none';
    emptyState.style.display = 'none';
}

// 隐藏加载状态
function hideLoading() {
    loading.style.display = 'none';
    galleryGrid.style.display = 'grid';
}

// 显示空状态
function showEmptyState() {
    galleryGrid.style.display = 'none';
    emptyState.style.display = 'flex';
}

// 隐藏空状态
function hideEmptyState() {
    emptyState.style.display = 'none';
    galleryGrid.style.display = 'grid';
}

// 显示错误
function showError(message) {
    galleryGrid.innerHTML = `
        <div style="grid-column: 1 / -1; text-align: center; padding: 2rem; color: #e53e3e;">
            <h3>❌ 加载失败</h3>
            <p>${message}</p>
            <button onclick="loadGalleryData()" class="btn btn-primary">🔄 重试</button>
        </div>
    `;
}
