/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 性能优化 */
img {
    will-change: transform;
}

.result-item {
    contain: layout style paint;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 头部样式 */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.header h1 {
    color: #4a5568;
    font-size: 2rem;
    font-weight: 600;
    margin: 0;
}

.header h1 i {
    color: #667eea;
    margin-right: 10px;
}

.nav-links {
    display: flex;
    gap: 1rem;
}

.nav-link {
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: #718096;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.balance-info {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    font-weight: 500;
}

.balance-info i {
    margin-right: 8px;
}

/* 积分不足提示框 */
.balance-warning {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border: 2px solid #ff6b6b;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    z-index: 10000;
    max-width: 400px;
    text-align: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.balance-warning-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
}

.balance-warning h3 {
    color: #ff6b6b;
    margin: 0 0 15px 0;
    font-size: 1.5em;
}

.balance-warning .balance-details {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    text-align: left;
}

.balance-warning .balance-item {
    display: flex;
    justify-content: space-between;
    margin: 8px 0;
    padding: 5px 0;
    border-bottom: 1px solid #e9ecef;
}

.balance-warning .balance-item:last-child {
    border-bottom: none;
    font-weight: bold;
    color: #ff6b6b;
}

.balance-warning .buttons {
    margin-top: 20px;
}

.balance-warning button {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1em;
    margin: 0 5px;
    transition: background 0.3s;
}

.balance-warning button:hover {
    background: #ff5252;
}

.balance-warning .btn-secondary {
    background: #6c757d;
}

.balance-warning .btn-secondary:hover {
    background: #5a6268;
}

/* 主要内容区域 */
.main-content {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

/* 通用section样式 */
section {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

section h2 {
    color: #4a5568;
    font-size: 1.5rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.step-number {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-weight: bold;
}

/* 上传区域样式 */
.upload-area {
    border: 3px dashed #cbd5e0;
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.upload-area:hover {
    border-color: #667eea;
    background: #edf2f7;
}

.upload-area.dragover {
    border-color: #667eea;
    background: #e6fffa;
}

.upload-icon {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 15px;
}

.upload-content p {
    font-size: 1.1rem;
    color: #4a5568;
    margin-bottom: 5px;
}

.upload-hint {
    font-size: 0.9rem;
    color: #a0aec0;
}

.uploaded-image {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 20px;
    background: #f7fafc;
    border-radius: 15px;
    border: 2px solid #e2e8f0;
}

.uploaded-image .image-container {
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.uploaded-image img {
    max-width: 100px;
    max-height: 100px;
    object-fit: contain;
    border-radius: 10px;
}

.image-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-remove {
    background: #e53e3e;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-remove:hover {
    background: #c53030;
    transform: scale(1.1);
}

/* 提示词卡片样式 */
.prompt-templates {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.prompt-card {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.prompt-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.prompt-card.selected {
    border-color: #667eea;
    background: #e6fffa;
}

.prompt-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.prompt-checkbox {
    margin-right: 10px;
    transform: scale(1.2);
}

.prompt-title {
    font-weight: 600;
    color: #4a5568;
    cursor: pointer;
    flex: 1;
}

.prompt-full {
    color: #4a5568;
    line-height: 1.6;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.btn-expand {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.btn-expand:hover {
    color: #5a67d8;
}



/* 生成按钮样式 */
.btn-generate {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
}

.btn-generate:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-generate:disabled {
    background: #a0aec0;
    cursor: not-allowed;
}

/* 进度条样式 */
.progress-info {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    color: #4a5568;
    font-weight: 500;
}

/* 结果头部样式 */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.btn-clear-results {
    padding: 0.5rem 1rem;
    background: #f56565;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-clear-results:hover {
    background: #e53e3e;
    transform: translateY(-1px);
}

.btn-clear-results:active {
    transform: translateY(0);
}

/* 结果网格样式 */
.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    justify-items: center;
}

.result-item {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    width: fit-content;
    max-width: 400px;
    min-width: 200px;
}

.result-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.result-item img {
    width: 100%;
    min-width: 200px;
    max-width: 400px;
    height: auto;
    max-height: 300px;
    object-fit: contain;
    cursor: pointer;
    border-radius: 8px 8px 0 0;
    background: #f8f9fa;
}

.result-info {
    padding: 15px;
    text-align: center;
}

.result-title {
    font-weight: 600;
    color: #4a5568;
    font-size: 0.95rem;
}



.result-error {
    background: #fed7d7;
    border: 2px solid #fc8181;
}

.result-error .result-info {
    color: #c53030;
}

/* 图片容器和覆盖按钮样式 */
.result-image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    min-height: 200px;
    background: #f8f9fa;
    border-radius: 8px;
    cursor: pointer;
}

.result-image-container .result-image {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: contain;
    border-radius: 8px;
    transition: transform 0.2s ease;
    display: block;
    background: #f8f9fa;
}

.result-image-container:hover .result-image {
    transform: scale(1.02);
}

.result-overlay-actions, .result-corner-actions {
    position: absolute;
    bottom: 6px;
    right: 6px;
    display: flex;
    gap: 4px;
    opacity: 1;
    /* 移除悬停显示效果，一直显示 */
}

/* 收藏和标记删除按钮 */
.btn-like, .btn-dislike, .btn-favorite, .btn-remove {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    backdrop-filter: blur(4px);
}

/* 收藏按钮样式 */
.btn-like:hover, .btn-favorite:hover {
    background: rgba(237, 137, 54, 0.9); /* 橙色，表示收藏 */
    transform: scale(1.05);
}

.btn-like.active, .btn-favorite.active {
    background: rgba(237, 137, 54, 0.9);
    transform: scale(1.05);
}

/* 标记删除按钮样式 */
.btn-dislike:hover, .btn-remove:hover {
    background: rgba(229, 62, 62, 0.9);
    transform: scale(1.05);
}

.btn-dislike.active, .btn-remove.active {
    background: rgba(229, 62, 62, 0.9);
    transform: scale(1.05);
}

/* 兼容旧版样式 */
.result-actions {
    display: flex;
    gap: 8px;
    margin-top: 10px;
    justify-content: center;
}

/* 并发生成相关样式 */
.result-pending {
    border: 2px dashed #cbd5e0;
    background: #f8f9fa;
}

.result-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    padding: 20px;
}

.loading-spinner {
    font-size: 3rem;
    animation: spin 2s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result-status {
    font-size: 0.9rem;
    color: #4a5568;
    margin: 5px 0;
    text-align: center;
}

.result-time {
    font-size: 0.8rem;
    color: #718096;
    text-align: center;
    font-weight: 500;
}

.result-success {
    border: 2px solid #48bb78;
    background: #f0fff4;
}

.result-error {
    border: 2px solid #e53e3e;
    background: #fef5e7;
}

.result-error-content {
    display: flex;
    align-items: center;
    padding: 20px;
    text-align: center;
}

.error-icon {
    font-size: 2rem;
    margin-right: 15px;
}

.error-message {
    font-size: 0.8rem;
    color: #e53e3e;
    margin-top: 5px;
}

.progress-note {
    font-size: 0.9rem;
    color: #718096;
    margin-top: 5px;
    text-align: center;
}

/* 图片查看模态框样式 */
.image-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.image-modal-content {
    position: relative;
    max-width: 95vw;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.image-modal-close {
    position: absolute;
    top: -40px;
    right: 0;
    font-size: 2.5rem;
    color: white;
    cursor: pointer;
    z-index: 1001;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.image-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

.image-modal img {
    max-width: 100%;
    max-height: 80vh;
    object-fit: contain;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.image-modal-info {
    margin-top: 15px;
    text-align: center;
}

.image-modal-info h3 {
    color: white;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.btn-download {
    background: linear-gradient(135deg, #48bb78, #38a169);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-download:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(72, 187, 120, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .prompt-templates {
        grid-template-columns: 1fr;
    }

    .results-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .result-item {
        max-width: 100%;
        min-width: auto;
    }

    .result-item img {
        min-width: auto;
        max-width: 100%;
    }

    section {
        padding: 20px;
    }
}
