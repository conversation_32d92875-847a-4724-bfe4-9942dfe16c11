#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接现有Chrome浏览器并上传首尾帧生成视频
保持原有的登录状态
"""

import asyncio
import os
import sys
import subprocess
import time
from pathlib import Path
from playwright.async_api import async_playwright


async def connect_and_upload_video(first_frame_path: str, last_frame_path: str, prompt: str = ""):
    """
    连接现有浏览器并上传首尾帧生成视频
    
    Args:
        first_frame_path: 首帧图片路径
        last_frame_path: 尾帧图片路径
        prompt: 提示词
    """
    
    print("🎬 连接现有Chrome浏览器生成视频")
    print("=" * 50)
    print(f"📸 首帧: {Path(first_frame_path).name}")
    print(f"📸 尾帧: {Path(last_frame_path).name}")
    print(f"💭 提示词: {prompt}")
    print()
    
    # 验证文件存在
    if not os.path.exists(first_frame_path):
        print(f"❌ 首帧图片不存在: {first_frame_path}")
        return False
    
    if not os.path.exists(last_frame_path):
        print(f"❌ 尾帧图片不存在: {last_frame_path}")
        return False
    
    # 启动Chrome调试模式（如果还没启动）
    debug_port = 9222
    chrome_path = r"C:\Program Files\Google\Chrome\Application\chrome.exe"
    
    print("🔍 检查Chrome调试端口...")
    
    # 检查调试端口是否已开启
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("🚀 启动Chrome调试模式...")
        # 启动Chrome调试模式
        subprocess.Popen([
            chrome_path,
            f"--remote-debugging-port={debug_port}",
            "--user-data-dir=chrome_debug_profile",
            "https://jimeng.jianying.com/ai-tool/generate?type=video"
        ])
        
        # 等待Chrome启动
        print("⏳ 等待Chrome启动...")
        for i in range(30):
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', debug_port))
            sock.close()
            if result == 0:
                break
            time.sleep(1)
        
        if result != 0:
            print("❌ Chrome调试模式启动失败")
            return False
        
        print("✅ Chrome调试模式已启动")
        time.sleep(3)
    else:
        print("✅ Chrome调试端口已开启")
    
    async with async_playwright() as p:
        try:
            print("🔗 连接到现有Chrome浏览器...")
            
            # 连接到现有的Chrome实例
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            
            # 获取现有的上下文和页面
            contexts = browser.contexts
            if not contexts:
                print("❌ 未找到浏览器上下文")
                return False
            
            context = contexts[0]
            pages = context.pages
            
            # 查找即梦AI页面或创建新页面
            jimeng_page = None
            for page in pages:
                url = page.url
                if "jimeng.jianying.com" in url:
                    jimeng_page = page
                    print(f"✅ 找到即梦AI页面: {url}")
                    break
            
            if not jimeng_page:
                print("🌐 在新标签页打开即梦AI...")
                jimeng_page = await context.new_page()
                await jimeng_page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
                await jimeng_page.wait_for_load_state('networkidle')
            else:
                # 确保在正确的页面
                if "type=video" not in jimeng_page.url:
                    await jimeng_page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
                    await jimeng_page.wait_for_load_state('networkidle')
            
            # 等待页面加载
            await asyncio.sleep(3)
            print("✅ 即梦AI页面准备就绪")
            
            # 执行上传和生成操作
            success = await perform_upload_and_generate(jimeng_page, first_frame_path, last_frame_path, prompt)
            
            if success:
                print("✅ 视频生成操作完成！")
                print("💡 请在浏览器中查看生成进度")
                return True
            else:
                print("❌ 视频生成操作失败")
                return False
                
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False


async def perform_upload_and_generate(page, first_frame_path, last_frame_path, prompt):
    """执行上传和生成操作"""
    try:
        # 查找文件上传元素
        print("🔍 查找文件上传元素...")
        
        # 等待页面元素加载
        await asyncio.sleep(2)
        
        file_inputs = await page.query_selector_all('input[type="file"]')
        print(f"找到 {len(file_inputs)} 个文件上传元素")
        
        if len(file_inputs) < 2:
            print("⚠️ 文件上传元素不足，尝试点击上传区域...")
            
            # 尝试点击上传区域来触发文件输入
            upload_selectors = [
                '.upload-area',
                '.upload-zone',
                '[class*="upload"]',
                'div[role="button"]',
                'button[class*="upload"]'
            ]
            
            for selector in upload_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        if await element.is_visible():
                            await element.click()
                            await asyncio.sleep(1)
                except:
                    continue
            
            # 重新查找文件输入
            await asyncio.sleep(2)
            file_inputs = await page.query_selector_all('input[type="file"]')
            print(f"点击后找到 {len(file_inputs)} 个文件上传元素")
        
        if len(file_inputs) >= 2:
            print("📤 开始上传图片...")
            
            # 上传首帧
            print("📤 上传首帧...")
            await file_inputs[0].set_input_files(first_frame_path)
            
            # 等待并处理保存弹窗
            await handle_save_popup(page, "首帧")
            
            # 上传尾帧
            print("📤 上传尾帧...")
            await file_inputs[1].set_input_files(last_frame_path)
            
            # 等待并处理保存弹窗
            await handle_save_popup(page, "尾帧")
            
            # 设置提示词
            if prompt:
                print("💭 设置提示词...")
                await set_prompt(page, prompt)
            
            # 设置10秒时长
            print("⏱️ 设置10秒时长...")
            await set_duration(page, 10)
            
            # 点击生成按钮
            print("🚀 点击生成按钮...")
            return await click_generate_button(page)
        else:
            print("❌ 未找到足够的文件上传元素")
            return False
            
    except Exception as e:
        print(f"❌ 上传操作失败: {e}")
        return False


async def handle_save_popup(page, frame_type):
    """处理保存弹窗"""
    print(f"⏳ 等待{frame_type}保存弹窗...")
    
    # 等待最多10秒寻找保存按钮
    for attempt in range(20):
        try:
            # 查找所有可见按钮
            buttons = await page.query_selector_all('button')
            
            for button in buttons:
                if await button.is_visible():
                    text = await button.inner_text()
                    text = text.strip().lower() if text else ""
                    
                    # 检查保存相关关键词
                    if any(keyword in text for keyword in ['保存', '确定', '确认', 'save', 'ok']):
                        await button.click()
                        print(f"✅ {frame_type}保存按钮已点击: {text}")
                        await asyncio.sleep(2)
                        return True
            
        except Exception as e:
            pass
        
        await asyncio.sleep(0.5)
    
    print(f"⚠️ 未找到{frame_type}保存按钮，继续...")
    await asyncio.sleep(3)
    return True


async def set_prompt(page, prompt):
    """设置提示词"""
    try:
        # 查找提示词输入框
        selectors = [
            'textarea[placeholder*="提示"]',
            'textarea[placeholder*="prompt"]',
            'input[placeholder*="提示"]',
            'textarea',
            '.prompt-input'
        ]
        
        for selector in selectors:
            element = await page.query_selector(selector)
            if element and await element.is_visible():
                await element.fill(prompt)
                print("✅ 提示词设置完成")
                return True
        
        print("⚠️ 未找到提示词输入框")
        return False
        
    except Exception as e:
        print(f"⚠️ 设置提示词失败: {e}")
        return False


async def set_duration(page, duration):
    """设置视频时长"""
    try:
        # 通过文本查找10秒按钮
        buttons = await page.query_selector_all('button')
        for button in buttons:
            try:
                if await button.is_visible():
                    text = await button.inner_text()
                    if text and ('10' in text and ('秒' in text or 's' in text.lower())):
                        await button.click()
                        print(f"✅ 找到并点击时长按钮: {text}")
                        return True
            except:
                continue
        
        print("⚠️ 未找到10秒时长设置，使用默认值")
        return True
        
    except Exception as e:
        print(f"⚠️ 设置时长失败: {e}")
        return True


async def click_generate_button(page):
    """点击生成按钮"""
    try:
        # 通过文本查找生成按钮
        buttons = await page.query_selector_all('button')
        for button in buttons:
            try:
                if await button.is_visible() and await button.is_enabled():
                    text = await button.inner_text()
                    if text and ('生成' in text or 'generate' in text.lower()):
                        await button.click()
                        print(f"✅ 找到并点击生成按钮: {text}")
                        await asyncio.sleep(3)
                        return True
            except:
                continue
        
        print("❌ 未找到可用的生成按钮")
        return False
        
    except Exception as e:
        print(f"❌ 点击生成按钮失败: {e}")
        return False


async def main():
    """主函数"""
    
    # 测试图片路径
    first_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座孤立在纯黑色空间中的蒸汽朋克机械堡垒_建筑内部的锅炉透过格栅.png"
    last_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (4).png"
    prompt = "从蒸汽朋克机械堡垒平滑过渡到生物发光植物建筑"
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) >= 3:
        first_frame = sys.argv[1]
        last_frame = sys.argv[2]
        prompt = sys.argv[3] if len(sys.argv) > 3 else ""
    
    success = await connect_and_upload_video(first_frame, last_frame, prompt)
    
    if success:
        print("\n🎉 操作完成！请在浏览器中查看生成进度")
    else:
        print("\n❌ 操作失败")


if __name__ == "__main__":
    asyncio.run(main())
