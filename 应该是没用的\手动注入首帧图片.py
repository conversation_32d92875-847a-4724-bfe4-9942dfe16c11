#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
即梦AI手动注入首帧图片 - 专门用于手动注入首帧图片的脚本
使用方法：
1. 确保Chrome浏览器已开启调试模式（端口9222）
# 方法1：直接命令行启动
chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug


# 方法2：完整路径启动
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir=chrome-debug

# 方法3：如果Chrome安装在其他位置
"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir=chrome-debug

🔍 验证调试模式是否开启
方法1：浏览器验证
在Chrome中访问：http://localhost:9222/json

如果显示JSON数据，说明调试模式已开启
如果无法访问，说明调试模式未开启

2. 打开即梦AI页面：https://jimeng.jianying.com/ai-tool/generate?type=video
3. 修改下面的图片路径为您要上传的图片
4. 运行此脚本
"""

import asyncio
import os
import sys
import base64
import mimetypes
from pathlib import Path
from playwright.async_api import async_playwright


def read_image_as_base64(image_path: str):
    """读取图片并转换为Base64"""
    try:
        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(image_path)
        if not mime_type or not mime_type.startswith('image/'):
            mime_type = 'image/png'
        
        # 读取文件
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 转换为Base64
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        return {
            "name": os.path.basename(image_path),
            "data": base64_data,
            "type": mime_type,
            "size": len(image_data)
        }
    except Exception as e:
        print(f"❌ 读取图片失败: {e}")
        return None


async def inject_first_frame():
    """注入首帧图片"""
    
    # 🔧 配置区域 - 请修改这里的图片路径
    # ==========================================
    first_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\保留黑色底背景_保留楼体轮廓_楼体的风格转换成赛博朋克风格.png"
    # ==========================================
    
    print("🖼️ 即梦AI手动注入首帧图片")
    print("=" * 50)
    print(f"📸 图片路径: {first_frame_path}")
    print(f"📸 图片名称: {Path(first_frame_path).name}")
    print()
    
    # 验证文件存在
    if not os.path.exists(first_frame_path):
        print(f"❌ 图片文件不存在: {first_frame_path}")
        print("💡 请检查路径是否正确，或修改脚本中的图片路径")
        return False
    
    print("✅ 图片文件验证通过")
    
    # 读取图片数据
    print("📤 读取图片数据...")
    image_data = read_image_as_base64(first_frame_path)
    if not image_data:
        return False
    
    print(f"✅ 图片数据读取成功: {image_data['size']} 字节")
    
    # 检查Chrome调试端口
    debug_port = 9222
    print("🔍 检查Chrome调试端口...")
    
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("❌ Chrome调试端口未开启")
        print("💡 请先启动Chrome调试模式：")
        print("   chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug")
        return False
    
    print("✅ Chrome调试端口已开启")
    
    async with async_playwright() as p:
        try:
            print("🔗 连接到Chrome浏览器...")
            
            # 连接到现有Chrome实例
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            
            # 获取所有上下文和页面
            contexts = browser.contexts
            if not contexts:
                print("❌ 未找到浏览器上下文")
                return False
            
            # 查找即梦AI页面
            jimeng_page = None
            for context in contexts:
                for page in context.pages:
                    if 'jimeng.jianying.com' in page.url:
                        jimeng_page = page
                        print(f"✅ 找到即梦AI页面: {page.url}")
                        break
                if jimeng_page:
                    break
            
            if not jimeng_page:
                print("❌ 未找到即梦AI页面")
                print("💡 请先在Chrome中打开即梦AI页面：")
                print("   https://jimeng.jianying.com/ai-tool/generate?type=video")
                return False
            
            # 执行注入
            success = await perform_injection(jimeng_page, image_data)
            
            if success:
                print("✅ 首帧图片注入成功！")
                print("🎉 请在浏览器中查看上传结果")
                return True
            else:
                print("❌ 首帧图片注入失败")
                return False
                
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False


async def perform_injection(page, image_data):
    """执行注入操作"""
    try:
        print("💉 开始注入首帧图片...")
        
        # 等待页面稳定
        await asyncio.sleep(2)
        
        # 步骤1: 注入JavaScript辅助函数
        print("🔧 步骤1: 注入JavaScript辅助函数...")
        
        await page.evaluate("""
            () => {
                console.log('💉 注入JavaScript辅助函数');
                
                // 创建文件对象的函数
                window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                    console.log('创建文件:', fileName, mimeType);
                    
                    try {
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        const file = new File([byteArray], fileName, { type: mimeType });
                        console.log('文件创建成功:', file.name, file.size, 'bytes');
                        return file;
                    } catch (error) {
                        console.error('创建文件失败:', error);
                        return null;
                    }
                };
                
                // 设置文件到input的函数
                window.setFileToInput = function(input, file) {
                    console.log('设置文件到input:', file.name);
                    
                    try {
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        input.files = dataTransfer.files;
                        
                        // 触发事件
                        const changeEvent = new Event('change', { bubbles: true });
                        input.dispatchEvent(changeEvent);
                        
                        const inputEvent = new Event('input', { bubbles: true });
                        input.dispatchEvent(inputEvent);
                        
                        console.log('文件设置成功，files.length:', input.files.length);
                        return true;
                    } catch (error) {
                        console.error('设置文件失败:', error);
                        return false;
                    }
                };
                
                console.log('✅ JavaScript辅助函数注入完成');
            }
        """)
        
        await asyncio.sleep(1)
        print("✅ JavaScript辅助函数注入完成")
        
        # 步骤2: 检查页面状态
        print("🔍 步骤2: 检查页面状态...")
        
        page_state = await page.evaluate("""
            () => {
                console.log('🔍 检查页面状态');
                
                const state = {
                    fileInputs: 0,
                    visibleInputs: 0,
                    blobImages: 0,
                    totalImages: 0
                };
                
                // 检查文件输入
                const inputs = document.querySelectorAll('input[type="file"]');
                state.fileInputs = inputs.length;
                
                inputs.forEach(input => {
                    if (input.offsetParent !== null) {
                        state.visibleInputs++;
                    }
                });
                
                // 检查图片
                state.totalImages = document.querySelectorAll('img').length;
                state.blobImages = document.querySelectorAll('img[src*="blob:"]').length;
                
                console.log('页面状态:', state);
                return state;
            }
        """)
        
        print(f"📊 页面状态:")
        print(f"   文件输入: {page_state['fileInputs']} 个")
        print(f"   可见输入: {page_state['visibleInputs']} 个")
        print(f"   总图片: {page_state['totalImages']} 个")
        print(f"   预览图片: {page_state['blobImages']} 个")
        
        if page_state['fileInputs'] == 0:
            print("❌ 页面上没有文件输入元素")
            return False
        
        # 步骤3: 执行图片注入
        print("💉 步骤3: 执行图片注入...")
        
        inject_result = await page.evaluate(f"""
            () => {{
                console.log('💉 开始注入图片');
                
                try {{
                    // 查找文件输入元素
                    const inputs = document.querySelectorAll('input[type="file"]');
                    if (inputs.length === 0) {{
                        console.error('未找到文件输入元素');
                        return false;
                    }}
                    
                    const firstInput = inputs[0];
                    console.log('找到文件输入元素:', firstInput);
                    
                    // 强制显示元素
                    firstInput.style.display = 'block !important';
                    firstInput.style.visibility = 'visible !important';
                    firstInput.style.opacity = '1 !important';
                    firstInput.style.position = 'static !important';
                    
                    // 创建文件对象
                    const file = window.createFileFromBase64(
                        '{image_data["data"]}',
                        '{image_data["name"]}',
                        '{image_data["type"]}'
                    );
                    
                    if (!file) {{
                        console.error('文件创建失败');
                        return false;
                    }}
                    
                    // 设置文件到input
                    const success = window.setFileToInput(firstInput, file);
                    
                    if (success) {{
                        console.log('✅ 图片注入成功');
                        return true;
                    }} else {{
                        console.error('❌ 图片注入失败');
                        return false;
                    }}
                    
                }} catch (error) {{
                    console.error('图片注入异常:', error);
                    return false;
                }}
            }}
        """)
        
        await asyncio.sleep(3)  # 等待处理
        
        if not inject_result:
            print("❌ 图片注入失败")
            return False
        
        print("✅ 图片注入成功")
        
        # 步骤4: 验证注入结果
        print("🔍 步骤4: 验证注入结果...")
        
        final_state = await page.evaluate("""
            () => {
                console.log('🔍 验证注入结果');
                
                const result = {
                    fileInputs: 0,
                    blobImages: 0,
                    totalImages: 0,
                    success: false
                };
                
                result.fileInputs = document.querySelectorAll('input[type="file"]').length;
                result.totalImages = document.querySelectorAll('img').length;
                result.blobImages = document.querySelectorAll('img[src*="blob:"]').length;
                result.success = result.blobImages >= 1;
                
                console.log('验证结果:', result);
                return result;
            }
        """)
        
        print(f"📊 验证结果:")
        print(f"   文件输入: {final_state['fileInputs']} 个")
        print(f"   总图片: {final_state['totalImages']} 个")
        print(f"   预览图片: {final_state['blobImages']} 个")
        print(f"   注入成功: {'✅' if final_state['success'] else '❌'}")
        
        return final_state['success']
        
    except Exception as e:
        print(f"❌ 注入操作失败: {e}")
        return False


async def main():
    """主函数"""
    
    print("🚀 启动即梦AI首帧图片注入工具")
    print()
    
    success = await inject_first_frame()
    
    if success:
        print("\n🎉 首帧图片注入成功！")
        print("💡 请在Chrome浏览器中查看即梦AI页面")
        print("🚀 现在可以设置提示词并生成视频")
    else:
        print("\n❌ 首帧图片注入失败")
        print("💡 请检查：")
        print("   1. Chrome是否开启调试模式")
        print("   2. 即梦AI页面是否已打开")
        print("   3. 图片路径是否正确")


if __name__ == "__main__":
    print("=" * 60)
    print("🖼️  即梦AI首帧图片手动注入工具")
    print("=" * 60)
    print("📝 使用说明：")
    print("   1. 确保Chrome开启调试模式（端口9222）")
    print("   2. 打开即梦AI页面")
    print("   3. 修改脚本中的图片路径")
    print("   4. 运行此脚本")
    print("=" * 60)
    print()
    
    asyncio.run(main())
