#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MySQL数据库自动安装和配置脚本
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
import time
import json

class MySQLInstaller:
    """MySQL自动安装器"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.mysql_dir = os.path.join(os.getcwd(), "mysql_portable")
        self.mysql_data_dir = os.path.join(self.mysql_dir, "data")
        self.mysql_config = {
            'host': 'localhost',
            'port': 3306,
            'user': 'root',
            'password': 'kling123456',
            'database': 'kling_ai'
        }
    
    def check_mysql_installed(self):
        """检查MySQL是否已安装"""
        try:
            result = subprocess.run(['mysql', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 系统已安装MySQL")
                return True
        except FileNotFoundError:
            pass
        
        # 检查便携版MySQL
        mysql_exe = os.path.join(self.mysql_dir, "bin", "mysql.exe")
        if os.path.exists(mysql_exe):
            print("✅ 发现便携版MySQL")
            return True
        
        print("❌ 未发现MySQL安装")
        return False
    
    def download_mysql_portable(self):
        """下载便携版MySQL"""
        if self.system != "windows":
            print("❌ 便携版MySQL仅支持Windows系统")
            return False
        
        print("📥 开始下载便携版MySQL...")
        
        # MySQL 8.0 便携版下载链接
        mysql_url = "https://dev.mysql.com/get/Downloads/MySQL-8.0/mysql-8.0.35-winx64.zip"
        mysql_zip = "mysql-portable.zip"
        
        try:
            print(f"正在下载: {mysql_url}")
            urllib.request.urlretrieve(mysql_url, mysql_zip)
            print("✅ MySQL下载完成")
            
            # 解压
            print("📦 正在解压MySQL...")
            with zipfile.ZipFile(mysql_zip, 'r') as zip_ref:
                zip_ref.extractall(".")
            
            # 重命名目录
            extracted_dir = None
            for item in os.listdir("."):
                if item.startswith("mysql-") and os.path.isdir(item):
                    extracted_dir = item
                    break
            
            if extracted_dir:
                if os.path.exists(self.mysql_dir):
                    shutil.rmtree(self.mysql_dir)
                os.rename(extracted_dir, self.mysql_dir)
                print(f"✅ MySQL解压到: {self.mysql_dir}")
            
            # 清理下载文件
            os.remove(mysql_zip)
            return True
            
        except Exception as e:
            print(f"❌ MySQL下载失败: {e}")
            return False
    
    def install_mysql_alternative(self):
        """安装MySQL的替代方案"""
        print("\n🔄 MySQL安装失败，使用SQLite作为替代方案...")
        
        # 创建SQLite版本的数据库配置
        sqlite_config = {
            'type': 'sqlite',
            'database': 'kling_ai.db'
        }
        
        # 保存配置
        with open('db_config.json', 'w', encoding='utf-8') as f:
            json.dump(sqlite_config, f, indent=2, ensure_ascii=False)
        
        print("✅ 已配置SQLite数据库")
        return True
    
    def configure_mysql(self):
        """配置MySQL"""
        print("⚙️ 正在配置MySQL...")
        
        # 创建配置文件
        config_content = f"""[mysqld]
port = {self.mysql_config['port']}
basedir = {self.mysql_dir.replace(os.sep, '/')}
datadir = {self.mysql_data_dir.replace(os.sep, '/')}
max_connections = 200
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
default-storage-engine = INNODB
sql_mode = NO_ENGINE_SUBSTITUTION,STRICT_TRANS_TABLES

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4
port = {self.mysql_config['port']}
"""
        
        config_file = os.path.join(self.mysql_dir, "my.ini")
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        print(f"✅ MySQL配置文件创建: {config_file}")
        
        # 初始化数据目录
        mysqld_exe = os.path.join(self.mysql_dir, "bin", "mysqld.exe")
        if os.path.exists(mysqld_exe):
            print("🔧 正在初始化MySQL数据目录...")
            try:
                subprocess.run([
                    mysqld_exe,
                    f"--defaults-file={config_file}",
                    "--initialize-insecure",
                    "--console"
                ], check=True, capture_output=True)
                print("✅ MySQL数据目录初始化完成")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ MySQL初始化失败: {e}")
                return False
        
        return False
    
    def start_mysql_service(self):
        """启动MySQL服务"""
        print("🚀 正在启动MySQL服务...")
        
        mysqld_exe = os.path.join(self.mysql_dir, "bin", "mysqld.exe")
        config_file = os.path.join(self.mysql_dir, "my.ini")
        
        if not os.path.exists(mysqld_exe):
            print("❌ 未找到MySQL可执行文件")
            return False
        
        try:
            # 启动MySQL服务（后台运行）
            process = subprocess.Popen([
                mysqld_exe,
                f"--defaults-file={config_file}",
                "--console"
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            # 等待服务启动
            print("⏳ 等待MySQL服务启动...")
            time.sleep(10)
            
            # 检查服务是否启动成功
            if process.poll() is None:
                print("✅ MySQL服务启动成功")
                
                # 保存进程ID
                with open('mysql_pid.txt', 'w') as f:
                    f.write(str(process.pid))
                
                return True
            else:
                stdout, stderr = process.communicate()
                print(f"❌ MySQL服务启动失败")
                print(f"错误信息: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"❌ 启动MySQL服务异常: {e}")
            return False
    
    def create_database_and_user(self):
        """创建数据库和用户"""
        print("📊 正在创建数据库和用户...")
        
        mysql_exe = os.path.join(self.mysql_dir, "bin", "mysql.exe")
        
        if not os.path.exists(mysql_exe):
            print("❌ 未找到MySQL客户端")
            return False
        
        try:
            # 连接MySQL并创建数据库
            sql_commands = [
                f"CREATE DATABASE IF NOT EXISTS {self.mysql_config['database']} DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;",
                f"ALTER USER 'root'@'localhost' IDENTIFIED BY '{self.mysql_config['password']}';",
                "FLUSH PRIVILEGES;"
            ]
            
            for sql in sql_commands:
                result = subprocess.run([
                    mysql_exe,
                    "-u", "root",
                    "-e", sql
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    print(f"❌ SQL执行失败: {sql}")
                    print(f"错误: {result.stderr}")
                    return False
            
            print("✅ 数据库和用户创建成功")
            
            # 保存数据库配置
            with open('db_config.json', 'w', encoding='utf-8') as f:
                json.dump({
                    'type': 'mysql',
                    **self.mysql_config
                }, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"❌ 创建数据库异常: {e}")
            return False
    
    def install(self):
        """执行完整安装流程"""
        print("🚀 开始MySQL安装和配置流程")
        print("=" * 50)
        
        # 1. 检查是否已安装
        if self.check_mysql_installed():
            print("✅ MySQL已可用，跳过安装")
            return True
        
        # 2. 下载便携版MySQL
        if not self.download_mysql_portable():
            return self.install_mysql_alternative()
        
        # 3. 配置MySQL
        if not self.configure_mysql():
            return self.install_mysql_alternative()
        
        # 4. 启动MySQL服务
        if not self.start_mysql_service():
            return self.install_mysql_alternative()
        
        # 5. 创建数据库和用户
        if not self.create_database_and_user():
            return self.install_mysql_alternative()
        
        print("\n🎉 MySQL安装和配置完成！")
        print(f"数据库配置:")
        print(f"  主机: {self.mysql_config['host']}")
        print(f"  端口: {self.mysql_config['port']}")
        print(f"  用户: {self.mysql_config['user']}")
        print(f"  密码: {self.mysql_config['password']}")
        print(f"  数据库: {self.mysql_config['database']}")
        
        return True

def main():
    """主函数"""
    installer = MySQLInstaller()
    
    try:
        success = installer.install()
        if success:
            print("\n✅ 数据库安装配置成功！")
            print("现在可以运行应用程序了。")
        else:
            print("\n❌ 数据库安装配置失败！")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ 安装被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 安装过程发生异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
