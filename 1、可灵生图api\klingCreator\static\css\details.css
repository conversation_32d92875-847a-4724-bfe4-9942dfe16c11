/* 详情页面样式 */

/* 导航栏 */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    margin-bottom: 1rem;
    border-bottom: 2px solid #e2e8f0;
}

.nav-brand h1 {
    margin: 0;
    color: #2d3748;
    font-size: 1.5rem;
}

.nav-links {
    display: flex;
    gap: 1rem;
}

.nav-link {
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: #4a5568;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
    background: #4299e1;
    color: white;
}

/* 返回按钮 */
.back-section {
    margin-bottom: 1.5rem;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: #f7fafc;
    color: #4a5568;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #edf2f7;
    color: #2d3748;
}

/* 原图部分 */
.original-section {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.original-image-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    background: #f7fafc;
    border-radius: 8px;
}

.original-image {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.original-info h2 {
    margin: 0 0 1rem 0;
    color: #2d3748;
    font-size: 1.5rem;
    word-break: break-all;
}

.original-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.stat-icon {
    font-size: 1.2rem;
    width: 24px;
}

.stat-label {
    color: #718096;
    min-width: 80px;
}

.stat-value {
    color: #2d3748;
    font-weight: 500;
}

/* 原图操作按钮 */
.original-actions {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

#reuse-image-btn {
    width: 100%;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

#reuse-image-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

#reuse-image-btn:active {
    transform: translateY(0);
}

/* 筛选部分 */
.filter-section {
    margin-bottom: 1.5rem;
}

.filter-section h3 {
    margin: 0 0 1rem 0;
    color: #2d3748;
    font-size: 1.3rem;
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;
}

.filter-tabs {
    display: flex;
    gap: 0.5rem;
}

.filter-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: #f7fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.filter-tab:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.filter-tab.active {
    background: #4299e1;
    border-color: #4299e1;
    color: white;
}

.sort-controls select {
    padding: 0.75rem 1rem;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 0.9rem;
    background: white;
    cursor: pointer;
}

/* 生成图片网格 */
.generated-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.generated-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
}

.generated-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.generated-image-container {
    position: relative;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 150px;
    background: #f7fafc;
    border-radius: 8px;
}

.generated-image {
    max-width: 100%;
    max-height: 250px;
    object-fit: contain;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.clickable-image {
    cursor: pointer;
    transition: opacity 0.2s ease;
}

.clickable-image:hover {
    opacity: 0.9;
}

.generated-overlay-actions, .generated-corner-actions {
    position: absolute;
    bottom: 6px;
    right: 6px;
    display: flex;
    gap: 4px;
    opacity: 1;
}

.btn-like, .btn-dislike, .btn-favorite, .btn-remove {
    background: rgba(0, 0, 0, 0.7);
    border: none;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    backdrop-filter: blur(4px);
}

/* 收藏按钮样式 */
.btn-like:hover, .btn-favorite:hover {
    background: rgba(237, 137, 54, 0.9); /* 橙色，表示收藏 */
    transform: scale(1.05);
}

.btn-like.active, .btn-favorite.active {
    background: rgba(237, 137, 54, 0.9);
    transform: scale(1.05);
}

/* 标记删除按钮样式 */
.btn-dislike:hover, .btn-remove:hover {
    background: rgba(229, 62, 62, 0.9);
    transform: scale(1.05);
}

.btn-dislike.active, .btn-remove.active {
    background: rgba(229, 62, 62, 0.9);
    transform: scale(1.05);
}

.generated-info {
    padding: 1rem;
}

.generated-style {
    font-weight: bold;
    color: #2d3748;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.generated-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.generated-actions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.8rem;
    color: #718096;
}

.generated-time {
    font-size: 0.8rem;
    color: #a0aec0;
}

/* 加载和空状态 */
.loading {
    text-align: center;
    padding: 2rem;
    color: #718096;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #4299e1;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: #718096;
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: #4a5568;
}

.empty-state p {
    margin-bottom: 1.5rem;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    align-items: center;
    justify-content: center;
}

.modal-content {
    position: relative;
    background-color: white;
    margin: 0;
    padding: 0;
    border-radius: 12px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    color: white;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1001;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.7);
}

#modal-image {
    width: 100%;
    max-height: 70vh;
    object-fit: contain;
    background: #f7fafc;
}

.modal-info {
    padding: 1.5rem;
}

.modal-info h3 {
    margin: 0 0 0.5rem 0;
    color: #2d3748;
}

.modal-stats {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    color: #718096;
    font-size: 0.9rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        gap: 1rem;
    }
    
    .original-section {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .filter-tabs {
        justify-content: center;
    }
    
    .generated-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }
}

/* ==================== 多选功能样式 ==================== */

/* 多选功能区域 */
.multi-select-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 1rem;
    margin-top: 1rem;
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.multi-select-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.multi-select-icon {
    font-size: 1.5rem;
}

.multi-select-text {
    font-weight: 600;
    font-size: 1.1rem;
}

.multi-select-tip {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-left: auto;
}

.multi-select-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

#generate-transition-video-btn {
    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(238, 90, 36, 0.3);
}

#generate-transition-video-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(238, 90, 36, 0.4);
}

#generate-transition-video-btn:disabled {
    background: rgba(255, 255, 255, 0.3);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

#cancel-multi-select-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#cancel-multi-select-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 图片选择状态 */
.generated-image-container {
    position: relative;
}

.multi-select-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(102, 126, 234, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    z-index: 10;
    pointer-events: none; /* 允许点击穿透到下层图片 */
}

.select-number {
    background: white;
    color: #667eea;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1.2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.generated-image-container.selected {
    transform: scale(0.95);
    transition: transform 0.2s ease;
}

.generated-image-container.selected .generated-image {
    border: 3px solid #667eea;
    border-radius: 8px;
}

/* 多选模式下的鼠标样式 */
.multi-select-mode .generated-image {
    cursor: pointer !important;
}

.multi-select-mode .generated-image:hover {
    opacity: 0.8;
    transition: opacity 0.2s ease;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .multi-select-section {
        padding: 0.75rem;
    }

    .multi-select-info {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
        margin-bottom: 0.75rem;
    }

    .multi-select-tip {
        margin-left: 0;
        font-size: 0.8rem;
    }

    .multi-select-actions {
        flex-direction: column;
        gap: 0.5rem;
        width: 100%;
    }

    #generate-transition-video-btn,
    #cancel-multi-select-btn {
        width: 100%;
        text-align: center;
    }

    .select-number {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
}

/* 任务详情模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.task-details-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    z-index: 1001;
    max-width: 800px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
    border-radius: 12px 12px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #2d3748;
    font-size: 1.25rem;
}

.close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #718096;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: #e2e8f0;
    color: #2d3748;
}

.modal-content {
    padding: 1.5rem;
}

.task-summary {
    margin-bottom: 2rem;
    padding: 1rem;
    background: #f7fafc;
    border-radius: 8px;
    border-left: 4px solid #4299e1;
}

.task-summary h4 {
    margin: 0 0 1rem 0;
    color: #2d3748;
    font-size: 1.1rem;
}

.task-summary p {
    margin: 0.5rem 0;
    color: #4a5568;
}

.segments-details h4 {
    margin: 0 0 1rem 0;
    color: #2d3748;
    font-size: 1.1rem;
}

.segments-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.segment-card {
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1rem;
    background: white;
    transition: all 0.2s ease;
}

.segment-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.segment-card.completed {
    border-color: #48bb78;
    background: #f0fff4;
}

.segment-card.processing {
    border-color: #ed8936;
    background: #fffaf0;
}

.segment-card.failed {
    border-color: #f56565;
    background: #fff5f5;
}

.segment-card.pending {
    border-color: #cbd5e0;
    background: #f7fafc;
}

.segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.segment-status {
    font-size: 0.875rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.05);
}

.segment-info p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
    color: #4a5568;
}

.error-text {
    color: #e53e3e !important;
    font-weight: 500;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.modal-actions .btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.modal-actions .btn-primary {
    background: #4299e1;
    color: white;
}

.modal-actions .btn-primary:hover {
    background: #3182ce;
}

.modal-actions .btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.modal-actions .btn-secondary:hover {
    background: #cbd5e0;
}
