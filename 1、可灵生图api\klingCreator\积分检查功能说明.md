# 积分检查功能说明

## 📋 功能概述

在前端生成图片时，系统会自动检查用户的可灵AI积分余额，确保有足够的积分来完成生成任务。如果积分不足，会友好地提示用户并阻止任务提交。

## ✨ 主要特性

### 1. 实时积分检查
- 在提交生成任务前自动检查积分余额
- 计算所需积分（每张图片约1积分）
- 实时显示当前积分余额

### 2. 友好的用户界面
- 页面顶部显示当前积分余额
- 积分不足时显示美观的弹窗提示
- 提供详细的积分信息和建议

### 3. 智能错误处理
- 区分积分不足和其他错误
- 提供具体的缺少积分数量
- 自动刷新积分余额显示

## 🔧 技术实现

### 后端实现 (app.py)

```python
# 在生成图片API中添加积分检查
@app.route('/api/generate', methods=['POST'])
def generate_images_concurrent():
    # ... 其他验证 ...
    
    # 检查积分余额
    cookie = get_cookie()
    temp_generator = ImageGen(cookie)
    current_balance = temp_generator.get_account_point()
    required_points = len(prompts) * 1.0  # 每张图片1积分
    
    if current_balance < required_points:
        return jsonify({
            'error': f'积分余额不足！',
            'current_balance': current_balance,
            'required_points': required_points,
            'insufficient_balance': True
        }), 400
```

### 前端实现 (script.js)

```javascript
// 检查积分不足错误
if (data.insufficient_balance) {
    showBalanceWarning(data);  // 显示友好的警告弹窗
    loadBalance();             // 刷新积分显示
}

// 显示积分警告弹窗
function showBalanceWarning(data) {
    // 创建美观的弹窗，显示详细积分信息
    // 提供充值链接和建议
}
```

### 样式实现 (style.css)

```css
/* 积分显示样式 */
.balance-info {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
}

/* 积分不足警告弹窗 */
.balance-warning {
    position: fixed;
    background: white;
    border: 2px solid #ff6b6b;
    border-radius: 15px;
    /* ... 更多样式 ... */
}
```

## 📊 积分计算规则

| 操作类型 | 消耗积分 | 说明 |
|---------|---------|------|
| 图生图 | 1积分/张 | 基于上传图片生成新图片 |
| 文生图 | 1积分/张 | 基于文字描述生成图片 |
| 批量生成 | N积分 | N = 生成图片数量 |

## 🎯 用户体验

### 积分充足时
1. 正常提交生成任务
2. 显示生成进度
3. 完成后刷新积分余额

### 积分不足时
1. 阻止任务提交
2. 显示友好的警告弹窗
3. 提供详细的积分信息：
   - 当前余额
   - 需要积分
   - 缺少积分
4. 提供解决建议：
   - 减少生成数量
   - 前往官网充值
   - 等待每日免费积分

## 🔍 错误处理

### 积分检查失败
- Cookie无效：提示用户重新登录
- 网络错误：显示网络异常信息
- API错误：记录详细错误日志

### 日志记录
```
🔍 检查积分余额...
💰 当前积分余额: 15.5
📊 需要积分: 3.0
🎯 生成任务数: 3
✅ 积分余额充足，开始生成图片
```

## 🚀 使用方法

1. **正常使用**：上传图片，选择提示词，点击生成
2. **积分不足**：系统自动提示，用户可选择：
   - 减少生成数量
   - 前往充值页面
   - 取消本次操作

## 🔧 配置选项

可以在代码中调整以下参数：

```python
# 每张图片消耗的积分数（可根据实际情况调整）
required_points = len(prompts) * 1.0

# 积分检查的超时时间
timeout = 10  # 秒
```

## 📝 注意事项

1. **积分计算**：当前按每张图片1积分计算，可根据实际API消耗调整
2. **实时性**：积分余额实时获取，确保准确性
3. **用户体验**：优先保证用户体验，避免任务提交后失败
4. **错误恢复**：积分检查失败时允许用户手动重试

## 🎉 效果展示

- ✅ 页面顶部实时显示积分余额
- ✅ 生成前自动检查积分
- ✅ 积分不足时友好提示
- ✅ 提供充值和减少任务的建议
- ✅ 自动刷新积分显示
