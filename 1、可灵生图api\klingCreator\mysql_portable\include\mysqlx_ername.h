/*
 * Copyright (c) 2016, 2023, Oracle and/or its affiliates.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License, version 2.0,
 * as published by the Free Software Foundation.
 *
 * This program is also distributed with certain software (including
 * but not limited to OpenSSL) that is licensed under separate terms,
 * as designated in a particular file or component or in included license
 * documentation.  The authors of MySQL hereby grant you an additional
 * permission to link the program and your derivative works with the
 * separately licensed software that they have included with MySQL.
 *  
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License, version 2.0, for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
 */

/* Autogenerated file, please don't edit */

#include "mysqlx_error.h"

  {"ER_X_BAD_MESSAGE", ER_X_BAD_MESSAGE, "", NULL, NULL, 0 },
  {"ER_X_CAPABILITIES_PREPARE_FAILED", ER_X_CAPABILITIES_PREPARE_FAILED, "", NULL, NULL, 0 },
  {"ER_X_CAPABILITY_NOT_FOUND", ER_X_CAPABILITY_NOT_FOUND, "", NULL, NULL, 0 },
  {"ER_X_INVALID_PROTOCOL_DATA", ER_X_INVALID_PROTOCOL_DATA, "", NULL, NULL, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_VALUE_LENGTH", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_VALUE_LENGTH, "", NULL, NULL, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_KEY_LENGTH", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_KEY_LENGTH, "", NULL, NULL, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_EMPTY_KEY", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_EMPTY_KEY, "", NULL, NULL, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_LENGTH", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_LENGTH, "", NULL, NULL, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_TYPE", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_TYPE, "", NULL, NULL, 0 },
  {"ER_X_CAPABILITY_SET_NOT_ALLOWED", ER_X_CAPABILITY_SET_NOT_ALLOWED, "", NULL, NULL, 0 },
  {"ER_X_SERVICE_ERROR", ER_X_SERVICE_ERROR, "", NULL, NULL, 0 },
  {"ER_X_SESSION", ER_X_SESSION, "", NULL, NULL, 0 },
  {"ER_X_INVALID_ARGUMENT", ER_X_INVALID_ARGUMENT, "", NULL, NULL, 0 },
  {"ER_X_MISSING_ARGUMENT", ER_X_MISSING_ARGUMENT, "", NULL, NULL, 0 },
  {"ER_X_BAD_INSERT_DATA", ER_X_BAD_INSERT_DATA, "", NULL, NULL, 0 },
  {"ER_X_CMD_NUM_ARGUMENTS", ER_X_CMD_NUM_ARGUMENTS, "", NULL, NULL, 0 },
  {"ER_X_CMD_ARGUMENT_TYPE", ER_X_CMD_ARGUMENT_TYPE, "", NULL, NULL, 0 },
  {"ER_X_CMD_ARGUMENT_VALUE", ER_X_CMD_ARGUMENT_VALUE, "", NULL, NULL, 0 },
  {"ER_X_BAD_UPSERT_DATA", ER_X_BAD_UPSERT_DATA, "", NULL, NULL, 0 },
  {"ER_X_DUPLICATED_CAPABILITIES", ER_X_DUPLICATED_CAPABILITIES, "", NULL, NULL, 0 },
  {"ER_X_CMD_ARGUMENT_OBJECT_EMPTY", ER_X_CMD_ARGUMENT_OBJECT_EMPTY, "", NULL, NULL, 0 },
  {"ER_X_CMD_INVALID_ARGUMENT", ER_X_CMD_INVALID_ARGUMENT, "", NULL, NULL, 0 },
  {"ER_X_BAD_UPDATE_DATA", ER_X_BAD_UPDATE_DATA, "", NULL, NULL, 0 },
  {"ER_X_BAD_TYPE_OF_UPDATE", ER_X_BAD_TYPE_OF_UPDATE, "", NULL, NULL, 0 },
  {"ER_X_BAD_COLUMN_TO_UPDATE", ER_X_BAD_COLUMN_TO_UPDATE, "", NULL, NULL, 0 },
  {"ER_X_BAD_MEMBER_TO_UPDATE", ER_X_BAD_MEMBER_TO_UPDATE, "", NULL, NULL, 0 },
  {"ER_X_BAD_STATEMENT_ID", ER_X_BAD_STATEMENT_ID, "", NULL, NULL, 0 },
  {"ER_X_BAD_CURSOR_ID", ER_X_BAD_CURSOR_ID, "", NULL, NULL, 0 },
  {"ER_X_BAD_SCHEMA", ER_X_BAD_SCHEMA, "", NULL, NULL, 0 },
  {"ER_X_BAD_TABLE", ER_X_BAD_TABLE, "", NULL, NULL, 0 },
  {"ER_X_BAD_PROJECTION", ER_X_BAD_PROJECTION, "", NULL, NULL, 0 },
  {"ER_X_DOC_ID_MISSING", ER_X_DOC_ID_MISSING, "", NULL, NULL, 0 },
  {"ER_X_DUPLICATE_ENTRY", ER_X_DUPLICATE_ENTRY, "", NULL, NULL, 0 },
  {"ER_X_DOC_REQUIRED_FIELD_MISSING", ER_X_DOC_REQUIRED_FIELD_MISSING, "", NULL, NULL, 0 },
  {"ER_X_PROJ_BAD_KEY_NAME", ER_X_PROJ_BAD_KEY_NAME, "", NULL, NULL, 0 },
  {"ER_X_BAD_DOC_PATH", ER_X_BAD_DOC_PATH, "", NULL, NULL, 0 },
  {"ER_X_CURSOR_EXISTS", ER_X_CURSOR_EXISTS, "", NULL, NULL, 0 },
  {"ER_X_CURSOR_REACHED_EOF", ER_X_CURSOR_REACHED_EOF, "", NULL, NULL, 0 },
  {"ER_X_PREPARED_STATMENT_CAN_HAVE_ONE_CURSOR", ER_X_PREPARED_STATMENT_CAN_HAVE_ONE_CURSOR, "", NULL, NULL, 0 },
  {"ER_X_PREPARED_EXECUTE_ARGUMENT_NOT_SUPPORTED", ER_X_PREPARED_EXECUTE_ARGUMENT_NOT_SUPPORTED, "", NULL, NULL, 0 },
  {"ER_X_PREPARED_EXECUTE_ARGUMENT_CONSISTENCY", ER_X_PREPARED_EXECUTE_ARGUMENT_CONSISTENCY, "", NULL, NULL, 0 },
  {"ER_X_EXPR_BAD_OPERATOR", ER_X_EXPR_BAD_OPERATOR, "", NULL, NULL, 0 },
  {"ER_X_EXPR_BAD_NUM_ARGS", ER_X_EXPR_BAD_NUM_ARGS, "", NULL, NULL, 0 },
  {"ER_X_EXPR_MISSING_ARG", ER_X_EXPR_MISSING_ARG, "", NULL, NULL, 0 },
  {"ER_X_EXPR_BAD_TYPE_VALUE", ER_X_EXPR_BAD_TYPE_VALUE, "", NULL, NULL, 0 },
  {"ER_X_EXPR_BAD_VALUE", ER_X_EXPR_BAD_VALUE, "", NULL, NULL, 0 },
  {"ER_X_INVALID_COLLECTION", ER_X_INVALID_COLLECTION, "", NULL, NULL, 0 },
  {"ER_X_INVALID_ADMIN_COMMAND", ER_X_INVALID_ADMIN_COMMAND, "", NULL, NULL, 0 },
  {"ER_X_EXPECT_NOT_OPEN", ER_X_EXPECT_NOT_OPEN, "", NULL, NULL, 0 },
  {"ER_X_EXPECT_NO_ERROR_FAILED", ER_X_EXPECT_NO_ERROR_FAILED, "", NULL, NULL, 0 },
  {"ER_X_EXPECT_BAD_CONDITION", ER_X_EXPECT_BAD_CONDITION, "", NULL, NULL, 0 },
  {"ER_X_EXPECT_BAD_CONDITION_VALUE", ER_X_EXPECT_BAD_CONDITION_VALUE, "", NULL, NULL, 0 },
  {"ER_X_INVALID_NAMESPACE", ER_X_INVALID_NAMESPACE, "", NULL, NULL, 0 },
  {"ER_X_BAD_NOTICE", ER_X_BAD_NOTICE, "", NULL, NULL, 0 },
  {"ER_X_CANNOT_DISABLE_NOTICE", ER_X_CANNOT_DISABLE_NOTICE, "", NULL, NULL, 0 },
  {"ER_X_BAD_CONFIGURATION", ER_X_BAD_CONFIGURATION, "", NULL, NULL, 0 },
  {"ER_X_MYSQLX_ACCOUNT_MISSING_PERMISSIONS", ER_X_MYSQLX_ACCOUNT_MISSING_PERMISSIONS, "", NULL, NULL, 0 },
  {"ER_X_EXPECT_FIELD_EXISTS_FAILED", ER_X_EXPECT_FIELD_EXISTS_FAILED, "", NULL, NULL, 0 },
  {"ER_X_BAD_LOCKING", ER_X_BAD_LOCKING, "", NULL, NULL, 0 },
  {"ER_X_FRAME_COMPRESSION_DISABLED", ER_X_FRAME_COMPRESSION_DISABLED, "", NULL, NULL, 0 },
  {"ER_X_DECOMPRESSION_FAILED", ER_X_DECOMPRESSION_FAILED, "", NULL, NULL, 0 },
  {"ER_X_BAD_COMPRESSED_FRAME", ER_X_BAD_COMPRESSED_FRAME, "", NULL, NULL, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_INVALID_ALGORITHM", ER_X_CAPABILITY_COMPRESSION_INVALID_ALGORITHM, "", NULL, NULL, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_INVALID_SERVER_STYLE", ER_X_CAPABILITY_COMPRESSION_INVALID_SERVER_STYLE, "", NULL, NULL, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_INVALID_CLIENT_STYLE", ER_X_CAPABILITY_COMPRESSION_INVALID_CLIENT_STYLE, "", NULL, NULL, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_INVALID_OPTION", ER_X_CAPABILITY_COMPRESSION_INVALID_OPTION, "", NULL, NULL, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_MISSING_REQUIRED_FIELDS", ER_X_CAPABILITY_COMPRESSION_MISSING_REQUIRED_FIELDS, "", NULL, NULL, 0 },
  {"ER_X_DOCUMENT_DOESNT_MATCH_EXPECTED_SCHEMA", ER_X_DOCUMENT_DOESNT_MATCH_EXPECTED_SCHEMA, "", NULL, NULL, 0 },
  {"ER_X_COLLECTION_OPTION_DOESNT_EXISTS", ER_X_COLLECTION_OPTION_DOESNT_EXISTS, "", NULL, NULL, 0 },
  {"ER_X_INVALID_VALIDATION_SCHEMA", ER_X_INVALID_VALIDATION_SCHEMA, "", NULL, NULL, 0 },

