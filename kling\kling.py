# 导入命令行参数解析库，用于处理程序启动时的命令行参数
import argparse
# 导入操作系统接口库，用于文件路径操作和环境变量访问
import os
# 导入时间库，用于延时和时间戳操作
import time
# 导入上下文管理库，用于资源管理和异常处理
import contextlib
# 导入类型提示库，用于函数参数和返回值的类型标注
from typing import Optional, Literal
# 导入枚举库，用于定义常量枚举类
from enum import Enum
# 导入HTTP cookie处理库，用于解析和处理cookie字符串
from http.cookies import SimpleCookie

# 导入伪造用户代理库，用于模拟不同浏览器的请求
from fake_useragent import UserAgent
# 导入HTTP请求库，用于与可灵AI API进行网络通信
import requests
# 导入requests工具函数，用于cookie字典转换
from requests.utils import cookiejar_from_dict
# 导入rich库的print函数，用于美化控制台输出
from rich import print
# 导入线程库，用于多线程处理
import threading

# 浏览器版本标识，用于模拟Edge浏览器
browser_version = "edge101"
# 创建用户代理生成器，专门模拟Edge浏览器的用户代理字符串
ua = UserAgent(browsers=["Edge"])
# 可灵AI中国版的基础URL地址
base_url = "https://klingai.kuaishou.com/"
# 可灵AI国际版的基础URL地址
base_url_not_cn = "https://klingai.com/"


# 每日签到检查函数，用于获取每日登录奖励
def call_for_daily_check(session: requests.Session, is_cn: bool) -> bool:
    # 根据是否为中国版选择不同的API端点
    if is_cn:
        # 向中国版API发送每日签到请求
        r = session.get(f"{base_url}api/pay/reward?activity=login_bonus_daily")
    else:
        # 向国际版API发送每日签到请求
        r = session.get(f"{base_url_not_cn}api/pay/reward?activity=login_bonus_daily")
    # 检查请求是否成功
    if r.ok:
        # 打印签到成功信息和返回的JSON数据
        print(f"Call daily login success with {is_cn}:\n{r.json()}\n")
        return True

    # 如果签到失败，抛出异常提示token可能不正确
    raise Exception(
        "Call daily login failed with CN or Non-CN. The token may be incorrect."
    )


# 任务状态枚举类，定义任务的各种状态
class TaskStatus(Enum):
    # 任务等待中状态，值为0
    PENDING = 0
    # 任务已完成状态，值为1
    COMPLETED = 1
    # 任务失败状态，值为3
    FAILED = 3


# 基础生成器类，VideoGen和ImageGen的父类，包含通用功能
class BaseGen:
    # 初始化方法，设置会话和API端点
    def __init__(self, cookie: str) -> None:
        # 创建requests会话对象，用于保持连接状态和cookie
        self.session: requests.Session = requests.Session()
        # 保存用户提供的cookie字符串
        self.cookie = cookie
        # 解析cookie字符串并设置到会话中，同时判断是否为中国版
        self.session.cookies, is_cn = self.parse_cookie_string(self.cookie)
        # 设置随机的用户代理字符串，模拟真实浏览器请求
        self.session.headers["user-agent"] = ua.random
        # 执行每日签到检查，获取每日奖励
        call_for_daily_check(self.session, is_cn)
        # 根据是否为中国版设置不同的基础URL和上传URL
        if is_cn:
            # 中国版的基础URL
            self.base_url = base_url
            # 中国版的图片上传基础URL
            image_upload_base_url = "https://upload.kuaishouzt.com/"
        else:
            # 国际版的基础URL
            self.base_url = base_url_not_cn
            # 国际版的图片上传基础URL
            image_upload_base_url = "https://upload.uvfuns.com/"

        # 构建API端点字典，包含图片上传相关的所有API地址
        self.apis_dict = {
            # 获取上传token的API端点
            "image_upload_gettoken": f"{self.base_url}api/upload/issue/token?filename=",
            # 恢复上传的API端点
            "image_upload_resume": f"{image_upload_base_url}api/upload/resume?upload_token=",
            # 分片上传的API端点
            "image_upload_fragment": f"{image_upload_base_url}api/upload/fragment",
            # 完成上传的API端点
            "image_upload_complete": f"{image_upload_base_url}api/upload/complete",
            # 获取上传文件URL的API端点
            "image_upload_geturl": f"{self.base_url}api/upload/verify/token?token=",
        }

        # 任务提交的API端点
        self.submit_url = f"{self.base_url}api/task/submit"
        # 每日签到的API端点
        self.daily_url = f"{self.base_url}api/pay/reward?activity=login_bonus_daily"
        # 获取账户积分的API端点
        self.point_url = f"{self.base_url}api/account/point"
        # 存储视频ID列表，用于未来扩展或其他用途
        self.video_id_list = []

    # 静态方法，解析cookie字符串并判断是否为中国版
    @staticmethod
    def parse_cookie_string(cookie_string):
        # 创建SimpleCookie对象用于解析cookie字符串
        cookie = SimpleCookie()
        # 加载cookie字符串到SimpleCookie对象中
        cookie.load(cookie_string)
        # 初始化cookie字典
        cookies_dict = {}
        # 初始化cookiejar对象
        cookiejar = None
        # 初始化中国版标识为False
        is_cn = False
        # 遍历解析后的cookie项
        for key, morsel in cookie.items():
            # 如果cookie名称以"kuaishou"开头，说明是中国版
            if key.startswith("kuaishou"):
                is_cn = True
            # 将cookie键值对添加到字典中
            cookies_dict[key] = morsel.value
            # 将字典转换为cookiejar对象，用于requests会话
            cookiejar = cookiejar_from_dict(
                cookies_dict, cookiejar=None, overwrite=True
            )
        # 返回cookiejar对象和是否为中国版的标识
        return cookiejar, is_cn

    # 获取账户积分余额的方法
    def get_account_point(self) -> float:
        # 发送请求获取奖励信息
        bonus_req = self.session.get(self.daily_url)
        # 解析响应为JSON格式
        bonus_data = bonus_req.json()
        # 断言响应状态为200，确保请求成功
        assert bonus_data.get("status") == 200

        # 发送请求获取积分信息
        point_req = self.session.get(self.point_url)
        # 解析响应为JSON格式
        point_data = point_req.json()
        # 断言响应状态为200，确保请求成功
        assert point_data.get("status") == 200

        # 从响应数据中提取总积分数
        total_point = point_data["data"]["total"]
        # 将积分除以100转换为实际积分值并返回
        return total_point / 100

    # 图片上传器方法，将本地图片上传到可灵AI服务器
    def image_uploader(self, image_path) -> str:
        """
        图片上传功能实现
        参考来源: https://github.com/dolacmeo/acfunsdk/blob/ece6f42e2736b316fea35d89ba1d0ccbec6c98f7/acfun/page/utils.py
        感谢原作者的贡献
        """
        # 以二进制模式打开图片文件并读取数据
        with open(image_path, "rb") as f:
            image_data = f.read()
        # 从文件路径中提取文件名
        file_name = image_path.split("/")[-1]
        # 构建获取上传token的URL
        upload_url = self.apis_dict["image_upload_gettoken"] + file_name
        # 发送请求获取上传token
        token_req = self.session.get(upload_url)
        # 解析响应为JSON格式
        token_data = token_req.json()

        # 断言响应状态为200，确保获取token成功
        assert token_data.get("status") == 200

        # 从响应中提取上传token
        token = token_data["data"]["token"]
        # 构建恢复上传的URL
        resume_url = self.apis_dict["image_upload_resume"] + token
        # 发送恢复上传请求
        resume_req = self.session.get(resume_url)
        # 解析响应为JSON格式
        resume_data = resume_req.json()

        # 断言恢复上传结果为1，确保操作成功
        assert resume_data.get("result") == 1
        # 发送分片上传请求，将图片数据上传
        fragment_req = self.session.post(
            self.apis_dict["image_upload_fragment"],
            data=image_data,  # 图片二进制数据
            params=dict(upload_token=token, fragment_id=0),  # 上传参数
            headers={"Content-Type": "application/octet-stream"},  # 设置内容类型为二进制流
        )
        # 解析分片上传响应
        fragment_data = fragment_req.json()
        # 断言分片上传结果为1，确保上传成功
        assert fragment_data.get("result") == 1
        # 发送完成上传请求
        complete_req = self.session.post(
            self.apis_dict["image_upload_complete"],
            params=dict(upload_token=token, fragment_count=1),  # 指定分片数量为1
        )
        # 解析完成上传响应
        complete_data = complete_req.json()
        # 断言完成上传结果为1，确保操作成功
        assert complete_data.get("result") == 1
        # 构建验证上传的URL
        verify_url = self.apis_dict["image_upload_geturl"] + token
        # 发送验证请求获取最终的图片URL
        result_req = self.session.get(verify_url)
        # 解析验证响应
        result_data = result_req.json()
        # 断言验证状态为200，确保获取URL成功
        assert result_data.get("status") == 200
        # 返回上传后的图片URL
        return result_data.get("data").get("url")

    # 获取任务元数据和状态的方法
    def fetch_metadata(self, task_id: str) -> tuple[dict, TaskStatus]:
        # 构建查询任务状态的URL
        url = f"{self.base_url}api/task/status?taskId={task_id}"
        # 发送请求获取任务状态
        response = self.session.get(url)
        # 从响应中提取数据部分
        data = response.json().get("data")
        # 断言数据不为空
        assert data is not None
        # 根据状态值判断任务状态（有趣的是它使用分辨率来检查图像是否准备就绪）
        if data.get("status") >= 90:
            # 状态值>=90表示任务已完成
            return data, TaskStatus.COMPLETED
        elif data.get("status") in [9, 50]:
            # 状态值为9或50表示任务失败
            return data, TaskStatus.FAILED
        else:
            # 其他状态值表示任务仍在等待中
            return data, TaskStatus.PENDING


# 视频生成器类，继承自BaseGen，专门用于生成AI视频
class VideoGen(BaseGen):

    # 视频延长方法，基于现有视频生成延长版本
    def extend_video(self, video_id: int, prompt: str = "") -> str:
        # 获取视频的URL和初始提示词
        data, status = self.fetch_metadata(video_id)
        # 断言任务状态为已完成，确保视频生成完毕
        assert status == TaskStatus.COMPLETED
        # 从数据中获取作品列表
        works = data.get("works", [])
        # 检查是否找到视频作品
        if not works:
            print("No Video found.")
            raise Exception("No Video found.")
        else:
            # 初始化视频相关变量
            video_type, resource, prompt_init = None, None, None
            # 获取第一个作品
            work = works[0]
            # 提取作品ID
            work_id = work.get("workId")
            # 提取视频资源URL
            resource = work.get("resource", {}).get("resource")
            # 提取任务参数列表
            arguments = work.get("taskInfo", {}).get("arguments", [])
            # 提取视频类型
            video_type = work.get("taskInfo", {}).get("type")

            # 遍历参数列表查找初始提示词
            for arg in arguments:
                if arg.get("name") == "prompt":
                    prompt_init = arg.get("value")
                    break
            # 检查是否成功获取视频资源
            if not resource:
                print("No Video found.")
                raise Exception("No Video found.")

        payload = {
            "type": "m2v_extend_video",
            "inputs": [
                {
                    "name": "input",
                    "inputType": "URL",
                    "url": resource,
                    "fromWorkId": work_id,
                },
            ],
            "arguments": [
                {
                    "name": "prompt",
                    "value": "",
                },
                {
                    "name": "biz",
                    "value": "klingai",
                },
                {
                    "name": "__initialType",
                    "value": video_type,
                },
                {
                    "name": "__initialPrompt",
                    "value": prompt_init,
                },
            ],
        }
        return self._get_video_with_payload(payload)

    def fetch_video_url(self, work_id: str, session: requests.Session):
        url = f"{self.base_url}api/works/batch_download_v2?workIds={work_id}"
        response = session.get(url)
        data = response.json().get("data")
        assert data is not None
        if data.get("status") == "success":
            return data["cdnUrl"], TaskStatus.COMPLETED
        else:
            return data, TaskStatus.PENDING

    def _get_video_with_payload(self, payload: dict) -> list:
        # 重试获取请求ID
        request_id = None
        max_retries = 50  # 最多重试50次
        retry_count = 0

        while not request_id and retry_count < max_retries:
            try:
                if retry_count > 0:
                    print(f"Retrying to get request ID ({retry_count + 1}/{max_retries})...")

                response = self.session.post(
                    self.submit_url,
                    json=payload,
                )
                if not response.ok:
                    print(response.text)
                    raise Exception(f"Error response {str(response)}")
                response_body = response.json()
                # 安全地检查响应数据
                data = response_body.get("data")
                if data and data.get("status") == 7:
                    message = data.get("message")
                    raise Exception(f"Request failed message {message}")
                request_id = data.get("task", {}).get("id") if data else None

                if not request_id:
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"Could not get request ID, retrying in 11 seconds ({retry_count}/{max_retries})")
                        # 倒计时显示
                        for countdown in range(11, 0, -1):
                            print(f"⏳ Retry countdown: {countdown} seconds", end='\r')
                            time.sleep(1)
                        print()  # 换行
                    else:
                        raise Exception("Could not get request ID after multiple attempts")
                else:
                    print(f"Successfully got request ID: {request_id}")
                    break

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"Request failed, retrying in 11 seconds: {str(e)}")
                    # 倒计时显示
                    for countdown in range(11, 0, -1):
                        print(f"⏳ Retry countdown: {countdown} seconds", end='\r')
                        time.sleep(1)
                    print()  # 换行
                else:
                    raise Exception(f"Failed after multiple retries: {str(e)}")

        if not request_id:
            raise Exception("Could not get request ID")
        # store the video id list
        self.video_id_list.append(request_id)
        start_wait = time.time()
        print("Waiting for results... will take 2mins to 5mins")
        while True:
            if int(time.time() - start_wait) > 1200:
                raise Exception("Request timeout")
            image_data, status = self.fetch_metadata(request_id)
            if status == TaskStatus.PENDING:
                print(".", end="", flush=True)
                # spider rule
                time.sleep(5)
            elif status == TaskStatus.FAILED:
                print("Request failed")
                return []
            else:
                result = []
                works = image_data.get("works", [])
                if not works:
                    print("No video found.")
                    return []
                else:
                    for work in works:
                        work: dict
                        resource = work.get("resource", {}).get("resource")

                        # if workId exists fetch resource without watermark (for pytest passing)
                        if "workId" in work.keys():
                            work_id = work["workId"]
                            resource, _ = self.fetch_video_url(
                                work_id, session=self.session
                            )

                        if resource:
                            # sleep for 2s for waiting the video to be ready in kuaishou server
                            time.sleep(2)
                            result.append(resource)
                return result

    def get_video(
        self,
        prompt: str,
        image_path: Optional[str] = None,
        image_url: Optional[str] = None,
        is_high_quality: bool = False,
        auto_extend: bool = False,
        model_name: Literal["1.0", "1.5", "1.6", "2.1"] = "1.0",
    ) -> list:
        self.session.headers["user-agent"] = ua.random
        if image_path or image_url:
            if image_path:
                image_payload_url = self.image_uploader(image_path)
            else:
                image_payload_url = image_url
            if is_high_quality:
                model_type = "m2v_img2video_hq"
            else:
                model_type = "m2v_img2video"
            payload = {
                "arguments": [
                    {"name": "prompt", "value": prompt},
                    {
                        "name": "negative_prompt",
                        "value": "",
                    },
                    {
                        "name": "cfg",
                        "value": "0.5",
                    },
                    {
                        "name": "duration",
                        "value": "5",
                    },
                    {
                        "name": "kling_version",
                        "value": model_name,
                    },
                    {
                        "name": "tail_image_enabled",
                        "value": "false",
                    },
                    {
                        "name": "camera_json",
                        "value": '{"type":"empty","horizontal":0,"vertical":0,"zoom":0,"tilt":0,"pan":0,"roll":0}',
                    },
                    {
                        "name": "biz",
                        "value": "klingai",
                    },
                ],
                "inputs": [
                    {
                        "inputType": "URL",
                        "url": image_payload_url,
                        "name": "input",
                    },
                ],
                "type": model_type,
            }

        else:
            if is_high_quality:
                model_type = "m2v_txt2video_hq"
            else:
                model_type = "m2v_txt2video"
            payload = {
                "arguments": [
                    {"name": "prompt", "value": prompt},
                    {
                        "name": "negative_prompt",
                        "value": "",
                    },
                    {
                        "name": "cfg",
                        "value": "0.5",
                    },
                    {
                        "name": "duration",
                        "value": "5",
                    },
                    {
                        "name": "kling_version",
                        "value": model_name,
                    },
                    {
                        "name": "aspect_ratio",
                        "value": "16:9",
                    },
                    {
                        "name": "camera_json",
                        "value": '{"type":"empty","horizontal":0,"vertical":0,"zoom":0,"tilt":0,"pan":0,"roll":0}',
                    },
                    {
                        "name": "biz",
                        "value": "klingai",
                    },
                ],
                "inputs": [],
                "type": model_type,
            }
        if auto_extend:
            print("will generate and extending video...")
            self._get_video_with_payload(payload)
            print("Auto extending video...")
            video_id = self.video_id_list.pop()
            return self.extend_video(video_id)
        else:
            return self._get_video_with_payload(payload)

    def save_video(
        self,
        prompt: str,
        output_dir: str,
        image_path: Optional[str] = None,
        image_url: Optional[str] = None,
        is_high_quality: bool = False,
        auto_extend: bool = False,
        model_name: Literal["1.0", "1.5", "1.6", "2.1"] = "1.0",
    ) -> None:
        mp4_index = 0
        try:
            links = self.get_video(
                prompt,
                image_path=image_path,
                image_url=image_url,
                is_high_quality=is_high_quality,
                auto_extend=auto_extend,
                model_name=model_name,
            )
        except Exception as e:
            print(e)
            raise
        with contextlib.suppress(FileExistsError):
            os.mkdir(output_dir)
        print()
        if not links:
            print("No video found.")
            return
        link = links[0]
        while os.path.exists(os.path.join(output_dir, f"{mp4_index}.mp4")):
            mp4_index += 1
        response = self.session.get(link)
        if response.status_code != 200:
            raise Exception("Could not download image")
        # save response to file
        with open(os.path.join(output_dir, f"{mp4_index}.mp4"), "wb") as output_file:
            output_file.write(response.content)
        mp4_index += 1


# 图像生成器类，继承自BaseGen，专门用于生成AI图像
class ImageGen(BaseGen):
    # 获取图像URL的方法，用于下载无水印的图像
    def fetch_image_url(self, work_id: str, session: requests.Session):
        # 构建批量下载API的URL，指定作品ID和文件类型为PNG
        url = f"{self.base_url}api/works/batch_download_v2?workIds={work_id}&fileTypes=PNG"
        # 发送GET请求获取下载信息
        response = session.get(url)
        # 从响应中提取数据部分
        data = response.json().get("data")
        # 断言数据不为空
        assert data is not None

        # 检查下载状态
        if data.get("status") == "success":
            # 如果成功，返回CDN URL和完成状态
            return data["cdnUrl"], TaskStatus.COMPLETED
        else:
            # 如果未成功，返回数据和等待状态
            return data, TaskStatus.PENDING

    # 获取图像的主要方法，支持文本生图和图生图
    def get_images(
        self,
        prompt: str,  # 文本提示词，描述要生成的图像内容
        image_path: Optional[str] = None,  # 可选的本地图片路径，用于图生图
        image_url: Optional[str] = None,  # 可选的图片URL，用于图生图
        ratio: Literal[  # 图像宽高比，限制为预定义的比例
            "1:1", "16:9", "4:3", "3:2", "2:3", "3:4", "9:16", "21:9"
        ] = "1:1",  # 默认为正方形
        count: int = 4,  # 生成图像的数量，默认4张
        model_name: Literal["1.0", "1.5", "2.0", "2.1"] = "2.1",  # 模型版本，默认2.1
        high_res: bool = False,  # 是否使用高分辨率，仅对2.0和2.1版本有效
    ) -> list:  # 返回图像URL列表

        # 验证宽高比参数是否在支持的范围内
        if ratio not in (
            "1:1",    # 正方形
            "16:9",   # 宽屏
            "4:3",    # 标准屏幕
            "3:2",    # 相机常用比例
            "2:3",    # 竖版相机比例
            "3:4",    # 竖版标准屏幕
            "9:16",   # 竖版宽屏
            "21:9",   # 超宽屏
        ):
            # 如果比例不支持，抛出值错误异常
            raise ValueError(f'Unsupported ratio "{ratio}".')

        # 验证图像数量是否在有效范围内（1-9张）
        if count < 1 or count > 9:
            # 如果数量超出范围，抛出值错误异常
            raise ValueError(
                f'Unsupported images count "{count}". From 1 to 9 required.'
            )

        # 设置随机的用户代理字符串，避免被识别为机器人
        self.session.headers["user-agent"] = ua.random
        if image_path or image_url:
            if image_path:
                image_payload_url = self.image_uploader(image_path)
            else:
                image_payload_url = image_url
            payload = {
                "arguments": [
                    {
                        "name": "biz",
                        "value": "klingai",
                    },
                    {"name": "prompt", "value": prompt},
                    {
                        "name": "imageCount",
                        "value": str(count),
                    },
                    {
                        "name": "kolors_version",
                        "value": model_name,
                    },
                    {
                        "name": "style",
                        "value": "默认",
                    },
                    {
                        "name": "aspect_ratio",
                        "value": ratio,
                    },
                ],
                "type": "mmu_img2img_aiweb",
                "inputs": [
                    {
                        "inputType": "URL",
                        "url": image_payload_url,
                        "name": "input",
                    },
                ],
            }
        else:
            payload = {
                "arguments": [
                    {
                        "name": "biz",
                        "value": "klingai",
                    },
                    {"name": "prompt", "value": prompt},
                    {
                        "name": "imageCount",
                        "value": str(count),
                    },
                    {
                        "name": "kolors_version",
                        "value": model_name,
                    },
                    {
                        "name": "style",
                        "value": "默认",
                    },
                    {
                        "name": "aspect_ratio",
                        "value": ratio,
                    },
                ],
                "type": "mmu_txt2img_aiweb",
                "inputs": [],
            }

        if model_name in ("2.0", "2.1"):
            payload["arguments"].append(
                {"name": "img_resolution", "value": "2k" if high_res else "1k"}
            )

        # 重试获取请求ID
        request_id = None
        max_retries = 50  # 最多重试50次
        retry_count = 0

        while not request_id and retry_count < max_retries:
            try:
                if retry_count > 0:
                    print(f"Retrying to get request ID ({retry_count + 1}/{max_retries})...")

                response = self.session.post(
                    self.submit_url,
                    json=payload,
                )
                if not response.ok:
                    print(response.text)
                    raise Exception(f"Error response {str(response)}")
                response_body = response.json()
                # 安全地检查响应数据
                data = response_body.get("data")
                if data and data.get("status") == 7:
                    message = data.get("message")
                    raise Exception(f"Request failed message {message}")
                request_id = (data.get("task") or {}).get("id") if data else None

                if not request_id:
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"Could not get request ID, retrying in 11 seconds ({retry_count}/{max_retries})")
                        # 倒计时显示
                        for countdown in range(11, 0, -1):
                            print(f"⏳ Retry countdown: {countdown} seconds", end='\r')
                            time.sleep(1)
                        print()  # 换行
                    else:
                        raise Exception("Could not get request ID after multiple attempts")
                else:
                    print(f"Successfully got request ID: {request_id}")
                    break

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"Request failed, retrying in 11 seconds: {str(e)}")
                    # 倒计时显示
                    for countdown in range(11, 0, -1):
                        print(f"⏳ Retry countdown: {countdown} seconds", end='\r')
                        time.sleep(1)
                    print()  # 换行
                else:
                    raise Exception(f"Failed after multiple retries: {str(e)}")

        if not request_id:
            raise Exception("Could not get request ID")
        start_wait = time.time()
        print("Waiting for results...")
        while True:
            if int(time.time() - start_wait) > 1200:
                raise Exception("Request timeout")
            image_data, status = self.fetch_metadata(request_id)
            if status == TaskStatus.PENDING:
                print(".", end="", flush=True)
                # spider rule
                time.sleep(2)
            elif status == TaskStatus.FAILED:
                print("Request failed")
                return []
            else:
                result = []
                works = image_data.get("works", [])
                if not works:
                    print("No images found.")
                    return []
                else:
                    for work in works:
                        work: dict
                        resource = work.get("resource", {}).get("resource")

                        # if workId exists fetch resource without watermark (for pytest passing)
                        if "workId" in work.keys():
                            work_id = work["workId"]
                            resource, _ = self.fetch_image_url(
                                work_id, session=self.session
                            )

                        if resource:
                            # sleep for 2s for waiting the video to be ready in kuaishou server
                            time.sleep(2)
                            result.append(resource)
                return result

    def save_images(
        self,
        prompt: str,
        output_dir: str,
        image_path: Optional[str] = None,
        image_url: Optional[str] = None,
        ratio: Literal[
            "1:1", "16:9", "4:3", "3:2", "2:3", "3:4", "9:16", "21:9"
        ] = "1:1",
        count: int = 4,
        model_name: Literal["1.0", "1.5", "2.0", "2.1"] = "2.1",
    ) -> None:
        png_index = 0
        try:
            links = self.get_images(
                prompt, image_path, image_url, ratio, count, model_name
            )
        except Exception as e:
            print(e)
            raise
        with contextlib.suppress(FileExistsError):
            os.mkdir(output_dir)
        print()

        def download_image(link: str, index: int) -> None:
            response = self.session.get(link)
            if response.status_code != 200:
                raise Exception("Could not download image")
            # save response to file
            with open(os.path.join(output_dir, f"{index}.png"), "wb") as output_file:
                output_file.write(response.content)

        threads = []
        for link in links:
            while os.path.exists(os.path.join(output_dir, f"{png_index}.png")):
                png_index += 1
            print(link)
            thread = threading.Thread(target=download_image, args=(link, png_index))
            threads.append(thread)
            thread.start()
            png_index += 1

        # Wait for all threads to complete
        for thread in threads:
            thread.join()


# 主函数，程序的入口点，处理命令行参数并执行相应的生成任务
def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser()
    # 添加认证cookie参数，用于用户身份验证
    parser.add_argument("-U", help="Auth cookie from browser", type=str, default="")
    # 添加图片文件路径参数，用于图生图或图生视频功能
    parser.add_argument(
        "-I", help="image file path if you want use image", type=str, default=""
    )
    # 添加生成类型参数，可选择图片或视频生成
    parser.add_argument(
        "--type",
        help="Type of generation",  # 生成类型说明
        type=str,
        default="image",  # 默认为图片生成
        choices=["image", "video"],  # 限制选择范围
    )
    # 添加模型名称参数，用于视频生成时选择不同版本的模型
    parser.add_argument(
        "--model_name",
        help="Model name for video generation for now(2024.9.22-) its only 1.0 and 1.5",  # 模型版本说明
        type=str,
        default="1.0",  # 默认使用1.0版本
    )
    # 添加提示词参数，这是生成内容的核心描述，必需参数
    parser.add_argument(
        "--prompt",
        help="Prompt to generate images for",  # 提示词说明
        type=str,
        required=True,  # 设为必需参数
    )

    # 添加输出目录参数，指定生成文件的保存位置
    parser.add_argument(
        "--output-dir",
        help="Output directory",  # 输出目录说明
        type=str,
        default="./output",  # 默认输出到当前目录下的output文件夹
    )
    # 添加高质量参数，用于视频生成时启用高质量模式
    parser.add_argument(
        "--high-quality",
        help="Use high quality",  # 高质量模式说明
        action="store_true",  # 布尔标志，存在即为True
    )
    # 添加自动延长参数，用于视频生成时自动延长视频长度
    parser.add_argument(
        "--auto-extend",
        help="Auto extend video",  # 自动延长说明
        action="store_true",  # 布尔标志，存在即为True
    )

    # 解析命令行参数
    args = parser.parse_args()

    # 根据生成类型创建相应的生成器并执行任务
    # 遵循旧版本的代码风格
    if args.type == "image":
        # 如果选择图片生成，创建ImageGen实例
        image_generator = ImageGen(
            # 优先使用环境变量中的cookie，如果没有则使用命令行参数
            os.environ.get("KLING_COOKIE") or args.U,
        )
        # 调用保存图片方法，生成并保存图片到指定目录
        image_generator.save_images(
            prompt=args.prompt,  # 使用用户提供的提示词
            output_dir=args.output_dir,  # 使用指定的输出目录
            image_path=args.I,  # 如果提供了图片路径，则进行图生图
        )
        # 打印用户账户的积分余额
        print(
            f"The balance of points in your account is: {image_generator.get_account_point()}"
        )
    else:
        # 如果选择视频生成，创建VideoGen实例
        video_generator = VideoGen(
            # 优先使用环境变量中的cookie，如果没有则使用命令行参数
            os.environ.get("KLING_COOKIE") or args.U,
        )
        # 调用保存视频方法，生成并保存视频到指定目录
        video_generator.save_video(
            prompt=args.prompt,  # 使用用户提供的提示词
            output_dir=args.output_dir,  # 使用指定的输出目录
            image_path=args.I,  # 如果提供了图片路径，则进行图生视频
            is_high_quality=args.high_quality,  # 是否使用高质量模式
            auto_extend=args.auto_extend,  # 是否自动延长视频
            model_name=args.model_name,  # 使用指定的模型版本
        )
        # 打印用户账户的积分余额
        print(
            f"The balance of points in your account is: {video_generator.get_account_point()}"
        )


# 当此文件作为主程序运行时（而不是被导入时）执行main函数
if __name__ == "__main__":
    # 调用主函数启动程序
    main()
