@echo off
echo 🚀 启动Chrome调试模式...
echo.
echo 💡 这将启动一个带调试端口的Chrome实例
echo    请在Chrome中登录即梦AI，然后运行Python脚本
echo.

REM 关闭现有Chrome进程
taskkill /f /im chrome.exe 2>nul

REM 等待进程完全关闭
timeout /t 2 /nobreak >nul

REM 启动带调试端口的Chrome
"C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir=chrome_debug_profile https://jimeng.jianying.com/ai-tool/generate?type=video

echo.
echo ✅ Chrome已启动
echo 💡 请在Chrome中登录即梦AI，然后运行: python jimeng_playwright_video.py
pause
