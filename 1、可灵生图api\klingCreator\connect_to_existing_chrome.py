#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连接到现有Chrome浏览器并上传首尾帧
保持原有登录状态，不打开新窗口
"""

import asyncio
import os
import sys
import json
import aiohttp
from pathlib import Path
from playwright.async_api import async_playwright


async def find_chrome_tabs():
    """查找现有Chrome标签页"""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:9222/json') as response:
                if response.status == 200:
                    tabs = await response.json()
                    return tabs
                else:
                    return None
    except:
        return None


async def connect_to_existing_chrome(first_frame_path: str, last_frame_path: str, prompt: str = ""):
    """
    连接到现有Chrome浏览器
    
    Args:
        first_frame_path: 首帧图片路径
        last_frame_path: 尾帧图片路径
        prompt: 提示词
    """
    
    print("🎬 连接现有Chrome浏览器上传首尾帧")
    print("=" * 50)
    print(f"📸 首帧: {Path(first_frame_path).name}")
    print(f"📸 尾帧: {Path(last_frame_path).name}")
    print(f"💭 提示词: {prompt}")
    print()
    
    # 验证文件存在
    if not os.path.exists(first_frame_path):
        print(f"❌ 首帧图片不存在: {first_frame_path}")
        return False
    
    if not os.path.exists(last_frame_path):
        print(f"❌ 尾帧图片不存在: {last_frame_path}")
        return False
    
    # 首先检查Chrome是否开启了调试端口
    print("🔍 检查Chrome调试端口...")
    tabs = await find_chrome_tabs()
    
    if not tabs:
        print("❌ 无法连接到Chrome调试端口")
        print("💡 请按以下步骤操作:")
        print("   1. 关闭所有Chrome窗口")
        print("   2. 运行以下命令启动Chrome:")
        print('   "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port=9222')
        print("   3. 在Chrome中登录即梦AI")
        print("   4. 重新运行此脚本")
        return False
    
    print(f"✅ 找到 {len(tabs)} 个Chrome标签页")
    
    # 查找即梦AI标签页
    jimeng_tab = None
    for tab in tabs:
        if 'jimeng.jianying.com' in tab.get('url', ''):
            jimeng_tab = tab
            print(f"✅ 找到即梦AI标签页: {tab['title']}")
            break
    
    if not jimeng_tab:
        print("⚠️ 未找到即梦AI标签页")
        print("💡 请在Chrome中打开: https://jimeng.jianying.com/ai-tool/generate?type=video")
        
        # 尝试在现有Chrome中打开新标签页
        try:
            # 使用第一个标签页打开即梦AI
            if tabs:
                first_tab = tabs[0]
                ws_url = first_tab['webSocketDebuggerUrl']
                
                async with async_playwright() as p:
                    browser = await p.chromium.connect_over_cdp(ws_url)
                    context = browser.contexts[0]
                    page = await context.new_page()
                    await page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
                    await page.wait_for_load_state('networkidle')
                    
                    print("✅ 已在现有Chrome中打开即梦AI页面")
                    
                    # 执行上传操作
                    return await perform_upload_in_page(page, first_frame_path, last_frame_path, prompt)
        except Exception as e:
            print(f"❌ 打开新标签页失败: {e}")
            return False
    else:
        # 连接到现有的即梦AI标签页
        try:
            ws_url = jimeng_tab['webSocketDebuggerUrl']
            
            async with async_playwright() as p:
                browser = await p.chromium.connect_over_cdp(ws_url)
                
                # 获取页面
                pages = []
                for context in browser.contexts:
                    pages.extend(context.pages)
                
                # 找到即梦AI页面
                jimeng_page = None
                for page in pages:
                    if 'jimeng.jianying.com' in page.url:
                        jimeng_page = page
                        break
                
                if jimeng_page:
                    print("✅ 连接到即梦AI页面")
                    
                    # 确保在视频生成页面
                    if 'type=video' not in jimeng_page.url:
                        await jimeng_page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
                        await jimeng_page.wait_for_load_state('networkidle')
                    
                    # 执行上传操作
                    return await perform_upload_in_page(jimeng_page, first_frame_path, last_frame_path, prompt)
                else:
                    print("❌ 无法找到即梦AI页面")
                    return False
                    
        except Exception as e:
            print(f"❌ 连接到现有标签页失败: {e}")
            return False


async def perform_upload_in_page(page, first_frame_path, last_frame_path, prompt):
    """在页面中执行上传操作"""
    try:
        print("🔍 查找页面元素...")
        
        # 等待页面加载
        await asyncio.sleep(3)
        
        # 查找文件上传元素
        file_inputs = await page.query_selector_all('input[type="file"]')
        print(f"找到 {len(file_inputs)} 个文件上传元素")
        
        if len(file_inputs) < 2:
            print("⚠️ 文件上传元素不足，尝试激活上传区域...")
            
            # 尝试点击上传相关的元素
            upload_selectors = [
                'div[class*="upload"]',
                'button[class*="upload"]',
                '.upload-area',
                '.upload-zone',
                'div[role="button"]'
            ]
            
            for selector in upload_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        if await element.is_visible():
                            await element.click()
                            await asyncio.sleep(1)
                            print(f"点击了: {selector}")
                except:
                    continue
            
            # 重新查找
            await asyncio.sleep(2)
            file_inputs = await page.query_selector_all('input[type="file"]')
            print(f"激活后找到 {len(file_inputs)} 个文件上传元素")
        
        if len(file_inputs) >= 2:
            print("📤 开始上传图片...")
            
            # 上传首帧
            print("📤 上传首帧...")
            await file_inputs[0].set_input_files(first_frame_path)
            await handle_upload_dialog(page, "首帧")
            
            # 上传尾帧
            print("📤 上传尾帧...")
            await file_inputs[1].set_input_files(last_frame_path)
            await handle_upload_dialog(page, "尾帧")
            
            # 设置提示词
            if prompt:
                print("💭 设置提示词...")
                await set_prompt_text(page, prompt)
            
            # 设置10秒时长
            print("⏱️ 设置10秒时长...")
            await set_video_duration(page)
            
            # 点击生成按钮
            print("🚀 点击生成按钮...")
            success = await click_generate(page)
            
            if success:
                print("✅ 视频生成已开始！")
                print("💡 请在浏览器中查看生成进度")
                return True
            else:
                print("❌ 生成按钮点击失败")
                return False
        else:
            print("❌ 未找到足够的文件上传元素")
            return False
            
    except Exception as e:
        print(f"❌ 页面操作失败: {e}")
        return False


async def handle_upload_dialog(page, frame_type):
    """处理上传后的对话框"""
    print(f"⏳ 处理{frame_type}上传对话框...")
    
    # 等待最多10秒处理对话框
    for i in range(20):
        try:
            # 查找所有可见按钮
            buttons = await page.query_selector_all('button')
            
            for button in buttons:
                if await button.is_visible():
                    text = await button.inner_text()
                    text_lower = text.strip().lower() if text else ""
                    
                    # 检查是否是确认/保存按钮
                    confirm_keywords = ['保存', '确定', '确认', 'save', 'ok', '完成', 'done']
                    if any(keyword in text_lower for keyword in confirm_keywords):
                        await button.click()
                        print(f"✅ {frame_type}点击确认按钮: {text}")
                        await asyncio.sleep(2)
                        return True
            
        except:
            pass
        
        await asyncio.sleep(0.5)
    
    print(f"✅ {frame_type}上传处理完成")
    return True


async def set_prompt_text(page, prompt):
    """设置提示词"""
    try:
        # 查找文本输入区域
        text_inputs = await page.query_selector_all('textarea, input[type="text"]')
        
        for input_elem in text_inputs:
            if await input_elem.is_visible():
                placeholder = await input_elem.get_attribute('placeholder')
                if placeholder and ('提示' in placeholder or 'prompt' in placeholder.lower()):
                    await input_elem.fill(prompt)
                    print("✅ 提示词设置完成")
                    return True
        
        # 如果没找到特定的，使用第一个可见的textarea
        textareas = await page.query_selector_all('textarea')
        for textarea in textareas:
            if await textarea.is_visible():
                await textarea.fill(prompt)
                print("✅ 提示词设置完成（使用文本区域）")
                return True
        
        print("⚠️ 未找到提示词输入框")
        return False
        
    except Exception as e:
        print(f"⚠️ 设置提示词失败: {e}")
        return False


async def set_video_duration(page):
    """设置视频时长为10秒"""
    try:
        # 查找10秒按钮
        buttons = await page.query_selector_all('button')
        for button in buttons:
            try:
                if await button.is_visible():
                    text = await button.inner_text()
                    if text and ('10' in text and ('秒' in text or 's' in text.lower())):
                        await button.click()
                        print(f"✅ 设置时长为10秒: {text}")
                        return True
            except:
                continue
        
        print("⚠️ 未找到10秒时长设置")
        return True
        
    except Exception as e:
        print(f"⚠️ 设置时长失败: {e}")
        return True


async def click_generate(page):
    """点击生成按钮"""
    try:
        # 查找生成按钮
        buttons = await page.query_selector_all('button')
        for button in buttons:
            try:
                if await button.is_visible() and await button.is_enabled():
                    text = await button.inner_text()
                    if text and ('生成' in text or 'generate' in text.lower() or '创建' in text):
                        await button.click()
                        print(f"✅ 点击生成按钮: {text}")
                        return True
            except:
                continue
        
        print("❌ 未找到生成按钮")
        return False
        
    except Exception as e:
        print(f"❌ 点击生成按钮失败: {e}")
        return False


async def main():
    """主函数"""
    
    # 测试图片路径
    first_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座孤立在纯黑色空间中的蒸汽朋克机械堡垒_建筑内部的锅炉透过格栅.png"
    last_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (4).png"
    prompt = "从蒸汽朋克机械堡垒平滑过渡到生物发光植物建筑"
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) >= 3:
        first_frame = sys.argv[1]
        last_frame = sys.argv[2]
        prompt = sys.argv[3] if len(sys.argv) > 3 else ""
    
    print("💡 使用说明:")
    print("   1. 请先启动Chrome并开启调试端口:")
    print('      "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --remote-debugging-port=9222')
    print("   2. 在Chrome中登录即梦AI")
    print("   3. 运行此脚本")
    print()
    
    success = await connect_to_existing_chrome(first_frame, last_frame, prompt)
    
    if success:
        print("\n🎉 操作完成！")
    else:
        print("\n❌ 操作失败")


if __name__ == "__main__":
    asyncio.run(main())
