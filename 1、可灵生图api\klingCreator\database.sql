-- 可灵AI图生图数据库设计
-- 创建数据库
CREATE DATABASE IF NOT EXISTS kling_ai DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE kling_ai;

-- 1. 生成记录表
CREATE TABLE IF NOT EXISTS generation_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    original_image_path VARCHAR(500) NOT NULL COMMENT '原始图片路径',
    original_image_name VARCHAR(255) NOT NULL COMMENT '原始图片文件名',
    prompt TEXT NOT NULL COMMENT '提示词',
    ai_provider ENUM('kling', 'jimeng') NOT NULL DEFAULT 'kling' COMMENT 'AI提供商',
    style_name VARCHAR(100) NOT NULL COMMENT '风格名称',
    status ENUM('pending', 'generating', 'completed', 'failed') NOT NULL DEFAULT 'pending' COMMENT '生成状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_created_at (created_at),
    INDEX idx_ai_provider (ai_provider),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片生成记录表';

-- 2. 生成结果表
CREATE TABLE IF NOT EXISTS generated_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    record_id INT NOT NULL COMMENT '生成记录ID',
    image_path VARCHAR(500) NOT NULL COMMENT '生成图片路径',
    image_name VARCHAR(255) NOT NULL COMMENT '生成图片文件名',
    image_url VARCHAR(1000) COMMENT '原始图片URL',
    file_size INT COMMENT '文件大小(字节)',
    width INT COMMENT '图片宽度',
    height INT COMMENT '图片高度',
    likes INT DEFAULT 0 COMMENT '点赞数',
    dislikes INT DEFAULT 0 COMMENT '点踩数',
    is_approved TINYINT(1) DEFAULT NULL COMMENT '审核状态: NULL-未审核, 1-通过, 0-拒绝',
    approved_by VARCHAR(100) COMMENT '审核人',
    approved_at TIMESTAMP NULL COMMENT '审核时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (record_id) REFERENCES generation_records(id) ON DELETE CASCADE,
    INDEX idx_record_id (record_id),
    INDEX idx_is_approved (is_approved),
    INDEX idx_likes (likes),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生成图片表';

-- 3. 用户操作记录表
CREATE TABLE IF NOT EXISTS user_actions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    image_id INT NOT NULL COMMENT '图片ID',
    action_type ENUM('like', 'dislike', 'view', 'download') NOT NULL COMMENT '操作类型',
    user_ip VARCHAR(45) COMMENT '用户IP',
    user_agent TEXT COMMENT '用户代理',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    FOREIGN KEY (image_id) REFERENCES generated_images(id) ON DELETE CASCADE,
    INDEX idx_image_id (image_id),
    INDEX idx_action_type (action_type),
    INDEX idx_created_at (created_at),
    UNIQUE KEY unique_user_action (image_id, user_ip, action_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户操作记录表';

-- 4. 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 插入默认配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('default_ai_provider', 'kling', '默认AI提供商'),
('max_daily_generations', '100', '每日最大生成数量'),
('auto_approve', '0', '是否自动审核通过'),
('storage_path', './web_output', '图片存储路径');

-- 创建视图：生成记录详情
CREATE VIEW generation_details AS
SELECT 
    gr.id as record_id,
    gr.original_image_name,
    gr.original_image_path,
    gr.prompt,
    gr.ai_provider,
    gr.style_name,
    gr.status as generation_status,
    gr.created_at as generation_time,
    gi.id as image_id,
    gi.image_name,
    gi.image_path,
    gi.likes,
    gi.dislikes,
    gi.is_approved,
    gi.approved_by,
    gi.approved_at,
    (gi.likes - gi.dislikes) as score
FROM generation_records gr
LEFT JOIN generated_images gi ON gr.id = gi.record_id
ORDER BY gr.created_at DESC, gi.created_at DESC;
