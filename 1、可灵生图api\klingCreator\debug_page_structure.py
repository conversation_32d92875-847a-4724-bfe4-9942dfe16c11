#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试页面结构
"""

import asyncio
from playwright.async_api import async_playwright

async def debug_page_structure():
    """调试页面结构"""
    print("🔍 调试即梦AI页面结构")
    print("=" * 50)
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 导航到页面
        await page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
        await page.wait_for_load_state('networkidle')
        
        # 检查是否需要切换到视频生成模式
        try:
            image_gen_button = await page.query_selector('text=图片生成')
            if image_gen_button:
                print("🔄 切换到视频生成模式...")
                await image_gen_button.click()
                await page.wait_for_timeout(3000)
        except:
            pass
        
        # 等待页面稳定
        await page.wait_for_timeout(5000)
        
        print("\n📋 页面中包含'首帧'的元素:")
        print("-" * 30)
        
        # 查找所有包含"首帧"的元素
        elements = await page.query_selector_all('*')
        frame_elements = []
        
        for element in elements:
            try:
                text_content = await element.text_content()
                if text_content and '首帧' in text_content:
                    tag_name = await element.evaluate('el => el.tagName')
                    class_name = await element.get_attribute('class') or ''
                    cursor = await element.get_attribute('cursor') or ''
                    role = await element.get_attribute('role') or ''
                    
                    frame_elements.append({
                        'tag': tag_name,
                        'text': text_content.strip(),
                        'class': class_name,
                        'cursor': cursor,
                        'role': role
                    })
            except:
                continue
        
        for i, elem in enumerate(frame_elements):
            print(f"{i+1}. {elem['tag']} - '{elem['text'][:50]}...'")
            if elem['class']:
                print(f"   class: {elem['class']}")
            if elem['cursor']:
                print(f"   cursor: {elem['cursor']}")
            if elem['role']:
                print(f"   role: {elem['role']}")
            print()
        
        print("\n📋 页面中包含'尾帧'的元素:")
        print("-" * 30)
        
        # 查找所有包含"尾帧"的元素
        last_frame_elements = []
        
        for element in elements:
            try:
                text_content = await element.text_content()
                if text_content and '尾帧' in text_content:
                    tag_name = await element.evaluate('el => el.tagName')
                    class_name = await element.get_attribute('class') or ''
                    cursor = await element.get_attribute('cursor') or ''
                    role = await element.get_attribute('role') or ''
                    
                    last_frame_elements.append({
                        'tag': tag_name,
                        'text': text_content.strip(),
                        'class': class_name,
                        'cursor': cursor,
                        'role': role
                    })
            except:
                continue
        
        for i, elem in enumerate(last_frame_elements):
            print(f"{i+1}. {elem['tag']} - '{elem['text'][:50]}...'")
            if elem['class']:
                print(f"   class: {elem['class']}")
            if elem['cursor']:
                print(f"   cursor: {elem['cursor']}")
            if elem['role']:
                print(f"   role: {elem['role']}")
            print()
        
        print("\n📋 页面中的文件输入框:")
        print("-" * 30)
        
        # 查找文件输入框
        file_inputs = await page.query_selector_all('input[type="file"]')
        for i, input_elem in enumerate(file_inputs):
            try:
                name = await input_elem.get_attribute('name') or ''
                id_attr = await input_elem.get_attribute('id') or ''
                class_name = await input_elem.get_attribute('class') or ''
                
                print(f"{i+1}. input[type='file']")
                if name:
                    print(f"   name: {name}")
                if id_attr:
                    print(f"   id: {id_attr}")
                if class_name:
                    print(f"   class: {class_name}")
                print()
            except:
                continue
        
        print("\n📋 页面中的按钮元素:")
        print("-" * 30)
        
        # 查找所有按钮
        buttons = await page.query_selector_all('button, [role="button"], [cursor="pointer"]')
        for i, button in enumerate(buttons[:10]):  # 只显示前10个
            try:
                text_content = await button.text_content()
                tag_name = await button.evaluate('el => el.tagName')
                class_name = await button.get_attribute('class') or ''
                
                if text_content and text_content.strip():
                    print(f"{i+1}. {tag_name} - '{text_content.strip()[:30]}...'")
                    if class_name:
                        print(f"   class: {class_name}")
                    print()
            except:
                continue
        
        # 等待用户查看
        print("\n⏳ 等待30秒供查看页面...")
        await page.wait_for_timeout(30000)
        
        await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_page_structure())
