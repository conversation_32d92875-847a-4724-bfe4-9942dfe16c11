// 全局变量
let uploadedFile = null;
let selectedPrompts = [];
let currentUploadId = null;
let currentFileMd5 = null;
let currentTasks = [];
let statusCheckInterval = null;

// DOM元素
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const uploadedImage = document.getElementById('uploadedImage');
const previewImage = document.getElementById('previewImage');
const imageName = document.getElementById('imageName');
const removeImage = document.getElementById('removeImage');
const generateBtn = document.getElementById('generateBtn');
const progressInfo = document.getElementById('progressInfo');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const resultsSection = document.getElementById('resultsSection');
const resultsGrid = document.getElementById('resultsGrid');
const clearResultsBtn = document.getElementById('clearResultsBtn');

const imageModal = document.getElementById('imageModal');
const modalClose = document.getElementById('modalClose');
const modalImage = document.getElementById('modalImage');
const modalTitle = document.getElementById('modalTitle');
const modalDownload = document.getElementById('modalDownload');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    loadBalance();

    // 检查是否是从详情页面重新使用图片
    checkReuseImageParams();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 文件上传相关
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
    removeImage.addEventListener('click', removeUploadedImage);
    
    // 提示词选择
    document.querySelectorAll('.prompt-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', handlePromptSelection);
    });
    
    // 生成按钮
    generateBtn.addEventListener('click', generateImages);

    // 清空结果按钮
    clearResultsBtn.addEventListener('click', clearAllResults);

    // 模态框
    modalClose.addEventListener('click', closeModal);
    imageModal.addEventListener('click', (e) => {
        if (e.target === imageModal) closeModal();
    });
}

// 加载账户余额
async function loadBalance() {
    try {
        const response = await fetch('/api/balance');
        const data = await response.json();

        if (data.balance !== undefined) {
            document.getElementById('balance').textContent = data.balance;
        } else {
            document.getElementById('balance').textContent = '获取失败';
        }
    } catch (error) {
        console.error('获取余额失败:', error);
        document.getElementById('balance').textContent = '获取失败';
    }
}

// 显示积分不足警告
function showBalanceWarning(data) {
    // 创建遮罩层
    const overlay = document.createElement('div');
    overlay.className = 'balance-warning-overlay';

    // 创建警告框
    const warning = document.createElement('div');
    warning.className = 'balance-warning';

    const shortage = (data.required_points - data.current_balance).toFixed(1);

    warning.innerHTML = `
        <h3>💰 积分余额不足</h3>
        <p>您的积分余额不足以完成此次生成任务</p>

        <div class="balance-details">
            <div class="balance-item">
                <span>当前余额:</span>
                <span>${data.current_balance} 积分</span>
            </div>
            <div class="balance-item">
                <span>需要积分:</span>
                <span>${data.required_points} 积分</span>
            </div>
            <div class="balance-item">
                <span>缺少积分:</span>
                <span>${shortage} 积分</span>
            </div>
        </div>

        <p>💡 建议：</p>
        <ul style="text-align: left; margin: 10px 0;">
            <li>减少生成图片的数量</li>
            <li>前往可灵AI官网充值积分</li>
            <li>等待每日免费积分刷新</li>
        </ul>

        <div class="buttons">
            <button onclick="closeBalanceWarning()">我知道了</button>
            <button class="btn-secondary" onclick="window.open('https://klingai.kuaishou.com/', '_blank')">前往充值</button>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(overlay);
    document.body.appendChild(warning);

    // 点击遮罩层关闭
    overlay.addEventListener('click', closeBalanceWarning);
}

// 关闭积分警告
function closeBalanceWarning() {
    const overlay = document.querySelector('.balance-warning-overlay');
    const warning = document.querySelector('.balance-warning');

    if (overlay) overlay.remove();
    if (warning) warning.remove();
}

// 文件拖拽处理
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

// 文件选择处理
function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

// 处理文件
async function handleFile(file) {
    // 检查文件类型
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        alert('不支持的文件格式！请选择图片文件。');
        return;
    }
    
    // 检查文件大小 (16MB)
    if (file.size > 16 * 1024 * 1024) {
        alert('文件太大！请选择小于16MB的图片。');
        return;
    }
    
    // 上传文件
    const formData = new FormData();
    formData.append('file', file);
    
    try {
        console.log('开始上传文件:', file.name);

        const response = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });

        console.log('响应状态:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP错误: ${response.status}`);
        }

        const data = await response.json();
        console.log('响应数据:', data);

        if (data.success) {
            uploadedFile = {
                filename: data.filename,
                originalName: data.original_name
            };

            // 保存数据库相关信息
            currentUploadId = data.upload_id;
            currentFileMd5 = data.file_md5;

            console.log('上传成功，文件信息:', uploadedFile);
            console.log('数据库ID:', currentUploadId, 'MD5:', currentFileMd5);

            // 显示预览
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                imageName.textContent = file.name;
                uploadArea.style.display = 'none';
                uploadedImage.style.display = 'flex';
                updateGenerateButton();
                console.log('预览显示完成');
            };
            reader.readAsDataURL(file);
        } else {
            console.error('上传失败:', data.error);
            alert('上传失败: ' + data.error);
        }
    } catch (error) {
        console.error('上传异常:', error);
        alert('上传失败: ' + error.message);
    }
}

// 移除上传的图片
function removeUploadedImage() {
    uploadedFile = null;
    uploadArea.style.display = 'block';
    uploadedImage.style.display = 'none';
    fileInput.value = '';
    updateGenerateButton();
}

// 处理提示词选择
function handlePromptSelection(e) {
    const checkbox = e.target;
    const card = checkbox.closest('.prompt-card');
    const promptId = card.dataset.id;
    const promptText = card.querySelector('.prompt-full').textContent;
    const promptName = card.querySelector('.prompt-title').textContent;
    
    if (checkbox.checked) {
        // 添加到选中列表
        selectedPrompts.push({
            id: promptId,
            name: promptName,
            prompt: promptText
        });
        card.classList.add('selected');
    } else {
        // 从选中列表移除
        selectedPrompts = selectedPrompts.filter(p => p.id !== promptId);
        card.classList.remove('selected');
    }
    
    updateGenerateButton();
}



// 更新生成按钮状态
function updateGenerateButton() {
    const canGenerate = uploadedFile && selectedPrompts.length > 0;
    generateBtn.disabled = !canGenerate;
    
    if (canGenerate) {
        generateBtn.innerHTML = `🎨 生成 ${selectedPrompts.length} 张图片`;
    } else {
        generateBtn.innerHTML = '🎨 开始生成';
    }
}

// 并发生成图片
async function generateImages() {
    if (!uploadedFile || selectedPrompts.length === 0) {
        alert('请先上传图片并选择提示词！');
        return;
    }

    // 显示进度
    progressInfo.style.display = 'block';
    generateBtn.disabled = true;
    resultsSection.style.display = 'block';

    // 不清空已有结果，保留原有的生成记录

    // 构建提示词数据，包含风格信息
    const promptsData = selectedPrompts.map(p => ({
        prompt: p.prompt,
        style_name: p.name
    }));

    try {
        // 准备请求数据
        const requestData = {
            filename: uploadedFile.filename,
            original_name: uploadedFile.originalName,
            upload_id: currentUploadId,
            file_md5: currentFileMd5,
            prompts: promptsData
        };

        console.log('发送生成请求，数据:', requestData);

        // 发送并发生成请求
        const response = await fetch('/api/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const data = await response.json();

        if (data.success) {
            // 为新任务创建占位符，使用任务ID
            initializeResultsDisplayWithTaskIds(data.task_ids, selectedPrompts);

            // 将新任务ID添加到现有任务列表中，而不是替换
            if (currentTasks && currentTasks.length > 0) {
                // 如果已有任务在进行，将新任务添加到列表前面
                currentTasks = [...data.task_ids, ...currentTasks];
                console.log(`✅ 已添加 ${data.total_tasks} 个新任务到现有任务列表`);
            } else {
                // 如果没有现有任务，直接设置新任务
                currentTasks = data.task_ids;
                console.log(`✅ 已提交 ${data.total_tasks} 个并发任务`);
            }

            // 确保状态检查正在运行
            ensureStatusCheckRunning();

            console.log(`📋 当前任务总数: ${currentTasks.length}`);
        } else {
            // 检查是否是积分不足错误
            if (data.insufficient_balance) {
                showBalanceWarning(data);
                console.error('积分不足:', data);

                // 刷新积分余额显示
                loadBalance();
            } else {
                throw new Error(data.error || '提交任务失败');
            }
        }
    } catch (error) {
        console.error('生成失败:', error);
        // 如果不是积分不足错误，显示通用错误信息
        if (!error.message.includes('积分余额不足')) {
            alert('生成失败: ' + error.message);
        }
        generateBtn.disabled = false;
        progressInfo.style.display = 'none';
    }
}

// 初始化结果显示区域（使用任务ID）
function initializeResultsDisplayWithTaskIds(taskIds, prompts) {
    // 不清空已有结果，保留原有的生成记录

    // 为每个新任务创建占位符，按逆序添加到最前面，保持正确的显示顺序
    for (let i = taskIds.length - 1; i >= 0; i--) {
        const taskId = taskIds[i];
        const prompt = prompts[i];
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item result-pending';
        resultItem.id = `result-task-${taskId}`;  // 使用任务ID
        resultItem.dataset.taskId = taskId;  // 保存任务ID到数据属性

        resultItem.innerHTML = `
            <div class="result-placeholder">
                <div class="loading-spinner">🎨</div>
                <div class="result-info">
                    <div class="result-title">${prompt.name}</div>
                    <div class="result-status">等待开始...</div>
                    <div class="result-time">用时: 0秒</div>
                </div>
            </div>
        `;

        // 将新的结果项添加到最前面
        if (resultsGrid.firstChild) {
            resultsGrid.insertBefore(resultItem, resultsGrid.firstChild);
        } else {
            resultsGrid.appendChild(resultItem);
        }
    }
}

// 初始化结果显示区域（保留兼容性）
function initializeResultsDisplay() {
    // 这个函数保留用于兼容性，但现在主要使用initializeResultsDisplayWithTaskIds
    console.log('使用旧的initializeResultsDisplay函数');
}

// 开始状态检查
function startStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }

    statusCheckInterval = setInterval(checkTaskStatus, 2000); // 每2秒检查一次
    updateProgressInfo();
}

// 确保状态检查正在运行（不重置现有检查）
function ensureStatusCheckRunning() {
    if (!statusCheckInterval) {
        // 只有在没有状态检查运行时才启动新的
        statusCheckInterval = setInterval(checkTaskStatus, 2000);
        console.log('🔄 启动状态检查');
    } else {
        console.log('🔄 状态检查已在运行，继续监控所有任务');
    }
    updateProgressInfo();
}

// 检查任务状态
async function checkTaskStatus() {
    if (!currentTasks || currentTasks.length === 0) {
        return;
    }

    try {
        const response = await fetch('/api/task_status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                task_ids: currentTasks
            })
        });

        const data = await response.json();

        if (data.success) {
            updateTaskDisplay(data.tasks);

            // 从currentTasks中移除已完成的任务
            const completedTaskIds = data.tasks
                .filter(task => task.status === 'completed' || task.status === 'failed')
                .map(task => task.task_id);

            if (completedTaskIds.length > 0) {
                currentTasks = currentTasks.filter(taskId => !completedTaskIds.includes(taskId));
                console.log(`📋 移除 ${completedTaskIds.length} 个已完成任务，剩余 ${currentTasks.length} 个任务`);
            }

            // 检查是否所有任务完成
            if (currentTasks.length === 0) {
                stopStatusCheck();
                onAllTasksCompleted();
            } else {
                // 更新进度信息显示剩余任务数
                updateProgressInfo();
            }
        }

    } catch (error) {
        console.error('状态检查失败:', error);
    }
}

// 更新任务显示
function updateTaskDisplay(tasks) {
    tasks.forEach(task => {
        const resultItem = document.getElementById(`result-task-${task.task_id}`);
        if (!resultItem) {
            console.log(`⚠️ 找不到任务 ${task.task_id} 的DOM元素`);
            return;
        }

        const statusElement = resultItem.querySelector('.result-status');
        const timeElement = resultItem.querySelector('.result-time');

        // 更新状态和时间
        if (statusElement) {
            statusElement.textContent = task.message || task.status;
        }

        if (timeElement) {
            if (task.status === 'completed' || task.status === 'failed') {
                // 完成或失败时显示总用时
                timeElement.textContent = `用时: ${task.total_time || 0}秒`;
            } else {
                // 进行中时始终显示当前用时，无论是否获取到ID
                const currentTime = task.elapsed_time || 0;
                if (task.status === 'waiting' || task.status === 'generating' || task.status === 'pending') {
                    if (task.message && task.message.includes('秒后重试')) {
                        // 重试倒计时状态
                        timeElement.textContent = `已用时: ${currentTime}秒 (重试中)`;
                    } else if (task.message && task.message.includes('等待间隔')) {
                        // 等待间隔状态
                        timeElement.textContent = `已用时: ${currentTime}秒 (等待中)`;
                    } else {
                        // 正常生成状态
                        timeElement.textContent = `已用时: ${currentTime}秒`;
                    }
                } else {
                    // 其他状态也显示用时
                    timeElement.textContent = `已用时: ${currentTime}秒`;
                }
            }
        }

        // 如果任务完成，显示结果
        if (task.status === 'completed' && task.filename) {
            displayCompletedTask(task, resultItem);
        } else if (task.status === 'failed') {
            displayFailedTask(task, resultItem);
        }
    });

    updateProgressInfo();
}

// 显示完成的任务
function displayCompletedTask(task, resultItem) {
    resultItem.className = 'result-item result-success';

    // 创建安全的HTML结构，避免中文字符在onclick中的问题
    resultItem.innerHTML = `
        <div class="result-image-container" data-image-src="/api/image/${encodeURIComponent(task.filename)}" data-style-name="${encodeURIComponent(task.style_name)}">
            <img src="/api/image/${encodeURIComponent(task.filename)}" alt="生成的图片" class="result-image clickable-image">
            ${task.generated_id ? `
                <div class="result-corner-actions">
                    <button class="btn-favorite" data-generated-id="${task.generated_id}" title="收藏这张图片">☆</button>
                    <button class="btn-remove" data-generated-id="${task.generated_id}" title="标记删除">🗂️</button>
                </div>
            ` : ''}
        </div>
        <div class="result-info">
            <div class="result-title">${task.style_name}</div>
            <div class="result-status">✅ 生成完成</div>
            <div class="result-time">用时: ${task.total_time}秒</div>
        </div>
    `;

    // 使用JavaScript事件绑定，避免HTML属性中的中文问题
    const imageElement = resultItem.querySelector('.clickable-image');
    if (imageElement) {
        imageElement.addEventListener('click', function() {
            const imageContainer = this.parentElement;
            const imageSrc = imageContainer.getAttribute('data-image-src');
            const styleName = decodeURIComponent(imageContainer.getAttribute('data-style-name'));
            openImageModal(imageSrc, styleName);
        });

        // 添加鼠标指针样式
        imageElement.style.cursor = 'pointer';
    }

    // 绑定收藏和标记删除按钮事件
    const likeBtn = resultItem.querySelector('.btn-favorite');
    const dislikeBtn = resultItem.querySelector('.btn-remove');

    if (likeBtn) {
        likeBtn.addEventListener('click', function(event) {
            event.stopPropagation();
            const generatedId = this.getAttribute('data-generated-id');
            likeImage(generatedId, this, event);
        });
    }

    if (dislikeBtn) {
        dislikeBtn.addEventListener('click', function(event) {
            event.stopPropagation();
            const generatedId = this.getAttribute('data-generated-id');
            dislikeImage(generatedId, this, event);
        });
    }


}

// 显示失败的任务
function displayFailedTask(task, resultItem) {
    resultItem.className = 'result-item result-error';

    resultItem.innerHTML = `
        <div class="result-error-content">
            <div class="error-icon">❌</div>
            <div class="result-info">
                <div class="result-title">${task.style_name}</div>
                <div class="result-status">生成失败</div>
                <div class="result-time">用时: ${task.total_time || 0}秒</div>
                <div class="error-message">${task.error || '未知错误'}</div>
            </div>
        </div>
    `;
}

// 更新进度信息
function updateProgressInfo() {
    if (!currentTasks || currentTasks.length === 0) {
        progressInfo.style.display = 'none';
        return;
    }

    progressInfo.innerHTML = `
        <div class="progress-text">🚀 正在并发生成 ${currentTasks.length} 张图片...</div>
        <div class="progress-note">每张图片完成后会立即显示</div>
    `;
}

// 停止状态检查
function stopStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
        statusCheckInterval = null;
    }
}

// 所有任务完成后的处理
function onAllTasksCompleted() {
    generateBtn.disabled = false;
    progressInfo.style.display = 'none';

    // 清理已完成的任务
    clearCompletedTasks();

    // 刷新余额
    loadBalance();

    console.log('🎉 所有生成任务完成');
}

// 清理已完成的任务
async function clearCompletedTasks() {
    try {
        await fetch('/api/clear_completed_tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        currentTasks = [];
    } catch (error) {
        console.error('清理任务失败:', error);
    }
}

// 显示结果（保留兼容性）
function displayResults(results) {
    resultsSection.style.display = 'block';
    // 不清空已有结果，保留原有的生成记录
    // resultsGrid.innerHTML = '';

    // 按逆序处理结果，保持正确的显示顺序
    for (let index = results.length - 1; index >= 0; index--) {
        const result = results[index];
        const resultItem = document.createElement('div');
        resultItem.className = 'result-item';
        
        if (result.success) {
            // 创建图片容器
            const imageContainer = document.createElement('div');
            imageContainer.className = 'result-image-container';

            const img = document.createElement('img');
            img.src = `/api/image/${result.filename}`;
            img.alt = '生成的图片';
            img.className = 'result-image clickable-image';
            img.style.cursor = 'pointer';
            img.onclick = () => openImageModal(`/api/image/${result.filename}`, result.style_name || selectedPrompts[index].name);

            imageContainer.appendChild(img);

            // 添加收藏和标记删除按钮（如果有generated_id）
            if (result.generated_id) {
                const actionsDiv = document.createElement('div');
                actionsDiv.className = 'result-corner-actions';

                const likeBtn = document.createElement('button');
                likeBtn.className = 'btn-favorite';
                likeBtn.innerHTML = '☆';  // 空心星
                likeBtn.title = '收藏这张图片';
                likeBtn.setAttribute('data-generated-id', result.generated_id);
                likeBtn.onclick = (e) => {
                    e.stopPropagation();
                    likeImage(result.generated_id, likeBtn);
                };

                const dislikeBtn = document.createElement('button');
                dislikeBtn.className = 'btn-remove';
                dislikeBtn.innerHTML = '🗂️';  // 空心文件夹
                dislikeBtn.title = '标记删除';
                dislikeBtn.setAttribute('data-generated-id', result.generated_id);
                dislikeBtn.onclick = (e) => {
                    e.stopPropagation();
                    dislikeImage(result.generated_id, dislikeBtn);
                };

                actionsDiv.appendChild(likeBtn);
                actionsDiv.appendChild(dislikeBtn);
                imageContainer.appendChild(actionsDiv);
            }

            const resultInfo = document.createElement('div');
            resultInfo.className = 'result-info';

            const resultTitle = document.createElement('div');
            resultTitle.className = 'result-title';
            resultTitle.textContent = result.style_name || selectedPrompts[index].name;

            resultInfo.appendChild(resultTitle);
            resultItem.appendChild(imageContainer);
            resultItem.appendChild(resultInfo);
        } else {
            resultItem.classList.add('result-error');

            const resultInfo = document.createElement('div');
            resultInfo.className = 'result-info';

            const resultTitle = document.createElement('div');
            resultTitle.className = 'result-title';
            resultTitle.textContent = '生成失败';

            const resultError = document.createElement('div');
            resultError.className = 'result-prompt';
            resultError.textContent = `错误: ${result.error}`;

            resultInfo.appendChild(resultTitle);
            resultInfo.appendChild(resultError);
            resultItem.appendChild(resultInfo);
        }

        // 将新的结果项添加到最前面
        if (resultsGrid.firstChild) {
            resultsGrid.insertBefore(resultItem, resultsGrid.firstChild);
        } else {
            resultsGrid.appendChild(resultItem);
        }
    }
}

// 展开/收起提示词
function togglePrompt(promptId) {
    const card = document.querySelector(`[data-id="${promptId}"]`);
    const full = card.querySelector('.prompt-full');
    const btn = card.querySelector('.btn-expand');

    if (full.style.display === 'none') {
        full.style.display = 'block';
        btn.innerHTML = '▲ 收起';
    } else {
        full.style.display = 'none';
        btn.innerHTML = '▼ 展开';
    }
}

// 打开图片查看模态框
function openImageModal(imageSrc, title) {
    modalImage.src = imageSrc;
    modalTitle.textContent = title;

    // 从图片URL中提取文件名用于下载，正确处理编码
    const encodedFilename = imageSrc.split('/').pop();
    const filename = decodeURIComponent(encodedFilename);
    modalDownload.onclick = () => downloadImage(filename, imageSrc);

    imageModal.style.display = 'flex';

    // 添加键盘事件监听，按ESC关闭
    document.addEventListener('keydown', handleEscapeKey);
}

// 处理ESC键关闭
function handleEscapeKey(e) {
    if (e.key === 'Escape') {
        closeModal();
    }
}

// 关闭模态框
function closeModal() {
    imageModal.style.display = 'none';
    // 移除键盘事件监听
    document.removeEventListener('keydown', handleEscapeKey);
}

// 下载图片
function downloadImage(filename, imageSrc) {
    console.log('下载文件:', filename);
    const link = document.createElement('a');

    // 使用原始的imageSrc，它已经正确编码了
    link.href = imageSrc || `/api/image/${encodeURIComponent(filename)}`;

    // 设置下载文件名为原始中文文件名
    link.download = filename;
    link.setAttribute('download', filename);

    // 设置正确的Content-Disposition头部支持
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    console.log('下载链接:', link.href);
    console.log('下载文件名:', filename);
}

// 收藏图片
async function likeImage(generatedId, button, event) {
    // 阻止事件冒泡，防止触发图片点击
    if (event) {
        event.stopPropagation();
    }

    // 防止重复点击
    if (button.disabled) {
        return;
    }

    button.disabled = true;

    try {
        console.log(`发送收藏请求: 图片ID=${generatedId}`);

        const response = await fetch(`/api/like/${generatedId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('收藏响应:', data);

        if (data.success) {
            // 更新按钮状态
            if (data.action === 'liked') {
                button.classList.add('active');
                button.innerHTML = '⭐';  // 实心星
                button.title = '取消收藏';

                // 取消标记删除按钮的激活状态
                const dislikeBtn = button.parentElement.querySelector('.btn-remove');
                if (dislikeBtn) {
                    dislikeBtn.classList.remove('active');
                    dislikeBtn.innerHTML = '🗂️';  // 空心文件夹
                    dislikeBtn.title = '标记删除';
                }
                console.log('✅ 收藏成功');
            } else if (data.action === 'removed_like') {
                button.classList.remove('active');
                button.innerHTML = '☆';  // 空心星
                button.title = '收藏这张图片';
                console.log('✅ 取消收藏成功');
            }

            console.log('收藏操作成功:', data.message);
        } else {
            console.error('收藏失败:', data.error);
            alert('收藏失败: ' + data.error);
        }
    } catch (error) {
        console.error('收藏异常:', error);
        alert('收藏失败: ' + error.message);
    } finally {
        // 恢复按钮可点击状态
        setTimeout(() => {
            button.disabled = false;
        }, 500);
    }
}

// 标记删除图片
async function dislikeImage(generatedId, button, event) {
    // 阻止事件冒泡，防止触发图片点击
    if (event) {
        event.stopPropagation();
    }

    // 防止重复点击
    if (button.disabled) {
        return;
    }

    button.disabled = true;

    try {
        console.log(`发送标记删除请求: 图片ID=${generatedId}`);

        const response = await fetch(`/api/dislike/${generatedId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('标记删除响应:', data);

        if (data.success) {
            // 更新按钮状态
            if (data.action === 'disliked') {
                button.classList.add('active');
                button.innerHTML = '🗑️';  // 实心垃圾桶
                button.title = '取消标记删除';

                // 取消收藏按钮的激活状态
                const likeBtn = button.parentElement.querySelector('.btn-favorite');
                if (likeBtn) {
                    likeBtn.classList.remove('active');
                    likeBtn.innerHTML = '☆';  // 空心星
                    likeBtn.title = '收藏这张图片';
                }
                console.log('✅ 标记删除成功');
            } else if (data.action === 'removed_dislike') {
                button.classList.remove('active');
                button.innerHTML = '🗂️';  // 空心文件夹
                button.title = '标记删除';
                console.log('✅ 取消标记删除成功');
            }

            console.log('标记删除操作成功:', data.message);
        } else {
            console.error('标记删除失败:', data.error);
            alert('标记删除失败: ' + data.error);
        }
    } catch (error) {
        console.error('标记删除异常:', error);
        alert('标记删除失败: ' + error.message);
    } finally {
        // 恢复按钮可点击状态
        setTimeout(() => {
            button.disabled = false;
        }, 500);
    }
}

// 检查重新使用图片的URL参数
function checkReuseImageParams() {
    const urlParams = new URLSearchParams(window.location.search);

    if (urlParams.get('reuse') === 'true') {
        const imageUrl = urlParams.get('imageUrl');
        const imageName = urlParams.get('imageName');
        const uploadId = urlParams.get('uploadId');

        console.log('检测到重新使用图片请求:', { imageUrl, imageName, uploadId });

        if (imageUrl && imageName) {
            loadReuseImage(imageUrl, imageName, uploadId);
        }

        // 清理URL参数，避免刷新时重复加载
        const cleanUrl = window.location.pathname;
        window.history.replaceState({}, document.title, cleanUrl);
    }
}

// 加载重新使用的图片
async function loadReuseImage(imageUrl, imageName, uploadId) {
    try {
        console.log('开始加载重新使用的图片:', imageUrl);

        // 显示加载状态
        showImageLoadingState();

        // 获取图片数据
        const response = await fetch(imageUrl);
        if (!response.ok) {
            throw new Error('图片加载失败');
        }

        const blob = await response.blob();

        // 创建File对象，使用原始文件名
        const file = new File([blob], imageName, { type: blob.type });

        console.log('图片加载完成，开始重新上传到服务器...');

        // 重新上传图片到服务器
        const formData = new FormData();
        formData.append('file', file);

        const uploadResponse = await fetch('/api/upload', {
            method: 'POST',
            body: formData
        });

        const uploadData = await uploadResponse.json();
        console.log('重新上传响应:', uploadData);

        if (uploadData.success) {
            // 使用新的上传信息
            uploadedFile = {
                filename: uploadData.filename,
                originalName: uploadData.original_name
            };

            // 保存数据库相关信息
            currentUploadId = uploadData.upload_id;
            currentFileMd5 = uploadData.file_md5;

            console.log('重新上传成功，文件信息:', uploadedFile);
            console.log('数据库ID:', currentUploadId, 'MD5:', currentFileMd5);

            // 显示图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImage.src = e.target.result;
                document.getElementById('imageName').textContent = file.name;

                // 显示上传图片区域，隐藏上传区域
                document.getElementById('uploadArea').style.display = 'none';
                uploadedImage.style.display = 'flex';

                // 更新生成按钮状态
                updateGenerateButton();

                console.log('✅ 图片重新使用成功');
            };

            reader.readAsDataURL(file);
        } else {
            throw new Error(uploadData.error || '重新上传失败');
        }

    } catch (error) {
        console.error('❌ 重新使用图片失败:', error);
        alert('重新使用图片失败: ' + error.message);

        // 恢复上传区域显示
        restoreUploadArea();
    }
}

// 显示图片加载状态
function showImageLoadingState() {
    // 显示加载状态
    const uploadArea = document.getElementById('uploadArea');
    const originalContent = uploadArea.innerHTML;

    uploadArea.innerHTML = `
        <div style="text-align: center; padding: 2rem; color: #667eea;">
            <div style="font-size: 2rem; margin-bottom: 1rem;">⏳</div>
            <div style="font-size: 1.1rem; font-weight: 500;">正在加载图片...</div>
            <div style="font-size: 0.9rem; color: #a0aec0; margin-top: 0.5rem;">请稍候</div>
        </div>
    `;

    // 保存原始内容，以便恢复
    uploadArea.dataset.originalContent = originalContent;

    console.log('正在加载图片...');
}

// 恢复上传区域
function restoreUploadArea() {
    const uploadArea = document.getElementById('uploadArea');
    const originalContent = uploadArea.dataset.originalContent;

    if (originalContent) {
        uploadArea.innerHTML = originalContent;
        delete uploadArea.dataset.originalContent;
    }

    uploadArea.style.display = 'block';
    uploadedImage.style.display = 'none';
}

// 清空所有结果
function clearAllResults() {
    if (confirm('确定要清空所有生成结果吗？此操作不可撤销。')) {
        resultsGrid.innerHTML = '';
        resultsSection.style.display = 'none';
        progressInfo.style.display = 'none';

        // 清空当前任务
        currentTasks = [];

        // 停止状态检查
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }

        console.log('✅ 所有结果已清空');
    }
}
