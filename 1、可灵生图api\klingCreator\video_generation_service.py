#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频生成服务
结合温和清空注入.py脚本生成视频
"""

import os
import subprocess
import threading
import time
from datetime import datetime
from pathlib import Path

class VideoGenerationService:
    """视频生成服务"""
    
    def __init__(self):
        self.script_path = Path(__file__).parent / "简化视频生成.py"
        self.output_dir = Path("video_output")
        self.output_dir.mkdir(exist_ok=True)

        # 创建子目录
        (self.output_dir / "transitions").mkdir(exist_ok=True)
        (self.output_dir / "final").mkdir(exist_ok=True)

        # 初始化数据库模型
        from database_models import DatabaseManager, GeneratedImageModel
        self.db = DatabaseManager()
        self.generated_model = GeneratedImageModel(self.db)
        
    def create_video_task(self, task_name, upload_id, selected_image_ids):
        """创建视频生成任务"""
        try:
            print(f"🎬 VideoGenerationService: 开始创建视频任务")
            print(f"📝 任务名称: {task_name}")
            print(f"📁 上传ID: {upload_id}")
            print(f"🖼️ 图片ID列表: {selected_image_ids}")

            # 导入数据库模型（延迟导入避免循环依赖）
            from database_models import video_task_model, video_segment_model, generated_image_model

            # 验证图片ID
            if len(selected_image_ids) < 2:
                raise ValueError("至少需要选择2张图片")

            print(f"✅ 图片数量验证通过: {len(selected_image_ids)}张图片")

            # 计算视频片段数量（两两组合）
            total_segments = len(selected_image_ids) - 1
            print(f"📊 计算视频片段数量: {total_segments}个片段")

            # 验证图片是否存在
            print("🔍 验证图片是否存在...")
            for i, image_id in enumerate(selected_image_ids):
                image = generated_image_model.get_by_id(image_id)
                if not image:
                    raise ValueError(f"图片ID {image_id} 不存在于数据库中")
                print(f"  ✅ 图片{i+1}: ID={image_id}, 文件名={image.get('generated_filename', 'Unknown')}")

            # 创建任务记录
            print("📋 创建任务记录...")
            task_id = video_task_model.create_task(
                task_name=task_name,
                upload_id=upload_id,
                image_ids=selected_image_ids,
                total_segments=total_segments
            )
            print(f"✅ 任务记录创建成功，任务ID: {task_id}")

            # 创建视频片段记录
            print("🎞️ 创建视频片段记录...")
            for i in range(total_segments):
                first_image_id = selected_image_ids[i]
                last_image_id = selected_image_ids[i + 1]

                print(f"  📹 片段{i+1}: {first_image_id} -> {last_image_id}")

                # 获取图片信息
                first_image = generated_image_model.get_by_id(first_image_id)
                last_image = generated_image_model.get_by_id(last_image_id)

                if not first_image or not last_image:
                    raise ValueError(f"图片ID {first_image_id} 或 {last_image_id} 不存在")

                # 创建视频片段记录
                video_segment_model.create_segment(
                    task_id=task_id,
                    first_image_id=first_image_id,
                    last_image_id=last_image_id,
                    segment_order=i + 1,
                    first_image_filename=first_image['generated_filename'],
                    last_image_filename=last_image['generated_filename']
                )
                print(f"    ✅ 片段{i+1}记录创建成功")

            # 启动后台视频生成任务
            print("🚀 启动后台视频生成线程...")
            thread = threading.Thread(
                target=self._generate_videos_async,
                args=(task_id, upload_id, selected_image_ids),
                daemon=True
            )
            thread.start()
            print("✅ 后台线程启动成功")

            return task_id

        except Exception as e:
            import traceback
            error_msg = str(e)
            error_trace = traceback.format_exc()

            print(f"❌ VideoGenerationService: 创建视频任务失败")
            print(f"📋 错误信息: {error_msg}")
            print(f"📋 错误详情:\n{error_trace}")

            raise Exception(f"视频任务创建失败: {error_msg}")
    
    def _generate_videos_async(self, task_id, upload_id, selected_image_ids):
        """异步生成视频"""
        try:
            print(f"\n{'='*80}")
            print(f"🎬 [异步任务开始] 任务ID: {task_id}")
            print(f"📁 上传ID: {upload_id}")
            print(f"🖼️ 图片IDs: {selected_image_ids}")
            print(f"{'='*80}")

            # 导入数据库模型
            print(f"🔧 [步骤A1] 导入数据库模型...")
            from database_models import video_task_model, video_segment_model, generated_image_model
            print(f"✅ 数据库模型导入成功")

            # 更新任务状态为进行中
            print(f"🔧 [步骤A2] 更新任务状态为processing...")
            video_task_model.update_task_status(task_id, 'processing')
            print(f"✅ 任务状态更新成功")

            # 获取所有视频片段
            print(f"🔧 [步骤A3] 获取视频片段列表...")
            segments = video_segment_model.get_segments_by_task(task_id)
            print(f"✅ 获取到 {len(segments)} 个视频片段")

            if not segments:
                raise Exception("没有找到任何视频片段记录")

            video_files = []

            print(f"\n🎞️ [开始生成视频片段] 总计 {len(segments)} 个片段")
            print(f"{'='*80}")

            for i, segment in enumerate(segments):
                try:
                    segment_num = i + 1
                    print(f"\n🎬 [片段{segment_num}] 开始处理...")
                    print(f"📋 片段ID: {segment['id']}")
                    print(f"🖼️ 首帧ID: {segment['first_image_id']}")
                    print(f"🖼️ 尾帧ID: {segment['last_image_id']}")
                    print(f"📊 顺序: {segment['segment_order']}")

                    # 更新片段状态为处理中
                    print(f"🔧 [片段{segment_num}-步骤1] 更新片段状态为processing...")
                    video_segment_model.update_segment_status(segment['id'], 'processing')
                    print(f"✅ 片段状态更新成功")

                    # 生成单个视频片段
                    print(f"🔧 [片段{segment_num}-步骤2] 调用视频生成脚本...")
                    video_path = self._generate_single_video(
                        segment['first_image_id'],
                        segment['last_image_id'],
                        task_id,
                        segment['segment_order']
                    )

                    if video_path and video_path.exists():
                        # 更新片段状态和视频路径
                        print(f"🔧 [片段{segment_num}-步骤3] 更新片段状态为completed...")
                        video_segment_model.update_segment_status(
                            segment['id'], 'completed', str(video_path)
                        )
                        video_files.append(video_path)
                        print(f"✅ [片段{segment_num}] 生成成功: {video_path}")
                    else:
                        # 更新片段状态为失败
                        print(f"❌ [片段{segment_num}] 视频文件不存在或生成失败")
                        video_segment_model.update_segment_status(segment['id'], 'failed')
                        print(f"🔧 片段状态已更新为failed")

                except Exception as e:
                    import traceback
                    error_trace = traceback.format_exc()
                    print(f"❌ [片段{segment_num}] 生成失败: {e}")
                    print(f"📋 错误详情:\n{error_trace}")

                    try:
                        video_segment_model.update_segment_status(segment['id'], 'failed')
                        print(f"🔧 片段{segment_num}状态已更新为failed")
                    except Exception as update_error:
                        print(f"❌ 更新片段{segment_num}状态失败: {update_error}")

                print(f"{'='*80}")
            
            # 如果有成功生成的视频片段，进行拼接
            print(f"[ASYNC] 视频片段生成完成，成功: {len(video_files)}, 总计: {len(segments)}")

            if video_files:
                print(f"[ASYNC] 开始拼接 {len(video_files)} 个视频片段...")
                final_video_path = self._concatenate_videos(video_files, task_id)
                if final_video_path:
                    # 更新任务状态为完成
                    video_task_model.update_task_status(
                        task_id, 'completed', str(final_video_path)
                    )
                    print(f"[ASYNC] 最终视频生成成功: {final_video_path}")
                else:
                    video_task_model.update_task_status(task_id, 'failed')
                    print("[ASYNC] 视频拼接失败")
            else:
                video_task_model.update_task_status(task_id, 'failed')
                print("[ASYNC] 没有成功生成的视频片段，任务失败")

        except Exception as e:
            import traceback
            error_trace = traceback.format_exc()
            print(f"[ASYNC] 异步视频生成失败: {e}")
            print(f"[ASYNC] 错误详情:\n{error_trace}")

            # 导入数据库模型
            try:
                from database_models import video_task_model
                video_task_model.update_task_status(task_id, 'failed')
                print(f"[ASYNC] 已将任务 {task_id} 状态更新为失败")
            except Exception as update_error:
                print(f"[ASYNC] 更新任务状态失败: {update_error}")
    
    def _generate_single_video(self, first_image_id, last_image_id, task_id, segment_order):
        """生成单个视频片段"""
        try:
            print(f"\n    🔧 [视频生成-步骤1] 查找图片文件...")
            print(f"    📋 首帧图片ID: {first_image_id}")
            print(f"    📋 尾帧图片ID: {last_image_id}")

            # 根据图片ID找到实际的文件
            first_image_path = self._find_actual_image_file_by_id(first_image_id)
            last_image_path = self._find_actual_image_file_by_id(last_image_id)

            # 检查图片文件是否存在
            print(f"    🔧 [视频生成-步骤2] 验证图片文件...")
            if not first_image_path or not first_image_path.exists():
                print(f"    ❌ 首帧图片不存在: ID {first_image_id} -> {first_image_path}")
                return None
            if not last_image_path or not last_image_path.exists():
                print(f"    ❌ 尾帧图片不存在: ID {last_image_id} -> {last_image_path}")
                return None

            print(f"    ✅ 首帧图片: {first_image_path}")
            print(f"    ✅ 尾帧图片: {last_image_path}")

            # 构建输出视频路径
            print(f"    🔧 [视频生成-步骤3] 准备输出路径...")
            output_video_path = self.output_dir / "transitions" / f"task_{task_id}_segment_{segment_order}.mp4"

            # 确保输出目录存在
            output_video_path.parent.mkdir(parents=True, exist_ok=True)
            print(f"    ✅ 输出路径: {output_video_path}")

            # 调用增强视频生成脚本生成视频
            print(f"    🔧 [视频生成-步骤4] 准备执行脚本...")
            script_path = Path(__file__).parent / "增强视频生成.py"

            if not script_path.exists():
                print(f"    ❌ 视频生成脚本不存在: {script_path}")
                return None

            cmd = [
                "python",
                str(script_path),
                "--first-image", str(first_image_path),
                "--last-image", str(last_image_path),
                "--output", str(output_video_path),
                "--debug"  # 启用调试模式
            ]

            print(f"    📋 执行命令: {' '.join(cmd)}")

            # 执行命令
            print(f"    🔧 [视频生成-步骤5] 执行视频生成脚本...")
            print(f"    ⏱️ 超时设置: 300秒")

            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
                encoding='utf-8',
                errors='ignore'  # 忽略编码错误
            )

            print(f"    ✅ 脚本执行完成，返回码: {result.returncode}")

            if result.stdout:
                print(f"    📋 [脚本输出]:\n{result.stdout}")

            if result.stderr:
                print(f"    📋 [脚本错误]:\n{result.stderr}")

            print(f"    🔧 [视频生成-步骤6] 验证生成结果...")

            if result.returncode == 0:
                if output_video_path.exists():
                    file_size = output_video_path.stat().st_size
                    print(f"    ✅ 视频生成成功: {output_video_path}")
                    print(f"    📊 文件大小: {file_size} 字节")
                    return output_video_path
                else:
                    print(f"    ❌ 脚本执行成功但视频文件不存在: {output_video_path}")
                    return None
            else:
                print(f"    ❌ 脚本执行失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"    📋 错误详情: {result.stderr}")
                return None
                
        except subprocess.TimeoutExpired:
            print(f"视频生成超时: ID {first_image_id} -> ID {last_image_id}")
            return None
        except Exception as e:
            print(f"生成视频时出错: {e}")
            return None

    def _find_actual_image_file_by_id(self, image_id):
        """根据图片ID找到实际的AI生成图片文件"""
        try:
            # 获取图片信息
            image_info = self.generated_model.get_by_id(image_id)
            if not image_info:
                print(f"❌ 未找到ID为{image_id}的图片记录")
                return None

            generated_filename = image_info['generated_filename']
            generated_path = image_info['generated_path']

            print(f"📋 查找图片ID {image_id}")
            print(f"    生成文件名: {generated_filename}")
            print(f"    存储路径: {generated_path}")

            # 首先尝试使用数据库中存储的完整路径
            if generated_path:
                generated_file_path = Path(generated_path)
                if generated_file_path.exists():
                    print(f"✅ 找到AI生成图片: {generated_file_path}")
                    return generated_file_path
                else:
                    print(f"⚠️ 数据库路径中的图片不存在: {generated_file_path}")

            # 如果数据库路径不存在，尝试在web_output目录中查找
            web_output_dir = Path("web_output")
            if web_output_dir.exists():
                web_output_file_path = web_output_dir / generated_filename
                if web_output_file_path.exists():
                    print(f"✅ 在web_output目录找到图片: {web_output_file_path}")
                    return web_output_file_path
                else:
                    print(f"⚠️ web_output目录中图片不存在: {web_output_file_path}")
            else:
                print(f"⚠️ web_output目录不存在")

            # 最后尝试在uploads目录中查找（作为备用）
            print(f"🔍 在uploads目录中查找备用图片...")

            # 提取基础文件名（去掉数字和扩展名）
            base_name = generated_filename.rsplit('_', 1)[0]  # 华创瑞景园-1952x1056-小

            uploads_dir = Path("uploads")
            if not uploads_dir.exists():
                print(f"❌ uploads目录不存在")
                return None

            # 获取所有匹配的文件，按修改时间排序
            matching_files = []
            for file_path in uploads_dir.glob(f"{base_name}_*.jpg"):
                matching_files.append(file_path)
            for file_path in uploads_dir.glob(f"{base_name}_*.png"):
                matching_files.append(file_path)

            if not matching_files:
                print(f"❌ 未找到匹配文件: {base_name}_*")
                return None

            # 按修改时间排序（最新的在前）
            matching_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # 根据图片ID选择对应的文件
            try:
                # 计算在所有匹配文件中的索引位置
                index = (image_id - 1) % len(matching_files)
                selected_file = matching_files[index]
                print(f"⚠️ 使用备用图片 (ID {image_id}): {selected_file}")
                return selected_file
            except:
                # 如果计算失败，返回第一个文件
                print(f"⚠️ 使用第一个匹配文件: {matching_files[0]}")
                return matching_files[0]

        except Exception as e:
            print(f"❌ 查找图片文件异常: {e}")
            return None

    def _concatenate_videos(self, video_files, task_id):
        """拼接多个视频文件"""
        try:
            if not video_files:
                return None
            
            # 如果只有一个视频文件，直接复制
            if len(video_files) == 1:
                final_path = self.output_dir / "final" / f"task_{task_id}_final.mp4"
                import shutil
                shutil.copy2(video_files[0], final_path)
                return final_path
            
            # 创建文件列表
            file_list_path = self.output_dir / f"task_{task_id}_filelist.txt"
            with open(file_list_path, 'w', encoding='utf-8') as f:
                for video_file in video_files:
                    f.write(f"file '{video_file.absolute()}'\n")
            
            # 输出路径
            final_video_path = self.output_dir / "final" / f"task_{task_id}_final.mp4"
            
            # 使用FFmpeg拼接视频
            cmd = [
                "ffmpeg",
                "-f", "concat",
                "-safe", "0",
                "-i", str(file_list_path),
                "-c", "copy",
                "-y",  # 覆盖输出文件
                str(final_video_path)
            ]
            
            print(f"拼接视频命令: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120  # 2分钟超时
            )
            
            # 清理临时文件
            if file_list_path.exists():
                file_list_path.unlink()
            
            if result.returncode == 0:
                print(f"视频拼接成功: {final_video_path}")
                return final_video_path
            else:
                print(f"视频拼接失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"拼接视频时出错: {e}")
            return None
    
    def get_task_status(self, task_id):
        """获取任务状态"""
        try:
            from database_models import video_task_model, video_segment_model
            
            task = video_task_model.get_task_by_id(task_id)
            if not task:
                return None
            
            segments = video_segment_model.get_segments_by_task(task_id)
            
            return {
                'task': task,
                'segments': segments
            }
        except Exception as e:
            print(f"获取任务状态失败: {e}")
            return None
    
    def get_tasks_by_upload(self, upload_id):
        """获取指定上传ID的所有视频任务"""
        try:
            from database_models import video_task_model, video_segment_model
            tasks = video_task_model.get_tasks_by_upload(upload_id)

            # 为每个任务添加completed_segments字段
            for task in tasks:
                completed_segments = video_segment_model.get_completed_segments_count(task['id'])
                task['completed_segments'] = completed_segments

            return tasks
        except Exception as e:
            print(f"获取视频任务失败: {e}")
            return []

# 创建全局服务实例
video_service = VideoGenerationService()
