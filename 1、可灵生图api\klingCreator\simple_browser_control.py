#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的浏览器控制脚本
使用现有Chrome浏览器的用户数据目录
"""

import asyncio
import os
import sys
from pathlib import Path
from playwright.async_api import async_playwright


async def upload_with_existing_profile(first_frame_path: str, last_frame_path: str, prompt: str = ""):
    """
    使用现有Chrome配置文件上传首尾帧
    
    Args:
        first_frame_path: 首帧图片路径
        last_frame_path: 尾帧图片路径
        prompt: 提示词
    """
    
    print("🎬 使用现有Chrome配置上传首尾帧")
    print("=" * 50)
    print(f"📸 首帧: {Path(first_frame_path).name}")
    print(f"📸 尾帧: {Path(last_frame_path).name}")
    print(f"💭 提示词: {prompt}")
    print()
    
    # 验证文件存在
    if not os.path.exists(first_frame_path):
        print(f"❌ 首帧图片不存在: {first_frame_path}")
        return False
    
    if not os.path.exists(last_frame_path):
        print(f"❌ 尾帧图片不存在: {last_frame_path}")
        return False
    
    # Chrome用户数据目录（通常的默认位置）
    user_data_dirs = [
        os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data"),
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data",
        "./chrome_user_data"  # 备用目录
    ]
    
    user_data_dir = None
    for dir_path in user_data_dirs:
        if os.path.exists(dir_path):
            user_data_dir = dir_path
            print(f"✅ 找到Chrome用户数据目录: {dir_path}")
            break
    
    if not user_data_dir:
        print("⚠️ 未找到Chrome用户数据目录，使用默认配置")
        user_data_dir = "./temp_chrome_data"
    
    async with async_playwright() as p:
        print("🚀 启动Chrome浏览器（使用现有配置）...")
        
        try:
            # 使用现有的用户数据目录启动Chrome
            context = await p.chromium.launch_persistent_context(
                user_data_dir=user_data_dir,
                headless=False,
                args=[
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation",
                    "--disable-automation",
                    "--no-sandbox"
                ],
                viewport={'width': 1920, 'height': 1080}
            )
            
            # 隐藏webdriver属性
            await context.add_init_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # 查找现有的即梦AI页面或创建新页面
            jimeng_page = None
            for page in context.pages:
                if "jimeng.jianying.com" in page.url:
                    jimeng_page = page
                    print(f"✅ 找到现有即梦AI页面")
                    break
            
            if not jimeng_page:
                print("🌐 打开新的即梦AI页面...")
                jimeng_page = await context.new_page()
            
            # 导航到即梦AI视频生成页面
            await jimeng_page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
            await jimeng_page.wait_for_load_state('networkidle')
            
            # 等待页面加载
            await asyncio.sleep(5)
            print("✅ 即梦AI页面加载完成")
            
            # 执行上传操作
            success = await perform_upload_operations(jimeng_page, first_frame_path, last_frame_path, prompt)
            
            if success:
                print("✅ 上传操作完成！")
                print("💡 浏览器将保持打开，您可以查看生成进度")
                
                # 保持浏览器打开
                input("按Enter键关闭浏览器...")
                return True
            else:
                print("❌ 上传操作失败")
                return False
                
        except Exception as e:
            print(f"❌ 浏览器操作失败: {e}")
            return False


async def perform_upload_operations(page, first_frame_path, last_frame_path, prompt):
    """执行上传操作"""
    try:
        print("🔍 查找文件上传元素...")
        
        # 等待页面元素加载
        await asyncio.sleep(3)
        
        # 查找文件输入元素
        file_inputs = await page.query_selector_all('input[type="file"]')
        print(f"找到 {len(file_inputs)} 个文件上传元素")
        
        if len(file_inputs) < 2:
            print("⚠️ 文件上传元素不足，尝试点击上传区域...")
            
            # 尝试点击各种可能的上传区域
            click_selectors = [
                'div[class*="upload"]',
                'button[class*="upload"]',
                '.upload-area',
                '.upload-zone',
                'div[role="button"]'
            ]
            
            for selector in click_selectors:
                try:
                    elements = await page.query_selector_all(selector)
                    for element in elements:
                        if await element.is_visible():
                            await element.click()
                            print(f"点击了上传区域: {selector}")
                            await asyncio.sleep(1)
                except:
                    continue
            
            # 重新查找文件输入
            await asyncio.sleep(2)
            file_inputs = await page.query_selector_all('input[type="file"]')
            print(f"点击后找到 {len(file_inputs)} 个文件上传元素")
        
        if len(file_inputs) >= 2:
            print("📤 开始上传图片...")
            
            # 上传首帧
            print("📤 上传首帧...")
            await file_inputs[0].set_input_files(first_frame_path)
            await handle_upload_completion(page, "首帧")
            
            # 上传尾帧
            print("📤 上传尾帧...")
            await file_inputs[1].set_input_files(last_frame_path)
            await handle_upload_completion(page, "尾帧")
            
            # 设置提示词
            if prompt:
                print("💭 设置提示词...")
                await set_prompt(page, prompt)
            
            # 设置10秒时长
            print("⏱️ 设置10秒时长...")
            await set_duration(page)
            
            # 点击生成按钮
            print("🚀 点击生成按钮...")
            return await click_generate_button(page)
        else:
            print("❌ 未找到足够的文件上传元素")
            
            # 显示页面信息帮助调试
            print("🔍 页面调试信息:")
            title = await page.title()
            print(f"   页面标题: {title}")
            
            # 查找所有可能的上传相关元素
            upload_elements = await page.query_selector_all('[class*="upload"], [id*="upload"]')
            print(f"   找到 {len(upload_elements)} 个包含'upload'的元素")
            
            return False
            
    except Exception as e:
        print(f"❌ 上传操作失败: {e}")
        return False


async def handle_upload_completion(page, frame_type):
    """处理上传完成（包括可能的保存弹窗）"""
    print(f"⏳ 等待{frame_type}上传完成...")
    
    # 等待最多15秒处理上传和可能的弹窗
    for attempt in range(30):
        try:
            # 查找保存相关按钮
            buttons = await page.query_selector_all('button')
            
            for button in buttons:
                if await button.is_visible():
                    text = await button.inner_text()
                    text = text.strip().lower() if text else ""
                    
                    # 检查保存相关关键词
                    save_keywords = ['保存', '确定', '确认', 'save', 'ok', '完成']
                    if any(keyword in text for keyword in save_keywords):
                        await button.click()
                        print(f"✅ {frame_type}点击了按钮: {text}")
                        await asyncio.sleep(2)
                        return True
            
        except:
            pass
        
        await asyncio.sleep(0.5)
    
    print(f"✅ {frame_type}上传处理完成")
    return True


async def set_prompt(page, prompt):
    """设置提示词"""
    try:
        # 查找提示词输入框
        selectors = [
            'textarea',
            'input[type="text"]',
            '[placeholder*="提示"]',
            '[placeholder*="prompt"]'
        ]
        
        for selector in selectors:
            elements = await page.query_selector_all(selector)
            for element in elements:
                if await element.is_visible():
                    placeholder = await element.get_attribute('placeholder')
                    if placeholder and ('提示' in placeholder or 'prompt' in placeholder.lower()):
                        await element.fill(prompt)
                        print("✅ 提示词设置完成")
                        return True
        
        # 如果没找到特定的提示词输入框，尝试第一个可见的textarea
        textareas = await page.query_selector_all('textarea')
        for textarea in textareas:
            if await textarea.is_visible():
                await textarea.fill(prompt)
                print("✅ 提示词设置完成（使用第一个文本区域）")
                return True
        
        print("⚠️ 未找到提示词输入框")
        return False
        
    except Exception as e:
        print(f"⚠️ 设置提示词失败: {e}")
        return False


async def set_duration(page):
    """设置10秒时长"""
    try:
        # 查找10秒相关的按钮
        buttons = await page.query_selector_all('button')
        for button in buttons:
            try:
                if await button.is_visible():
                    text = await button.inner_text()
                    if text and ('10' in text and ('秒' in text or 's' in text.lower())):
                        await button.click()
                        print(f"✅ 设置时长为10秒: {text}")
                        return True
            except:
                continue
        
        print("⚠️ 未找到10秒时长设置，使用默认值")
        return True
        
    except Exception as e:
        print(f"⚠️ 设置时长失败: {e}")
        return True


async def click_generate_button(page):
    """点击生成按钮"""
    try:
        # 查找生成按钮
        buttons = await page.query_selector_all('button')
        for button in buttons:
            try:
                if await button.is_visible() and await button.is_enabled():
                    text = await button.inner_text()
                    if text and ('生成' in text or 'generate' in text.lower() or '创建' in text):
                        await button.click()
                        print(f"✅ 点击生成按钮: {text}")
                        await asyncio.sleep(3)
                        return True
            except:
                continue
        
        print("❌ 未找到可用的生成按钮")
        return False
        
    except Exception as e:
        print(f"❌ 点击生成按钮失败: {e}")
        return False


async def main():
    """主函数"""
    
    # 测试图片路径
    first_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座孤立在纯黑色空间中的蒸汽朋克机械堡垒_建筑内部的锅炉透过格栅.png"
    last_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (4).png"
    prompt = "从蒸汽朋克机械堡垒平滑过渡到生物发光植物建筑"
    
    # 如果有命令行参数，使用命令行参数
    if len(sys.argv) >= 3:
        first_frame = sys.argv[1]
        last_frame = sys.argv[2]
        prompt = sys.argv[3] if len(sys.argv) > 3 else ""
    
    success = await upload_with_existing_profile(first_frame, last_frame, prompt)
    
    if success:
        print("\n🎉 操作完成！")
    else:
        print("\n❌ 操作失败")


if __name__ == "__main__":
    asyncio.run(main())
