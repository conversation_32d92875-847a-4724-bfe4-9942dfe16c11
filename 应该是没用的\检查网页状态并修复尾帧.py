#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查即梦AI网页状态并修复尾帧注入问题
"""

import asyncio
import os
import sys
import base64
import mimetypes
from pathlib import Path
from playwright.async_api import async_playwright


def read_image_as_base64(image_path: str):
    """读取图片并转换为Base64"""
    try:
        mime_type, _ = mimetypes.guess_type(image_path)
        if not mime_type or not mime_type.startswith('image/'):
            mime_type = 'image/png'
        
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        return {
            "name": os.path.basename(image_path),
            "data": base64_data,
            "type": mime_type,
            "size": len(image_data)
        }
    except Exception as e:
        print(f"❌ 读取图片失败: {e}")
        return None


async def check_and_fix_webpage():
    """检查网页状态并修复尾帧问题"""
    
    print("🔍 即梦AI网页状态检查与尾帧修复工具")
    print("=" * 60)
    
    # 检查Chrome调试端口
    debug_port = 9222
    print("🔍 检查Chrome调试端口...")
    
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("❌ Chrome调试端口未开启")
        return False
    
    print("✅ Chrome调试端口已开启")
    
    async with async_playwright() as p:
        try:
            print("🔗 连接到Chrome浏览器...")
            
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            contexts = browser.contexts
            
            if not contexts:
                print("❌ 未找到浏览器上下文")
                return False
            
            # 查找即梦AI页面
            jimeng_page = None
            for context in contexts:
                for page in context.pages:
                    if 'jimeng.jianying.com' in page.url:
                        jimeng_page = page
                        print(f"✅ 找到即梦AI页面: {page.url}")
                        break
                if jimeng_page:
                    break
            
            if not jimeng_page:
                print("❌ 未找到即梦AI页面")
                return False
            
            # 执行详细检查
            await perform_detailed_check(jimeng_page)
            
            return True
                
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False


async def perform_detailed_check(page):
    """执行详细的页面检查"""
    try:
        print("\n🔍 开始详细检查页面状态...")
        
        # 等待页面稳定
        await asyncio.sleep(2)
        
        # 步骤1: 全面页面分析
        print("📊 步骤1: 全面页面分析...")
        
        page_analysis = await page.evaluate("""
            () => {
                console.log('🔍 开始全面页面分析');
                
                const analysis = {
                    url: window.location.href,
                    title: document.title,
                    fileInputs: [],
                    images: [],
                    buttons: [],
                    textContent: {
                        hasFirstFrame: false,
                        hasLastFrame: false,
                        hasReference: false,
                        hasUpload: false
                    },
                    elements: {
                        uploadAreas: 0,
                        dragDropAreas: 0,
                        videoRelated: 0
                    }
                };
                
                // 分析文件输入
                const fileInputs = document.querySelectorAll('input[type="file"]');
                fileInputs.forEach((input, index) => {
                    const rect = input.getBoundingClientRect();
                    analysis.fileInputs.push({
                        index: index,
                        visible: input.offsetParent !== null,
                        display: getComputedStyle(input).display,
                        visibility: getComputedStyle(input).visibility,
                        opacity: getComputedStyle(input).opacity,
                        accept: input.accept,
                        multiple: input.multiple,
                        className: input.className,
                        id: input.id,
                        rect: {
                            width: rect.width,
                            height: rect.height,
                            x: rect.x,
                            y: rect.y
                        },
                        parentElement: input.parentElement ? input.parentElement.tagName : null,
                        parentClass: input.parentElement ? input.parentElement.className : null
                    });
                });
                
                // 分析图片
                const images = document.querySelectorAll('img');
                images.forEach((img, index) => {
                    if (img.src.includes('blob:') || img.src.includes('data:')) {
                        analysis.images.push({
                            index: index,
                            src: img.src.substring(0, 50) + '...',
                            width: img.width,
                            height: img.height,
                            className: img.className,
                            alt: img.alt
                        });
                    }
                });
                
                // 分析按钮和可点击元素
                const buttons = document.querySelectorAll('button, div[role="button"], [class*="button"], [class*="upload"]');
                buttons.forEach((btn, index) => {
                    const text = btn.textContent || btn.innerText || '';
                    if (text.length > 0 && text.length < 50) {
                        analysis.buttons.push({
                            index: index,
                            text: text.trim(),
                            tagName: btn.tagName,
                            className: btn.className,
                            visible: btn.offsetParent !== null,
                            clickable: !btn.disabled
                        });
                    }
                });
                
                // 分析文本内容
                const bodyText = document.body.textContent.toLowerCase();
                analysis.textContent.hasFirstFrame = bodyText.includes('首帧') || bodyText.includes('first frame');
                analysis.textContent.hasLastFrame = bodyText.includes('尾帧') || bodyText.includes('last frame') || bodyText.includes('参考帧');
                analysis.textContent.hasReference = bodyText.includes('参考') || bodyText.includes('reference');
                analysis.textContent.hasUpload = bodyText.includes('上传') || bodyText.includes('upload');
                
                // 分析特殊元素
                analysis.elements.uploadAreas = document.querySelectorAll('[class*="upload"], [class*="drop"]').length;
                analysis.elements.dragDropAreas = document.querySelectorAll('[draggable], [ondrop]').length;
                analysis.elements.videoRelated = document.querySelectorAll('[class*="video"], [class*="frame"]').length;
                
                console.log('页面分析结果:', analysis);
                return analysis;
            }
        """)
        
        # 显示分析结果
        print(f"📋 页面信息:")
        print(f"   URL: {page_analysis['url']}")
        print(f"   标题: {page_analysis['title']}")
        print(f"   文件输入数量: {len(page_analysis['fileInputs'])} 个")
        print(f"   预览图片数量: {len(page_analysis['images'])} 个")
        print(f"   按钮数量: {len(page_analysis['buttons'])} 个")
        
        print(f"\n📝 文本内容检测:")
        print(f"   包含'首帧': {page_analysis['textContent']['hasFirstFrame']}")
        print(f"   包含'尾帧': {page_analysis['textContent']['hasLastFrame']}")
        print(f"   包含'参考': {page_analysis['textContent']['hasReference']}")
        print(f"   包含'上传': {page_analysis['textContent']['hasUpload']}")
        
        # 详细分析文件输入
        print(f"\n📁 文件输入详细信息:")
        for i, input_info in enumerate(page_analysis['fileInputs']):
            print(f"   输入 {i}:")
            print(f"     可见: {input_info['visible']}")
            print(f"     display: {input_info['display']}")
            print(f"     visibility: {input_info['visibility']}")
            print(f"     opacity: {input_info['opacity']}")
            print(f"     accept: {input_info['accept']}")
            print(f"     className: {input_info['className']}")
            print(f"     父元素: {input_info['parentElement']} - {input_info['parentClass']}")
        
        # 显示相关按钮
        print(f"\n🔘 相关按钮:")
        relevant_buttons = [btn for btn in page_analysis['buttons'] 
                          if any(keyword in btn['text'].lower() 
                                for keyword in ['帧', 'frame', '上传', 'upload', '添加', 'add', '参考', 'reference'])]
        
        for btn in relevant_buttons[:10]:  # 只显示前10个相关按钮
            print(f"   '{btn['text']}' - {btn['tagName']} - 可见:{btn['visible']} - 可点击:{btn['clickable']}")
        
        # 步骤2: 尝试激活尾帧区域
        print(f"\n🔧 步骤2: 尝试激活尾帧区域...")
        
        activation_result = await page.evaluate("""
            () => {
                console.log('🔧 尝试激活尾帧区域');
                
                const result = {
                    clickedElements: [],
                    newFileInputs: 0,
                    errors: []
                };
                
                try {
                    // 策略1: 点击包含"尾帧"、"参考"、"添加"等关键词的元素
                    const keywords = ['尾帧', '参考', '添加', 'reference', 'last', 'frame', 'add', '上传'];
                    const clickableElements = document.querySelectorAll('button, div[role="button"], span[role="button"], [class*="button"], [onclick]');
                    
                    clickableElements.forEach(el => {
                        const text = (el.textContent || el.innerText || '').toLowerCase();
                        const className = (el.className || '').toLowerCase();
                        
                        for (const keyword of keywords) {
                            if (text.includes(keyword.toLowerCase()) || className.includes(keyword.toLowerCase())) {
                                if (el.offsetParent && !el.disabled) {
                                    try {
                                        el.click();
                                        result.clickedElements.push({
                                            text: text.substring(0, 30),
                                            className: className.substring(0, 50),
                                            keyword: keyword
                                        });
                                        console.log('点击了元素:', text.substring(0, 30), '关键词:', keyword);
                                    } catch (e) {
                                        result.errors.push('点击失败: ' + e.message);
                                    }
                                }
                                break;
                            }
                        }
                    });
                    
                    // 策略2: 查找并点击可能的上传区域
                    const uploadAreas = document.querySelectorAll('[class*="upload"], [class*="drop"], [class*="file"]');
                    uploadAreas.forEach(area => {
                        if (area.offsetParent && area.tagName !== 'INPUT') {
                            try {
                                area.click();
                                result.clickedElements.push({
                                    text: 'upload area',
                                    className: area.className.substring(0, 50),
                                    keyword: 'upload area'
                                });
                                console.log('点击了上传区域:', area.className);
                            } catch (e) {
                                result.errors.push('点击上传区域失败: ' + e.message);
                            }
                        }
                    });
                    
                    // 检查是否有新的文件输入出现
                    setTimeout(() => {
                        result.newFileInputs = document.querySelectorAll('input[type="file"]').length;
                    }, 1000);
                    
                } catch (error) {
                    result.errors.push('激活过程出错: ' + error.message);
                }
                
                console.log('激活结果:', result);
                return result;
            }
        """)
        
        await asyncio.sleep(3)  # 等待页面响应
        
        print(f"🔧 激活尝试结果:")
        print(f"   点击了 {len(activation_result['clickedElements'])} 个元素")
        for clicked in activation_result['clickedElements']:
            print(f"     - '{clicked['text']}' (关键词: {clicked['keyword']})")
        
        if activation_result['errors']:
            print(f"   错误: {len(activation_result['errors'])} 个")
            for error in activation_result['errors'][:3]:  # 只显示前3个错误
                print(f"     - {error}")
        
        # 步骤3: 重新检查页面状态
        print(f"\n🔍 步骤3: 重新检查页面状态...")
        
        updated_state = await page.evaluate("""
            () => {
                const state = {
                    fileInputs: document.querySelectorAll('input[type="file"]').length,
                    visibleInputs: 0,
                    blobImages: document.querySelectorAll('img[src*="blob:"]').length,
                    totalImages: document.querySelectorAll('img').length
                };
                
                document.querySelectorAll('input[type="file"]').forEach(input => {
                    if (input.offsetParent !== null) {
                        state.visibleInputs++;
                    }
                });
                
                return state;
            }
        """)
        
        print(f"📊 更新后状态:")
        print(f"   文件输入: {updated_state['fileInputs']} 个")
        print(f"   可见输入: {updated_state['visibleInputs']} 个")
        print(f"   预览图片: {updated_state['blobImages']} 个")
        print(f"   总图片: {updated_state['totalImages']} 个")
        
        # 步骤4: 如果检测到第二个文件输入，尝试注入尾帧
        if updated_state['fileInputs'] >= 2:
            print(f"\n💉 步骤4: 检测到第二个文件输入，尝试注入尾帧...")
            
            # 尾帧图片路径
            last_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (3).png"
            
            if os.path.exists(last_frame_path):
                last_frame_data = read_image_as_base64(last_frame_path)
                if last_frame_data:
                    success = await inject_tail_frame(page, last_frame_data)
                    if success:
                        print("✅ 尾帧注入成功！")
                    else:
                        print("❌ 尾帧注入失败")
                else:
                    print("❌ 尾帧数据读取失败")
            else:
                print("❌ 尾帧图片文件不存在")
        else:
            print(f"\n⚠️ 仍然只有 {updated_state['fileInputs']} 个文件输入")
            print("💡 建议:")
            print("   1. 手动查看页面，寻找'添加尾帧'或'参考帧'按钮")
            print("   2. 检查是否需要先完成首帧上传")
            print("   3. 确认账户是否有尾帧功能权限")
        
        # 步骤5: 提供修复建议
        print(f"\n💡 修复建议:")
        
        if len(page_analysis['fileInputs']) == 0:
            print("   ❌ 没有文件输入元素 - 页面可能未正确加载")
        elif len(page_analysis['fileInputs']) == 1:
            if page_analysis['textContent']['hasLastFrame']:
                print("   ⚠️ 页面包含'尾帧'文本但只有一个文件输入")
                print("   💡 可能需要先上传首帧才能激活尾帧区域")
            else:
                print("   ⚠️ 页面不包含'尾帧'文本")
                print("   💡 当前页面可能不支持尾帧功能")
        else:
            print("   ✅ 检测到多个文件输入，可以尝试注入尾帧")
        
        if len(relevant_buttons) > 0:
            print("   💡 发现相关按钮，可以尝试手动点击激活")
        
        return True
        
    except Exception as e:
        print(f"❌ 详细检查失败: {e}")
        return False


async def inject_tail_frame(page, last_frame_data):
    """注入尾帧"""
    try:
        # 注入JavaScript辅助函数
        await page.evaluate("""
            () => {
                if (!window.createFileFromBase64) {
                    window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                        try {
                            const byteCharacters = atob(base64Data);
                            const byteNumbers = new Array(byteCharacters.length);
                            for (let i = 0; i < byteCharacters.length; i++) {
                                byteNumbers[i] = byteCharacters.charCodeAt(i);
                            }
                            const byteArray = new Uint8Array(byteNumbers);
                            return new File([byteArray], fileName, { type: mimeType });
                        } catch (error) {
                            console.error('创建文件失败:', error);
                            return null;
                        }
                    };
                    
                    window.setFileToInput = function(input, file) {
                        try {
                            const dataTransfer = new DataTransfer();
                            dataTransfer.items.add(file);
                            input.files = dataTransfer.files;
                            
                            const changeEvent = new Event('change', { bubbles: true });
                            input.dispatchEvent(changeEvent);
                            
                            const inputEvent = new Event('input', { bubbles: true });
                            input.dispatchEvent(inputEvent);
                            
                            return true;
                        } catch (error) {
                            console.error('设置文件失败:', error);
                            return false;
                        }
                    };
                }
            }
        """)
        
        # 执行尾帧注入
        inject_result = await page.evaluate(f"""
            () => {{
                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');
                    if (inputs.length < 2) {{
                        return false;
                    }}
                    
                    const lastInput = inputs[1];
                    
                    // 强制显示
                    lastInput.style.display = 'block !important';
                    lastInput.style.visibility = 'visible !important';
                    lastInput.style.opacity = '1 !important';
                    
                    // 创建文件
                    const file = window.createFileFromBase64(
                        '{last_frame_data["data"]}',
                        '{last_frame_data["name"]}',
                        '{last_frame_data["type"]}'
                    );
                    
                    if (!file) return false;
                    
                    // 设置文件
                    return window.setFileToInput(lastInput, file);
                    
                }} catch (error) {{
                    console.error('尾帧注入失败:', error);
                    return false;
                }}
            }}
        """)
        
        return inject_result
        
    except Exception as e:
        print(f"❌ 尾帧注入异常: {e}")
        return False


async def main():
    """主函数"""
    
    success = await check_and_fix_webpage()
    
    if success:
        print("\n✅ 网页检查完成")
    else:
        print("\n❌ 网页检查失败")


if __name__ == "__main__":
    print("🔍 即梦AI网页状态检查与尾帧修复工具")
    print("=" * 60)
    
    asyncio.run(main())
