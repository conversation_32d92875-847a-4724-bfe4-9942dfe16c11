#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
即梦AI手动注入首尾帧图片 - 专门用于手动注入首尾帧图片的脚本
使用方法：
1. 确保Chrome浏览器已开启调试模式（端口9222）
2. 打开即梦AI页面：https://jimeng.jianying.com/ai-tool/generate?type=video
3. 修改下面的图片路径为您要上传的图片
4. 运行此脚本
"""

import asyncio
import os
import sys
import base64
import mimetypes
from pathlib import Path
from playwright.async_api import async_playwright


def read_image_as_base64(image_path: str):
    """读取图片并转换为Base64"""
    try:
        # 获取MIME类型
        mime_type, _ = mimetypes.guess_type(image_path)
        if not mime_type or not mime_type.startswith('image/'):
            mime_type = 'image/png'
        
        # 读取文件
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 转换为Base64
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        return {
            "name": os.path.basename(image_path),
            "data": base64_data,
            "type": mime_type,
            "size": len(image_data)
        }
    except Exception as e:
        print(f"❌ 读取图片失败: {e}")
        return None


async def inject_frames():
    """注入首尾帧图片"""
    
    # 🔧 配置区域 - 请修改这里的图片路径
    # ==========================================
    first_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\保留黑色底背景_保留楼体轮廓_楼体的风格转换成赛博朋克风格.png"
    last_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (3).png"
    # ==========================================
    
    print("🖼️ 即梦AI手动注入首尾帧图片")
    print("=" * 50)
    print(f"📸 首帧路径: {first_frame_path}")
    print(f"📸 首帧名称: {Path(first_frame_path).name}")
    print(f"📸 尾帧路径: {last_frame_path}")
    print(f"📸 尾帧名称: {Path(last_frame_path).name}")
    print()
    
    # 验证文件存在
    if not os.path.exists(first_frame_path):
        print(f"❌ 首帧图片文件不存在: {first_frame_path}")
        print("💡 请检查路径是否正确，或修改脚本中的图片路径")
        return False
    
    if not os.path.exists(last_frame_path):
        print(f"❌ 尾帧图片文件不存在: {last_frame_path}")
        print("💡 请检查路径是否正确，或修改脚本中的图片路径")
        return False
    
    print("✅ 图片文件验证通过")
    
    # 读取图片数据
    print("📤 读取图片数据...")
    first_frame_data = read_image_as_base64(first_frame_path)
    last_frame_data = read_image_as_base64(last_frame_path)
    
    if not first_frame_data or not last_frame_data:
        return False
    
    print(f"✅ 首帧数据读取成功: {first_frame_data['size']} 字节")
    print(f"✅ 尾帧数据读取成功: {last_frame_data['size']} 字节")
    
    # 检查Chrome调试端口
    debug_port = 9222
    print("🔍 检查Chrome调试端口...")
    
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("❌ Chrome调试端口未开启")
        print("💡 请先启动Chrome调试模式：")
        print("   chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug")
        return False
    
    print("✅ Chrome调试端口已开启")
    
    async with async_playwright() as p:
        try:
            print("🔗 连接到Chrome浏览器...")
            
            # 连接到现有Chrome实例
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            
            # 获取所有上下文和页面
            contexts = browser.contexts
            if not contexts:
                print("❌ 未找到浏览器上下文")
                return False
            
            # 查找即梦AI页面
            jimeng_page = None
            for context in contexts:
                for page in context.pages:
                    if 'jimeng.jianying.com' in page.url:
                        jimeng_page = page
                        print(f"✅ 找到即梦AI页面: {page.url}")
                        break
                if jimeng_page:
                    break
            
            if not jimeng_page:
                print("❌ 未找到即梦AI页面")
                print("💡 请先在Chrome中打开即梦AI页面：")
                print("   https://jimeng.jianying.com/ai-tool/generate?type=video")
                return False
            
            # 验证页面URL
            current_url = jimeng_page.url
            expected_url = "https://jimeng.jianying.com/ai-tool/generate?type=video"
            
            if not current_url.startswith("https://jimeng.jianying.com/ai-tool/generate"):
                print(f"⚠️ 当前页面URL不正确: {current_url}")
                print(f"💡 期望的URL: {expected_url}")
                print("🔄 正在跳转到正确页面...")
                
                try:
                    await jimeng_page.goto(expected_url)
                    await jimeng_page.wait_for_load_state('networkidle', timeout=30000)
                    print("✅ 页面跳转成功")
                except Exception as e:
                    print(f"❌ 页面跳转失败: {e}")
                    return False
            else:
                print("✅ 页面URL验证通过")
            
            # 执行注入
            success = await perform_injection(jimeng_page, first_frame_data, last_frame_data)
            
            if success:
                print("✅ 首尾帧图片注入成功！")
                print("🎉 请在浏览器中查看上传结果")
                return True
            else:
                print("❌ 首尾帧图片注入失败")
                return False
                
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False


async def perform_injection(page, first_frame_data, last_frame_data):
    """执行注入操作"""
    try:
        print("💉 开始注入首尾帧图片...")
        
        # 等待页面稳定
        await asyncio.sleep(3)
        
        # 步骤1: 注入JavaScript辅助函数
        print("🔧 步骤1: 注入JavaScript辅助函数...")
        
        await page.evaluate("""
            () => {
                console.log('💉 注入JavaScript辅助函数');
                
                // 创建文件对象的函数
                window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                    console.log('创建文件:', fileName, mimeType);
                    
                    try {
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        const file = new File([byteArray], fileName, { type: mimeType });
                        console.log('文件创建成功:', file.name, file.size, 'bytes');
                        return file;
                    } catch (error) {
                        console.error('创建文件失败:', error);
                        return null;
                    }
                };
                
                // 设置文件到input的函数
                window.setFileToInput = function(input, file) {
                    console.log('设置文件到input:', file.name);
                    
                    try {
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        input.files = dataTransfer.files;
                        
                        // 触发事件
                        const changeEvent = new Event('change', { bubbles: true });
                        input.dispatchEvent(changeEvent);
                        
                        const inputEvent = new Event('input', { bubbles: true });
                        input.dispatchEvent(inputEvent);
                        
                        console.log('文件设置成功，files.length:', input.files.length);
                        return true;
                    } catch (error) {
                        console.error('设置文件失败:', error);
                        return false;
                    }
                };
                
                console.log('✅ JavaScript辅助函数注入完成');
            }
        """)
        
        await asyncio.sleep(1)
        print("✅ JavaScript辅助函数注入完成")
        
        # 步骤2: 检查页面状态
        print("🔍 步骤2: 检查页面状态...")
        
        page_state = await page.evaluate("""
            () => {
                console.log('🔍 检查页面状态');
                
                const state = {
                    fileInputs: 0,
                    visibleInputs: 0,
                    blobImages: 0,
                    totalImages: 0,
                    hasFirstFrame: false,
                    hasLastFrame: false
                };
                
                // 检查文件输入
                const inputs = document.querySelectorAll('input[type="file"]');
                state.fileInputs = inputs.length;
                
                inputs.forEach(input => {
                    if (input.offsetParent !== null) {
                        state.visibleInputs++;
                    }
                });
                
                // 检查图片
                state.totalImages = document.querySelectorAll('img').length;
                state.blobImages = document.querySelectorAll('img[src*="blob:"]').length;
                
                // 检查页面文本
                const pageText = document.body.textContent;
                state.hasFirstFrame = pageText.includes('首帧');
                state.hasLastFrame = pageText.includes('尾帧');
                
                console.log('页面状态:', state);
                return state;
            }
        """)
        
        print(f"📊 页面状态:")
        print(f"   文件输入: {page_state['fileInputs']} 个")
        print(f"   可见输入: {page_state['visibleInputs']} 个")
        print(f"   总图片: {page_state['totalImages']} 个")
        print(f"   预览图片: {page_state['blobImages']} 个")
        print(f"   包含'首帧': {page_state['hasFirstFrame']}")
        print(f"   包含'尾帧': {page_state['hasLastFrame']}")
        
        if page_state['fileInputs'] == 0:
            print("❌ 页面上没有文件输入元素")
            return False
        
        # 步骤3: 执行首帧注入
        print("💉 步骤3: 执行首帧注入...")
        
        first_inject_result = await page.evaluate(f"""
            () => {{
                console.log('💉 开始注入首帧');
                
                try {{
                    // 查找文件输入元素
                    const inputs = document.querySelectorAll('input[type="file"]');
                    if (inputs.length === 0) {{
                        console.error('未找到文件输入元素');
                        return false;
                    }}
                    
                    const firstInput = inputs[0];
                    console.log('找到首帧输入元素:', firstInput);
                    
                    // 强制显示元素
                    firstInput.style.display = 'block !important';
                    firstInput.style.visibility = 'visible !important';
                    firstInput.style.opacity = '1 !important';
                    firstInput.style.position = 'static !important';
                    
                    // 创建首帧文件对象
                    const file = window.createFileFromBase64(
                        '{first_frame_data["data"]}',
                        '{first_frame_data["name"]}',
                        '{first_frame_data["type"]}'
                    );
                    
                    if (!file) {{
                        console.error('首帧文件创建失败');
                        return false;
                    }}
                    
                    // 设置文件到input
                    const success = window.setFileToInput(firstInput, file);
                    
                    if (success) {{
                        console.log('✅ 首帧注入成功');
                        return true;
                    }} else {{
                        console.error('❌ 首帧注入失败');
                        return false;
                    }}
                    
                }} catch (error) {{
                    console.error('首帧注入异常:', error);
                    return false;
                }}
            }}
        """)
        
        await asyncio.sleep(5)  # 等待首帧处理
        
        if not first_inject_result:
            print("❌ 首帧注入失败")
            return False
        
        print("✅ 首帧注入成功")
        
        # 步骤4: 智能激活尾帧区域
        print("🔧 步骤4: 智能激活尾帧区域...")

        # 首先尝试点击尾帧相关按钮
        activation_success = await page.evaluate("""
            () => {
                console.log('🔧 尝试激活尾帧区域');

                let activated = false;

                // 策略1: 查找并点击"尾帧"按钮
                const tailFrameButtons = document.querySelectorAll('*');
                for (const el of tailFrameButtons) {
                    const text = (el.textContent || '').trim();
                    if (text === '尾帧' && el.offsetParent && el.tagName !== 'INPUT') {
                        try {
                            console.log('点击尾帧按钮:', el);
                            el.click();
                            activated = true;
                            break;
                        } catch (e) {
                            console.error('点击尾帧按钮失败:', e);
                        }
                    }
                }

                // 策略2: 查找参考帧相关元素
                if (!activated) {
                    const referenceElements = document.querySelectorAll('[class*="reference"], [class*="tail"], [class*="last"]');
                    for (const el of referenceElements) {
                        if (el.offsetParent && el.tagName !== 'INPUT') {
                            try {
                                console.log('点击参考帧元素:', el.className);
                                el.click();
                                activated = true;
                                break;
                            } catch (e) {
                                console.error('点击参考帧元素失败:', e);
                            }
                        }
                    }
                }

                // 策略3: 查找上传区域的父元素
                if (!activated) {
                    const uploadParents = document.querySelectorAll('[class*="upload"]');
                    for (const parent of uploadParents) {
                        const children = parent.querySelectorAll('*');
                        for (const child of children) {
                            const text = (child.textContent || '').trim();
                            if ((text.includes('尾帧') || text.includes('参考')) && child.offsetParent) {
                                try {
                                    console.log('点击上传区域子元素:', text);
                                    child.click();
                                    activated = true;
                                    break;
                                } catch (e) {
                                    console.error('点击上传区域子元素失败:', e);
                                }
                            }
                        }
                        if (activated) break;
                    }
                }

                return activated;
            }
        """)

        if activation_success:
            print("✅ 成功激活尾帧区域")
        else:
            print("⚠️ 自动激活失败，尝试其他方法")

        await asyncio.sleep(3)  # 等待页面响应

        # 检查是否有新的文件输入出现
        tail_frame_found = False

        for attempt in range(20):  # 等待10秒
            current_check = await page.evaluate("""
                () => {
                    const state = {
                        fileInputs: document.querySelectorAll('input[type="file"]').length,
                        visibleInputs: 0,
                        blobImages: document.querySelectorAll('img[src*="blob:"]').length
                    };

                    document.querySelectorAll('input[type="file"]').forEach(input => {
                        if (input.offsetParent !== null || getComputedStyle(input).display !== 'none') {
                            state.visibleInputs++;
                        }
                    });

                    return state;
                }
            """)

            if current_check['fileInputs'] >= 2:
                print("✅ 检测到第二个文件输入！")
                tail_frame_found = True
                break

            if attempt % 10 == 0 and attempt > 0:
                print(f"⏳ 等待尾帧区域... (第{attempt//2 + 1}秒)")

            await asyncio.sleep(0.5)
        
        # 步骤5: 智能尾帧注入
        if tail_frame_found:
            print("💉 步骤5: 智能尾帧注入...")

            last_inject_result = await page.evaluate(f"""
                () => {{
                    console.log('💉 开始智能尾帧注入');

                    try {{
                        const inputs = document.querySelectorAll('input[type="file"]');
                        console.log('找到文件输入数量:', inputs.length);

                        if (inputs.length < 2) {{
                            console.error('未找到第二个文件输入');
                            return false;
                        }}

                        // 尝试所有可能的文件输入（从第二个开始）
                        for (let i = 1; i < inputs.length; i++) {{
                            const input = inputs[i];
                            console.log(`尝试文件输入 ${{i}}:`, input);

                            // 强制显示元素
                            input.style.display = 'block !important';
                            input.style.visibility = 'visible !important';
                            input.style.opacity = '1 !important';
                            input.style.position = 'static !important';
                            input.style.width = 'auto !important';
                            input.style.height = 'auto !important';

                            // 创建尾帧文件对象
                            const file = window.createFileFromBase64(
                                '{last_frame_data["data"]}',
                                '{last_frame_data["name"]}',
                                '{last_frame_data["type"]}'
                            );

                            if (!file) {{
                                console.error('尾帧文件创建失败');
                                continue;
                            }}

                            // 设置文件到input
                            const success = window.setFileToInput(input, file);

                            if (success) {{
                                console.log(`✅ 尾帧注入成功到输入 ${{i}}`);
                                return true;
                            }} else {{
                                console.log(`❌ 输入 ${{i}} 注入失败，尝试下一个`);
                            }}
                        }}

                        console.error('所有文件输入都注入失败');
                        return false;

                    }} catch (error) {{
                        console.error('尾帧注入异常:', error);
                        return false;
                    }}
                }}
            """)

            await asyncio.sleep(3)

            if last_inject_result:
                print("✅ 尾帧注入成功")
            else:
                print("❌ 尾帧注入失败")
        else:
            print("⚠️ 尾帧区域未自动出现")
            print("🔧 尝试手动激活尾帧功能...")

            # 最后尝试：手动激活尾帧功能
            manual_activation = await page.evaluate("""
                () => {
                    console.log('🔧 手动激活尾帧功能');

                    // 查找所有可能的激活元素
                    const possibleElements = [
                        ...document.querySelectorAll('[class*="tail"]'),
                        ...document.querySelectorAll('[class*="reference"]'),
                        ...document.querySelectorAll('[class*="last"]'),
                        ...document.querySelectorAll('*')
                    ].filter(el => {
                        const text = (el.textContent || '').trim();
                        return text === '尾帧' || text === '参考帧' || text === '添加尾帧';
                    });

                    console.log('找到可能的激活元素:', possibleElements.length);

                    for (const el of possibleElements) {
                        if (el.offsetParent && el.tagName !== 'INPUT') {
                            try {
                                console.log('尝试点击:', el.textContent);
                                el.click();

                                // 等待一下看是否有新的文件输入出现
                                setTimeout(() => {
                                    const newInputCount = document.querySelectorAll('input[type="file"]').length;
                                    console.log('点击后文件输入数量:', newInputCount);
                                }, 1000);

                                return true;
                            } catch (e) {
                                console.error('点击失败:', e);
                            }
                        }
                    }

                    return false;
                }
            """)

            if manual_activation:
                print("✅ 尝试了手动激活")
                await asyncio.sleep(3)

                # 重新检查是否有新的文件输入
                final_check = await page.evaluate("""
                    () => {
                        return document.querySelectorAll('input[type="file"]').length;
                    }
                """)

                if final_check >= 2:
                    print("🎉 手动激活成功！现在有多个文件输入")
                    # 可以在这里再次尝试注入尾帧
                else:
                    print("⚠️ 手动激活后仍只有一个文件输入")
            else:
                print("❌ 未找到可激活的尾帧元素")

            print("💡 建议手动操作：")
            print("   1. 在浏览器中查找'尾帧'或'参考帧'按钮")
            print("   2. 点击该按钮激活尾帧上传区域")
            print("   3. 然后重新运行脚本进行尾帧注入")
        
        # 步骤6: 最终验证
        print("🔍 步骤6: 最终验证...")
        
        final_state = await page.evaluate("""
            () => {
                console.log('🔍 验证注入结果');
                
                const result = {
                    fileInputs: 0,
                    blobImages: 0,
                    totalImages: 0,
                    success: false
                };
                
                result.fileInputs = document.querySelectorAll('input[type="file"]').length;
                result.totalImages = document.querySelectorAll('img').length;
                result.blobImages = document.querySelectorAll('img[src*="blob:"]').length;
                result.success = result.blobImages >= 1;
                
                console.log('验证结果:', result);
                return result;
            }
        """)
        
        print(f"📊 最终验证结果:")
        print(f"   文件输入: {final_state['fileInputs']} 个")
        print(f"   总图片: {final_state['totalImages']} 个")
        print(f"   预览图片: {final_state['blobImages']} 个")
        print(f"   注入成功: {'✅' if final_state['success'] else '❌'}")
        
        if final_state['blobImages'] >= 2:
            print("🎉 首尾帧都注入成功！")
            return True
        elif final_state['blobImages'] == 1:
            print("⚠️ 只有首帧注入成功")
            return True  # 部分成功也算成功
        else:
            print("❌ 注入失败")
            return False
        
    except Exception as e:
        print(f"❌ 注入操作失败: {e}")
        return False


async def main():
    """主函数"""
    
    print("🚀 启动即梦AI首尾帧图片注入工具")
    print()
    
    success = await inject_frames()
    
    if success:
        print("\n🎉 首尾帧图片注入成功！")
        print("💡 请在Chrome浏览器中查看即梦AI页面")
        print("🚀 现在可以设置提示词并生成视频")
    else:
        print("\n❌ 首尾帧图片注入失败")
        print("💡 请检查：")
        print("   1. Chrome是否开启调试模式")
        print("   2. 即梦AI页面是否已打开")
        print("   3. 图片路径是否正确")
        print("   4. 页面URL是否正确")


if __name__ == "__main__":
    print("=" * 60)
    print("🖼️  即梦AI首尾帧图片手动注入工具")
    print("=" * 60)
    print("📝 使用说明：")
    print("   1. 确保Chrome开启调试模式（端口9222）")
    print("   2. 打开即梦AI页面")
    print("   3. 修改脚本中的图片路径")
    print("   4. 运行此脚本")
    print("=" * 60)
    print()
    
    asyncio.run(main())
