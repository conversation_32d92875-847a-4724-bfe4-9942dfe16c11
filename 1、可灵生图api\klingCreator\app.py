#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI图生图Web应用
"""

import os
import json
import time
import uuid
import re
import threading
import subprocess
import asyncio
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
from kling import ImageGen
from cookie_utils import get_cookie, check_cookie
import requests
from datetime import datetime
from database_models import (
    db_manager, db, uploaded_image_model, generated_image_model,
    user_action_model, calculate_file_md5, UploadedImageModel,
    GeneratedImageModel, UserActionModel, video_task_model, video_segment_model
)
from version import get_version, get_version_info

def log_network_request(url, status=None, method="GET", extra_info=""):
    """将网络请求记录到文件"""
    try:
        log_file = Path(__file__).parent / "网络请求.txt"
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        with open(log_file, 'a', encoding='utf-8') as f:
            if status:
                f.write(f"[{timestamp}] {method} {status} - {url}\n")
            else:
                f.write(f"[{timestamp}] {method} - {url}\n")

            if extra_info:
                f.write(f"    {extra_info}\n")
            f.write("\n")

    except Exception as e:
        # 记录网络请求失败时不输出到终端，避免干扰
        pass

class ChromeNetworkMonitor:
    """Chrome调试浏览器网络监控器"""

    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None

    def start_monitoring(self):
        """启动网络监控"""
        if not self.monitoring:
            self.monitoring = True
            self.monitor_thread = threading.Thread(target=self._monitor_chrome_network, daemon=True)
            self.monitor_thread.start()
            log_network_request("CHROME_MONITOR_START", None, "INFO", "Chrome网络监控启动")

    def stop_monitoring(self):
        """停止网络监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1)
        log_network_request("CHROME_MONITOR_STOP", None, "INFO", "Chrome网络监控停止")

    def _monitor_chrome_network(self):
        """监控Chrome网络请求的主循环"""
        import asyncio

        # 在新线程中运行异步监控
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            loop.run_until_complete(self._async_monitor())
        except Exception as e:
            log_network_request("MONITOR_ERROR", None, "ERROR", f"监控异常: {e}")
        finally:
            loop.close()

    async def _async_monitor(self):
        """异步监控Chrome网络请求"""
        try:
            from playwright.async_api import async_playwright

            while self.monitoring:
                try:
                    async with async_playwright() as p:
                        log_network_request("CHROME_CONNECT_ATTEMPT", None, "INFO", "尝试连接Chrome调试端口...")

                        # 连接到Chrome调试端口
                        browser = await p.chromium.connect_over_cdp("http://localhost:9222")
                        log_network_request("CHROME_CONNECTED", None, "INFO", "成功连接到Chrome调试浏览器")

                        # 获取所有页面
                        contexts = browser.contexts
                        if contexts:
                            context = contexts[0]
                            pages = context.pages

                            if pages:
                                page = pages[0]
                                log_network_request("PAGE_MONITOR_START", None, "INFO", f"开始监控页面: {page.url}")

                                # 设置网络请求监听
                                def handle_response(response):
                                    url = response.url
                                    status = response.status
                                    method = response.request.method

                                    # 记录所有网络请求
                                    log_network_request(url, status, method)

                                    # 记录关键请求到文件（不再输出到控制台）
                                    if any(keyword in url.lower() for keyword in ['jimeng', 'jianying', 'vlabvod', 'video', 'api']):
                                        url_display = url[:80] + '...' if len(url) > 80 else url
                                        log_network_request(url, status, method, f"[关键请求] {method} {status} - {url_display}")

                                    # 特别标记视频文件
                                    if any(keyword in url.lower() for keyword in ['vlabvod.com', '.mp4']) and 'video' in url:
                                        log_network_request(url, status, method, "*** 视频文件 ***")

                                page.on("response", handle_response)

                                # 持续监控
                                while self.monitoring:
                                    await asyncio.sleep(1)
                            else:
                                log_network_request("NO_PAGES", None, "INFO", "Chrome中没有打开的页面，等待页面加载...")
                                await asyncio.sleep(5)
                        else:
                            log_network_request("NO_CONTEXT", None, "INFO", "Chrome中没有可用的上下文，等待...")
                            await asyncio.sleep(5)

                except Exception as e:
                    log_network_request("CHROME_CONNECTION_FAILED", None, "ERROR", f"连接失败: {e}")
                    log_network_request("CHROME_DEBUG_TIP", None, "INFO", "请确保Chrome以调试模式启动: chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug")

                    # 等待一段时间后重试
                    await asyncio.sleep(10)

        except ImportError:
            log_network_request("PLAYWRIGHT_MISSING", None, "ERROR", "缺少playwright依赖，无法启动网络监控")

# 全局网络监控器实例
chrome_monitor = ChromeNetworkMonitor()

app = Flask(__name__)
app.config['SECRET_KEY'] = 'kling-ai-image-generator'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 配置目录
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'web_output'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# 并发生成相关
executor = ThreadPoolExecutor(max_workers=5)  # 最多5个并发任务
generation_tasks = {}  # 存储生成任务状态
task_lock = threading.Lock()  # 线程锁
last_submit_times = {}  # 记录每个任务的最后提交时间
SUBMIT_INTERVAL = 6  # 提交间隔6秒

# 初始化数据库
def init_database():
    """初始化数据库连接"""
    if not db_manager.connect():
        print("⚠️ 数据库连接失败，某些功能可能不可用")
        return False
    else:
        print("✅ 数据库连接成功")
        return True

# 提示词预设
PROMPT_TEMPLATES = [
    {
        "id": "bio_building",
        "name": "生物发光建筑",
        "prompt": "一座完全由生物发光植物构成的奇幻建筑，孤立在纯黑色背景中。巨大的、发光的奇异花朵和流光溢彩的藤蔓构成了建筑的主体结构，半透明的叶片下透出柔和的内部光芒。空气中漂浮着由它散发出的点点荧光。主色调为梦幻般的翠绿色、宝蓝色和淡紫色，充满生命力。幻想概念艺术，强烈的发光效果，细节丰细，固定视角。"
    },
    {
        "id": "steampunk_fortress",
        "name": "蒸汽朋克堡垒",
        "prompt": "一座孤立在纯黑色空间中的蒸汽朋克机械堡垒。建筑内部的锅炉透过格栅和窗口发出炽热的橙红色光芒，裸露的黄铜管道和玻璃真空管闪烁着微光。复杂的齿轮结构缓慢转动，表面有精密的金属质感和铆钉。整体色调为浓郁的古铜色和炽热的橙色，充满工业动力美感。数字绘画，高度详细，4K，固定机位，纯黑色背景。"
    },
    {
        "id": "cyberpunk_style",
        "name": "赛博朋克风格",
        "prompt": "保留黑色底背景，楼体的风格转换成赛博朋克风格，绚丽多彩，线条丰富"
    }
]

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def sanitize_filename_keep_chinese(filename):
    """清理文件名，保留中文字符"""
    # 移除不安全的字符，但保留中文、英文、数字、点、横线、下划线、空格
    safe_chars = re.sub(r'[<>:"/\\|?*]', '', filename)
    # 移除多余的空格，但保留单个空格
    safe_chars = re.sub(r'\s+', ' ', safe_chars).strip()
    # 如果文件名为空或只有扩展名，使用默认名称
    if not safe_chars or safe_chars.startswith('.'):
        safe_chars = f"image_{uuid.uuid4().hex[:8]}{safe_chars}"
    return safe_chars

def get_unique_filename(base_name, extension, output_dir):
    """生成唯一的文件名，避免覆盖，保留中文"""
    # 清理基础文件名，保留中文
    clean_base_name = sanitize_filename_keep_chinese(base_name)

    counter = 1
    while True:
        filename = f"{clean_base_name}_{counter}.{extension}"
        filepath = os.path.join(output_dir, filename)
        if not os.path.exists(filepath):
            return filename
        counter += 1

def get_client_ip():
    """获取客户端IP地址"""
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

class ConcurrentImageGenerator:
    """并发图片生成器"""

    def __init__(self):
        self.cookie = get_cookie()
        if not self.cookie or not check_cookie():
            raise Exception("Cookie无效或不存在")

    def generate_single_image(self, task_id, image_path, prompt, style_name, upload_id, base_name):
        """生成单张图片（在线程中运行）"""
        # 记录任务开始时间（包含所有等待和重试时间）
        task_start_time = time.time()

        try:
            # 检查并等待间隔时间
            with task_lock:
                current_time = time.time()
                elapsed_time = int(current_time - task_start_time)
                if task_id in last_submit_times:
                    time_since_last = current_time - last_submit_times[task_id]
                    if time_since_last < SUBMIT_INTERVAL:
                        wait_time = SUBMIT_INTERVAL - time_since_last
                        generation_tasks[task_id].update({
                            'status': 'waiting',
                            'start_time': task_start_time,
                            'elapsed_time': elapsed_time,
                            'message': f'等待间隔时间，还需 {int(wait_time)} 秒...'
                        })

            # 等待间隔时间
            if task_id in last_submit_times:
                time_since_last = time.time() - last_submit_times[task_id]
                if time_since_last < SUBMIT_INTERVAL:
                    wait_time = SUBMIT_INTERVAL - time_since_last
                    time.sleep(wait_time)

            # 记录提交时间
            with task_lock:
                last_submit_times[task_id] = time.time()
                current_time = time.time()
                elapsed_time = int(current_time - task_start_time)
                if task_id in generation_tasks:
                    generation_tasks[task_id].update({
                        'status': 'generating',
                        'start_time': task_start_time,  # 保持原始开始时间
                        'elapsed_time': elapsed_time,
                        'message': '正在生成图片...'
                    })

            # 创建新的生成器实例（每个线程独立）
            generator = ImageGen(self.cookie)

            # 设置请求头
            generator.session.headers.update({
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh",
                "content-type": "application/json",
                "time-zone": "Asia/Shanghai",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            })

            # 上传图片
            with task_lock:
                if task_id in generation_tasks:
                    generation_tasks[task_id]['message'] = '正在上传图片...'

            image_payload_url = generator.image_uploader(image_path)

            # 构建请求载荷
            payload = {
                "type": "mmu_img2img_aiweb",
                "inputs": [
                    {
                        "inputType": "URL",
                        "url": image_payload_url,
                        "name": "input"
                    }
                ],
                "arguments": [
                    {
                        "name": "biz",
                        "value": "klingai"
                    },
                    {
                        "name": "prompt",
                        "value": prompt
                    },
                    {
                        "name": "imageCount",
                        "value": "1"
                    },
                    {
                        "name": "kolors_version",
                        "value": "2.0"
                    },
                    {
                        "name": "style",
                        "value": "默认"
                    },
                    {
                        "name": "referenceType",
                        "value": "mmu_img2img_aiweb_v20_stylize"
                    }
                ]
            }

            # 发送请求并重试获取请求ID
            request_id = None
            max_retries = 50  # 最多重试50次
            retry_count = 0

            while not request_id and retry_count < max_retries:
                try:
                    with task_lock:
                        if task_id in generation_tasks:
                            if retry_count == 0:
                                generation_tasks[task_id]['message'] = '正在提交生成请求...'
                            else:
                                generation_tasks[task_id]['message'] = f'重试获取请求ID ({retry_count + 1}/{max_retries})...'

                    response = generator.session.post(
                        generator.submit_url,
                        json=payload,
                        headers=generator.session.headers
                    )

                    if not response.ok:
                        raise Exception(f"请求失败: {response.status_code} - {response.text}")

                    response_body = response.json()
                    data = response_body.get("data")

                    if data and data.get("status") == 7:
                        message = data.get("message")
                        raise Exception(f"请求失败: {message}")

                    request_id = data.get("task", {}).get("id") if data else None

                    if not request_id:
                        retry_count += 1
                        if retry_count < max_retries:
                            print(f"任务 {task_id}: 无法获取请求ID，11秒后重试 ({retry_count}/{max_retries})")

                            # 倒计时重试，同时更新任务状态
                            for countdown in range(11, 0, -1):
                                current_time = time.time()
                                elapsed_time = int(current_time - task_start_time)
                                with task_lock:
                                    if task_id in generation_tasks:
                                        generation_tasks[task_id]['message'] = f'无法获取请求ID，{countdown}秒后重试 ({retry_count}/{max_retries})...'
                                        generation_tasks[task_id]['elapsed_time'] = elapsed_time
                                time.sleep(1)
                        else:
                            raise Exception("多次尝试后仍无法获取请求ID")
                    else:
                        print(f"任务 {task_id}: 成功获取请求ID: {request_id}")
                        break

                except Exception as e:
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"任务 {task_id}: 请求异常，11秒后重试: {str(e)}")

                        # 倒计时重试，同时更新任务状态
                        for countdown in range(11, 0, -1):
                            current_time = time.time()
                            elapsed_time = int(current_time - task_start_time)
                            with task_lock:
                                if task_id in generation_tasks:
                                    generation_tasks[task_id]['message'] = f'请求异常，{countdown}秒后重试 ({retry_count}/{max_retries})...'
                                    generation_tasks[task_id]['elapsed_time'] = elapsed_time
                            time.sleep(1)
                    else:
                        raise Exception(f"多次重试后仍然失败: {str(e)}")

            if not request_id:
                raise Exception("无法获取请求ID")

            # 等待结果
            with task_lock:
                if task_id in generation_tasks:
                    current_time = time.time()
                    elapsed_time = int(current_time - task_start_time)
                    generation_tasks[task_id]['message'] = '正在生成中，请稍候...'
                    generation_tasks[task_id]['elapsed_time'] = elapsed_time

            while True:
                current_time = time.time()
                elapsed_time = int(current_time - task_start_time)  # 使用任务开始时间

                # 更新已用时间
                with task_lock:
                    if task_id in generation_tasks:
                        generation_tasks[task_id]['elapsed_time'] = elapsed_time

                if elapsed_time > 600:  # 10分钟超时
                    raise Exception("请求超时")

                image_data, status = generator.fetch_metadata(request_id)

                if status.name == "PENDING":
                    time.sleep(3)
                elif status.name == "FAILED":
                    raise Exception("生成失败")
                else:
                    works = image_data.get("works", [])
                    if not works:
                        raise Exception("未找到生成的图片")

                    resource = works[0].get("resource", {}).get("resource")
                    if resource:
                        # 下载并保存图片
                        response = requests.get(resource)
                        if response.status_code == 200:
                            # 生成唯一文件名
                            output_filename = get_unique_filename(base_name, 'png', OUTPUT_FOLDER)
                            output_path = os.path.join(OUTPUT_FOLDER, output_filename)

                            with open(output_path, 'wb') as f:
                                f.write(response.content)

                            # 保存到数据库
                            generated_id = None
                            if upload_id:
                                try:
                                    generated_id = generated_image_model.add_generated_image(
                                        upload_id=upload_id,
                                        generated_filename=output_filename,
                                        generated_path=output_path,
                                        style_name=style_name,
                                        prompt=prompt
                                    )
                                except Exception as e:
                                    print(f"数据库保存失败: {e}")

                            end_time = time.time()
                            total_time = int(end_time - task_start_time)  # 使用任务开始时间

                            # 更新任务状态为完成
                            with task_lock:
                                if task_id in generation_tasks:
                                    generation_tasks[task_id].update({
                                        'status': 'completed',
                                        'filename': output_filename,
                                        'generated_id': generated_id,
                                        'total_time': total_time,
                                        'message': f'生成完成，用时 {total_time} 秒'
                                    })

                            return True
                        else:
                            raise Exception(f"下载图片失败: {response.status_code}")
                    else:
                        raise Exception("无法获取图片URL")

        except Exception as e:
            end_time = time.time()
            total_time = int(end_time - task_start_time)  # 使用任务开始时间

            # 更新任务状态为失败
            with task_lock:
                if task_id in generation_tasks:
                    generation_tasks[task_id].update({
                        'status': 'failed',
                        'error': str(e),
                        'total_time': total_time,
                        'message': f'生成失败: {str(e)}'
                    })

            return False

class WebImageGenerator:
    """Web版图生图生成器"""
    
    def __init__(self):
        self.cookie = get_cookie()
        if not self.cookie or not check_cookie():
            raise Exception("Cookie无效或不存在")
        self.generator = ImageGen(self.cookie)
    
    def generate_image(self, image_path, prompt):
        """生成单张图片"""
        # 设置请求头
        self.generator.session.headers.update({
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh",
            "content-type": "application/json",
            "time-zone": "Asia/Shanghai",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        })
        
        # 上传图片
        image_payload_url = self.generator.image_uploader(image_path)
        
        # 构建请求载荷
        payload = {
            "type": "mmu_img2img_aiweb",
            "inputs": [
                {
                    "inputType": "URL",
                    "url": image_payload_url,
                    "name": "input"
                }
            ],
            "arguments": [
                {
                    "name": "biz",
                    "value": "klingai"
                },
                {
                    "name": "prompt",
                    "value": prompt
                },
                {
                    "name": "imageCount",
                    "value": "1"
                },
                {
                    "name": "kolors_version",
                    "value": "2.0"
                },
                {
                    "name": "style",
                    "value": "默认"
                },
                {
                    "name": "referenceType",
                    "value": "mmu_img2img_aiweb_v20_stylize"
                }
            ]
        }
        
        # 发送请求
        response = self.generator.session.post(
            self.generator.submit_url,
            json=payload,
            headers=self.generator.session.headers
        )
        
        if not response.ok:
            raise Exception(f"请求失败: {response.status_code} - {response.text}")
        
        response_body = response.json()
        data = response_body.get("data")
        
        if data and data.get("status") == 7:
            message = data.get("message")
            raise Exception(f"请求失败: {message}")
        
        request_id = data.get("task", {}).get("id") if data else None
        
        if not request_id:
            raise Exception("无法获取请求ID")
        
        # 等待结果
        start_wait = time.time()
        while True:
            if int(time.time() - start_wait) > 600:  # 10分钟超时
                raise Exception("请求超时")
            
            image_data, status = self.generator.fetch_metadata(request_id)
            
            if status.name == "PENDING":
                time.sleep(3)
            elif status.name == "FAILED":
                raise Exception("生成失败")
            else:
                works = image_data.get("works", [])
                if not works:
                    raise Exception("未找到生成的图片")
                
                resource = works[0].get("resource", {}).get("resource")
                if resource:
                    return resource
                else:
                    raise Exception("无法获取图片URL")

@app.route('/')
def index():
    """主页"""
    return render_template('index.html', prompt_templates=PROMPT_TEMPLATES)

@app.route('/gallery')
def gallery():
    """画廊页面"""
    return render_template('gallery.html')

@app.route('/details/<int:upload_id>')
def details(upload_id):
    """图片详情页面"""
    return render_template('details.html')

@app.route('/test')
def test():
    """测试页面"""
    return render_template('test.html')

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """上传文件"""
    try:
        print("收到上传请求")
        print(f"请求文件: {request.files}")

        if 'file' not in request.files:
            print("错误: 没有文件字段")
            return jsonify({'error': '没有文件'}), 400

        file = request.files['file']
        print(f"文件名: {file.filename}")

        if file.filename == '':
            print("错误: 文件名为空")
            return jsonify({'error': '没有选择文件'}), 400

        if file and allowed_file(file.filename):
            # 保留中文文件名
            original_filename = file.filename
            print(f"原始文件名: {original_filename}")

            # 使用新的文件名处理，保留中文字符
            name, ext = os.path.splitext(original_filename)
            safe_name = sanitize_filename_keep_chinese(name)
            unique_id = str(uuid.uuid4())[:8]
            unique_filename = f"{safe_name}_{unique_id}{ext}"

            filepath = os.path.join(UPLOAD_FOLDER, unique_filename)
            print(f"保存路径: {filepath}")

            # 确保目录存在
            os.makedirs(UPLOAD_FOLDER, exist_ok=True)

            file.save(filepath)
            print(f"文件保存成功: {filepath}")

            # 保存到数据库
            try:
                upload_id = uploaded_image_model.add_image(
                    filename=original_filename,
                    safe_filename=unique_filename,
                    file_path=filepath
                )
                print(f"数据库记录创建成功，ID: {upload_id}")

                # 计算MD5用于前端
                file_md5 = calculate_file_md5(filepath)

                return jsonify({
                    'success': True,
                    'filename': unique_filename,
                    'original_name': original_filename,
                    'safe_name': safe_name,
                    'upload_id': upload_id,
                    'file_md5': file_md5
                })

            except Exception as e:
                print(f"数据库操作失败: {e}")
                # 即使数据库失败，文件上传仍然成功
                return jsonify({
                    'success': True,
                    'filename': unique_filename,
                    'original_name': original_filename,
                    'safe_name': safe_name,
                    'upload_id': None,
                    'warning': '数据库记录失败，但文件上传成功'
                })
        else:
            print(f"错误: 不支持的文件格式 {file.filename}")
            return jsonify({'error': '不支持的文件格式'}), 400

    except Exception as e:
        print(f"上传异常: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/generate', methods=['POST'])
def generate_images_concurrent():
    """并发生成图片"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        prompts = data.get('prompts', [])

        if not filename or not prompts:
            return jsonify({'error': '缺少必要参数'}), 400

        filepath = os.path.join(UPLOAD_FOLDER, filename)
        if not os.path.exists(filepath):
            return jsonify({'error': '文件不存在'}), 400

        # 检查积分余额
        print("🔍 检查积分余额...")
        try:
            cookie = get_cookie()
            if not cookie or not check_cookie():
                return jsonify({'error': 'Cookie无效，无法检查积分余额'}), 400

            # 创建临时生成器来检查积分
            temp_generator = ImageGen(cookie)
            current_balance = temp_generator.get_account_point()

            # 计算需要的积分（每张图片大约需要1积分，这里可以根据实际情况调整）
            required_points = len(prompts) * 1.0  # 每个提示词生成一张图片需要1积分

            print(f"💰 当前积分余额: {current_balance}")
            print(f"📊 需要积分: {required_points}")
            print(f"🎯 生成任务数: {len(prompts)}")

            if current_balance < required_points:
                return jsonify({
                    'error': f'积分余额不足！当前余额: {current_balance} 积分，需要: {required_points} 积分',
                    'current_balance': current_balance,
                    'required_points': required_points,
                    'insufficient_balance': True
                }), 400

            print(f"✅ 积分余额充足，开始生成图片")

        except Exception as e:
            print(f"⚠️ 积分检查失败: {e}")
            return jsonify({'error': f'积分检查失败: {str(e)}'}), 500

        # 获取原始文件名和上传记录
        original_name = data.get('original_name', filename)
        upload_id = data.get('upload_id')
        base_name = os.path.splitext(original_name)[0]

        # 如果没有upload_id，尝试通过文件MD5查找
        if not upload_id:
            file_md5 = calculate_file_md5(filepath)
            upload_record = uploaded_image_model.get_by_md5(file_md5)
            if upload_record:
                upload_id = upload_record['id']
                print(f"通过MD5找到上传记录，ID: {upload_id}")

        # 初始化生成器
        try:
            generator = ConcurrentImageGenerator()
        except Exception as e:
            return jsonify({'error': f'生成器初始化失败: {str(e)}'}), 500

        # 创建并发任务
        task_ids = []

        for i, prompt_data in enumerate(prompts):
            # 支持新的数据格式
            if isinstance(prompt_data, dict):
                prompt = prompt_data.get('prompt', '')
                style_name = prompt_data.get('style_name', '默认')
            else:
                prompt = prompt_data
                style_name = '默认'

            # 生成任务ID
            task_id = f"{uuid.uuid4().hex[:8]}_{i}"
            task_ids.append(task_id)

            # 初始化任务状态
            with task_lock:
                generation_tasks[task_id] = {
                    'task_id': task_id,
                    'status': 'pending',
                    'prompt': prompt,
                    'style_name': style_name,
                    'index': i,
                    'start_time': None,
                    'elapsed_time': 0,
                    'total_time': None,
                    'message': '等待开始...',
                    'filename': None,
                    'generated_id': None,
                    'error': None
                }

            # 提交到线程池
            executor.submit(
                generator.generate_single_image,
                task_id, filepath, prompt, style_name, upload_id, base_name
            )

        return jsonify({
            'success': True,
            'message': f'已提交 {len(task_ids)} 个并发生成任务',
            'task_ids': task_ids,
            'total_tasks': len(task_ids)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/task_status', methods=['POST'])
def get_task_status():
    """获取任务状态"""
    try:
        data = request.get_json()
        task_ids = data.get('task_ids', [])

        if not task_ids:
            return jsonify({'error': '缺少任务ID'}), 400

        # 获取任务状态
        task_statuses = []
        with task_lock:
            for task_id in task_ids:
                if task_id in generation_tasks:
                    task_statuses.append(generation_tasks[task_id].copy())
                else:
                    task_statuses.append({
                        'task_id': task_id,
                        'status': 'not_found',
                        'message': '任务不存在'
                    })

        return jsonify({
            'success': True,
            'tasks': task_statuses
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/clear_completed_tasks', methods=['POST'])
def clear_completed_tasks():
    """清理已完成的任务"""
    try:
        with task_lock:
            completed_tasks = [
                task_id for task_id, task in generation_tasks.items()
                if task['status'] in ['completed', 'failed']
            ]

            for task_id in completed_tasks:
                del generation_tasks[task_id]

        return jsonify({
            'success': True,
            'cleared_count': len(completed_tasks)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/gallery')
def get_gallery():
    """获取画廊数据"""
    try:
        upload_model = UploadedImageModel(db)
        generated_model = GeneratedImageModel(db)
        user_action_model_instance = UserActionModel(db)

        # 获取所有上传图片
        all_uploads = upload_model.get_all_uploads()

        # 只保留有生成图片的上传记录
        uploads = []

        # 为每个上传图片计算统计信息
        for upload in all_uploads:
            # 获取生成图片数量
            generated_images = generated_model.get_by_upload_filename(upload['filename'])
            generated_count = len(generated_images)

            # 只有生成过图片的才加入画廊
            if generated_count > 0:
                upload['generated_count'] = generated_count

                # 找到最新生成时间
                latest_generation_time = None
                if generated_images:
                    # 按生成时间排序，获取最新的
                    sorted_images = sorted(generated_images, key=lambda x: x.get('generation_time', ''), reverse=True)
                    latest_generation_time = sorted_images[0].get('generation_time')

                upload['latest_generation_time'] = latest_generation_time

                # 计算点赞点踩数量
                like_count = 0
                dislike_count = 0
                for img in generated_images:
                    actions = user_action_model_instance.get_actions_by_generated_id(img['id'])
                    for action in actions:
                        if action['action_type'] == 'like':
                            like_count += 1
                        elif action['action_type'] == 'dislike':
                            dislike_count += 1

                upload['like_count'] = like_count
                upload['dislike_count'] = dislike_count
                upload['total_likes'] = like_count  # 兼容前端字段名
                upload['total_dislikes'] = dislike_count  # 兼容前端字段名

                # 添加到结果列表
                uploads.append(upload)

        # 按最新生成时间排序（最新的在前面）
        # 使用最新的生成时间作为排序依据
        uploads.sort(key=lambda x: x.get('latest_generation_time', x.get('upload_time', '')), reverse=True)

        # 计算总体统计
        total_uploads = len(uploads)
        total_generated = sum(upload['generated_count'] for upload in uploads)
        total_likes = sum(upload['like_count'] for upload in uploads)
        total_dislikes = sum(upload['dislike_count'] for upload in uploads)

        statistics = {
            'total_uploads': total_uploads,
            'total_generated': total_generated,
            'total_likes': total_likes,
            'total_dislikes': total_dislikes
        }

        return jsonify({
            'success': True,
            'uploads': uploads,
            'statistics': statistics
        })

    except Exception as e:
        print(f"获取画廊数据失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/details/<int:upload_id>')
def get_details(upload_id):
    """获取图片详情"""
    try:
        upload_model = UploadedImageModel(db)
        generated_model = GeneratedImageModel(db)
        user_action_model_instance = UserActionModel(db)

        # 获取原图信息
        original = upload_model.get_by_id(upload_id)
        if not original:
            return jsonify({'error': '图片不存在'}), 404

        # 获取生成的图片
        generated_images = generated_model.get_by_upload_id(upload_id)

        # 为每个生成图片添加用户操作和统计信息
        client_ip = request.remote_addr
        for img in generated_images:
            # 添加字段映射，确保前端可以正确访问文件名
            img['filename'] = img.get('generated_filename', '')
            img['created_at'] = img.get('generation_time', '')

            # 获取当前用户的操作
            user_like = user_action_model_instance.get_user_action(img['id'], client_ip, 'like')
            user_dislike = user_action_model_instance.get_user_action(img['id'], client_ip, 'dislike')

            if user_like:
                img['user_action'] = 'like'
            elif user_dislike:
                img['user_action'] = 'dislike'
            else:
                img['user_action'] = None

            # 计算总点赞点踩数
            actions = user_action_model_instance.get_actions_by_generated_id(img['id'])
            like_count = sum(1 for action in actions if action['action_type'] == 'like')
            dislike_count = sum(1 for action in actions if action['action_type'] == 'dislike')

            img['like_count'] = like_count
            img['dislike_count'] = dislike_count

        # 计算原图统计
        original['generated_count'] = len(generated_images)
        original['like_count'] = sum(img['like_count'] for img in generated_images)
        original['dislike_count'] = sum(img['dislike_count'] for img in generated_images)

        return jsonify({
            'success': True,
            'original': original,
            'generated': generated_images
        })

    except Exception as e:
        print(f"获取详情数据失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/upload/<path:filename>')
def get_upload_image(filename):
    """获取上传的图片"""
    # URL解码文件名，正确处理中文字符
    import urllib.parse
    import glob
    decoded_filename = urllib.parse.unquote(filename)

    # 首先尝试直接匹配
    filepath = os.path.join(UPLOAD_FOLDER, decoded_filename)
    if os.path.exists(filepath):
        return send_file(
            filepath,
            as_attachment=False,
            download_name=decoded_filename,
            mimetype='image/png'
        )

    # 如果直接匹配失败，尝试查找带MD5后缀的文件
    # 从数据库获取实际的文件名
    try:
        upload_model = UploadedImageModel(db)
        upload_record = upload_model.get_by_filename(decoded_filename)
        if upload_record and upload_record.get('safe_filename'):
            actual_filepath = os.path.join(UPLOAD_FOLDER, upload_record['safe_filename'])
            if os.path.exists(actual_filepath):
                return send_file(
                    actual_filepath,
                    as_attachment=False,
                    download_name=decoded_filename,
                    mimetype='image/png'
                )
    except Exception as e:
        print(f"查找上传文件失败: {e}")

    # 最后尝试通过文件名模式匹配
    name_without_ext = os.path.splitext(decoded_filename)[0]
    ext = os.path.splitext(decoded_filename)[1]
    pattern = os.path.join(UPLOAD_FOLDER, f"{name_without_ext}_*{ext}")
    matching_files = glob.glob(pattern)

    if matching_files:
        # 返回第一个匹配的文件
        return send_file(
            matching_files[0],
            as_attachment=False,
            download_name=decoded_filename,
            mimetype='image/png'
        )

    return jsonify({'error': '文件不存在'}), 404

@app.route('/api/image/<path:filename>')
def get_image(filename):
    """获取生成的图片"""
    # URL解码文件名，正确处理中文字符
    import urllib.parse
    decoded_filename = urllib.parse.unquote(filename)

    filepath = os.path.join(OUTPUT_FOLDER, decoded_filename)
    if os.path.exists(filepath):
        # 设置正确的文件名，支持中文
        return send_file(
            filepath,
            as_attachment=False,
            download_name=decoded_filename,
            mimetype='image/png'
        )
    return jsonify({'error': '文件不存在'}), 404

@app.route('/api/balance')
def get_balance():
    """获取账户余额"""
    try:
        cookie = get_cookie()
        if not cookie or not check_cookie():
            return jsonify({'error': 'Cookie无效'}), 400

        generator = ImageGen(cookie)
        balance = generator.get_account_point()
        return jsonify({'balance': balance})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/like/<int:generated_id>', methods=['POST'])
def like_image(generated_id):
    """点赞图片"""
    try:
        client_ip = get_client_ip()

        # 获取当前图片信息
        image_info = generated_image_model.get_by_id(generated_id)
        if not image_info:
            return jsonify({'error': '图片不存在'}), 404

        # 检查用户是否已经点过赞
        existing_like = user_action_model.get_user_action(generated_id, client_ip, 'like')
        existing_dislike = user_action_model.get_user_action(generated_id, client_ip, 'dislike')

        if existing_like:
            # 已经点过赞，取消点赞
            user_action_model.remove_action(generated_id, client_ip, 'like')
            print(f"用户 {client_ip} 取消点赞图片 {generated_id}")
            return jsonify({
                'success': True,
                'action': 'removed_like',
                'message': '取消点赞'
            })
        else:
            # 如果之前点过踩，先删除点踩记录
            if existing_dislike:
                user_action_model.remove_action(generated_id, client_ip, 'dislike')
                print(f"用户 {client_ip} 自动取消点踩图片 {generated_id}")

            # 记录点赞操作
            user_action_model.record_action(generated_id, 'like', client_ip)
            print(f"用户 {client_ip} 点赞图片 {generated_id}")

            return jsonify({
                'success': True,
                'action': 'liked',
                'message': '点赞成功'
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dislike/<int:generated_id>', methods=['POST'])
def dislike_image(generated_id):
    """点踩图片"""
    try:
        client_ip = get_client_ip()

        # 获取当前图片信息
        image_info = generated_image_model.get_by_id(generated_id)
        if not image_info:
            return jsonify({'error': '图片不存在'}), 404

        # 检查用户是否已经点过踩
        existing_like = user_action_model.get_user_action(generated_id, client_ip, 'like')
        existing_dislike = user_action_model.get_user_action(generated_id, client_ip, 'dislike')

        if existing_dislike:
            # 已经点过踩，取消点踩
            user_action_model.remove_action(generated_id, client_ip, 'dislike')
            print(f"用户 {client_ip} 取消点踩图片 {generated_id}")
            return jsonify({
                'success': True,
                'action': 'removed_dislike',
                'message': '取消点踩'
            })
        else:
            # 如果之前点过赞，先删除点赞记录
            if existing_like:
                user_action_model.remove_action(generated_id, client_ip, 'like')
                print(f"用户 {client_ip} 自动取消点赞图片 {generated_id}")

            # 记录点踩操作
            user_action_model.record_action(generated_id, 'dislike', client_ip)
            print(f"用户 {client_ip} 点踩图片 {generated_id}")

            return jsonify({
                'success': True,
                'action': 'disliked',
                'message': '点踩成功'
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/history')
def get_history():
    """获取生成历史"""
    try:
        limit = request.args.get('limit', 50, type=int)

        # 获取最近生成的图片
        recent_images = generated_image_model.get_recent_generated(limit)

        # 按上传图片分组
        history_groups = {}
        for image in recent_images:
            upload_id = image['upload_id']
            if upload_id not in history_groups:
                history_groups[upload_id] = {
                    'upload_id': upload_id,
                    'original_filename': image['original_filename'],
                    'file_md5': image['file_md5'],
                    'images': []
                }
            history_groups[upload_id]['images'].append(image)

        # 转换为列表格式
        history_list = list(history_groups.values())

        return jsonify({
            'success': True,
            'history': history_list
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# 视频生成相关API
from video_generation_service import video_service

@app.route('/api/create-video-task', methods=['POST'])
def create_video_task():
    """创建视频生成任务"""
    try:
        from datetime import datetime

        print(f"\n{'='*100}")
        print(f"🎬 [API] 收到视频生成请求")
        print(f"⏰ 请求时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        data = request.get_json()
        print(f"📋 [API] 请求数据: {data}")

        task_name = data.get('task_name', '过渡视频')
        upload_id = data.get('upload_id')
        selected_image_ids = data.get('selected_image_ids', [])

        print(f"📝 [API] 任务名称: {task_name}")
        print(f"📁 [API] 上传ID: {upload_id}")
        print(f"🖼️ [API] 选择的图片ID: {selected_image_ids}")
        print(f"📊 [API] 图片数量: {len(selected_image_ids)}")

        # 参数验证
        print(f"🔧 [API] 开始参数验证...")

        if not upload_id:
            error_msg = '缺少upload_id参数'
            print(f"❌ [API] 参数错误: {error_msg}")
            return jsonify({'error': error_msg}), 400

        if len(selected_image_ids) < 2:
            error_msg = '至少需要选择2张图片'
            print(f"❌ [API] 参数错误: {error_msg}")
            return jsonify({'error': error_msg}), 400

        print(f"✅ [API] 参数验证通过")
        print(f"🚀 [API] 先创建数据库任务记录...")

        # 1. 先创建数据库任务记录
        print(f"📝 [API] 创建视频任务记录...")
        task_id = video_task_model.create_task(
            task_name=task_name,
            upload_id=upload_id,
            image_ids=selected_image_ids,
            total_segments=len(selected_image_ids) - 1  # 相邻图片对数量
        )
        print(f"✅ [API] 任务创建成功，任务ID: {task_id}")

        # 2. 查找图片文件（需要先获取文件名）
        first_image_id = selected_image_ids[0]
        last_image_id = selected_image_ids[1]

        print(f"\n🎬 [API] 生成视频: 图片{first_image_id} -> 图片{last_image_id}")

        from video_generation_service import VideoGenerationService
        video_service_temp = VideoGenerationService()

        first_image_path = video_service_temp._find_actual_image_file_by_id(first_image_id)
        last_image_path = video_service_temp._find_actual_image_file_by_id(last_image_id)

        print(f"📁 [API] 首帧图片路径: {first_image_path}")
        print(f"📁 [API] 尾帧图片路径: {last_image_path}")

        if not first_image_path or not first_image_path.exists():
            raise Exception(f"首帧图片不存在: {first_image_path}")
        if not last_image_path or not last_image_path.exists():
            raise Exception(f"尾帧图片不存在: {last_image_path}")

        # 3. 创建视频片段记录（现在有了文件名）
        print(f"📝 [API] 创建视频片段记录...")
        segment_id = video_segment_model.create_segment(
            task_id=task_id,
            first_image_id=first_image_id,
            last_image_id=last_image_id,
            segment_order=1,
            first_image_filename=first_image_path.name,
            last_image_filename=last_image_path.name
        )
        print(f"✅ [API] 片段创建成功，片段ID: {segment_id}")

        # 4. 构建输出路径
        output_dir = Path("video_output") / "direct"
        output_dir.mkdir(parents=True, exist_ok=True)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_video_path = output_dir / f"video_{first_image_id}_to_{last_image_id}_{timestamp}.mp4"

        print(f"📁 [API] 输出视频路径: {output_video_path}")

        # 5. 调用增强视频生成脚本（带任务ID和片段ID）
        script_path = Path(__file__).parent / "增强视频生成.py"
        cmd = [
            "python",
            str(script_path),
            "--first-image", str(first_image_path),
            "--last-image", str(last_image_path),
            "--output", str(output_video_path),
            "--task-id", str(task_id),
            "--segment-id", str(segment_id)
        ]

        print(f"\n[API] 调用增强视频生成.py")
        print(f"📋 [API] 脚本路径: {script_path}")
        print(f"📋 [API] 传递的参数:")
        print(f"    --first-image: {first_image_path}")
        print(f"    --last-image: {last_image_path}")
        print(f"    --output: {output_video_path}")
        print(f"    --task-id: {task_id}")
        print(f"    --segment-id: {segment_id}")
        print(f"📋 [API] 完整命令: {' '.join(cmd)}")

        # 同步执行，直接显示结果
        success = False
        message = ""

        try:
            print(f"\n⏳ [API] 开始执行视频生成...")
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=900,  # 15分钟超时（包含视频生成、监控和下载时间）
                encoding='utf-8',
                errors='ignore'
            )

            print(f"✅ [API] 脚本执行完成，返回码: {result.returncode}")

            if result.stdout:
                print(f"📋 [API] 脚本输出:")
                print(result.stdout)

            if result.stderr:
                print(f"⚠️ [API] 脚本错误输出:")
                print(result.stderr)

            if result.returncode == 0:
                if output_video_path.exists():
                    file_size = output_video_path.stat().st_size
                    print(f"🎉 [API] 视频生成成功!")
                    print(f"📁 [API] 文件路径: {output_video_path}")
                    print(f"📊 [API] 文件大小: {file_size} 字节")

                    success = True
                    message = f"视频生成成功! 文件: {output_video_path.name}"
                else:
                    print(f"❌ [API] 脚本执行成功但视频文件不存在")
                    success = False
                    message = "脚本执行成功但视频文件未生成"
            else:
                print(f"❌ [API] 脚本执行失败，返回码: {result.returncode}")
                success = False
                error_details = ""
                if result.stderr:
                    error_details = f" 错误信息: {result.stderr[:200]}"  # 限制错误信息长度
                message = f"视频生成失败，返回码: {result.returncode}{error_details}"

                # 更新数据库状态为失败
                print(f"📝 [API] 更新数据库状态为失败...")
                video_task_model.update_task_status(task_id, 'failed', message)
                video_segment_model.update_segment_status(segment_id, 'failed')

        except subprocess.TimeoutExpired:
            print(f"⏰ [API] 脚本执行超时（15分钟）")
            success = False
            message = "视频生成超时（15分钟）"

            # 更新数据库状态为失败
            print(f"📝 [API] 更新数据库状态为超时失败...")
            video_task_model.update_task_status(task_id, 'failed', message)
            video_segment_model.update_segment_status(segment_id, 'failed')

        except Exception as e:
            print(f"❌ [API] 执行异常: {e}")
            success = False
            message = f"执行异常: {str(e)}"

            # 更新数据库状态为失败
            print(f"📝 [API] 更新数据库状态为异常失败...")
            if 'task_id' in locals():
                video_task_model.update_task_status(task_id, 'failed', message)
            if 'segment_id' in locals():
                video_segment_model.update_segment_status(segment_id, 'failed')

        print(f"📋 [API] 返回响应: success={success}")
        print(f"📋 [API] 消息: {message}")
        print(f"{'='*100}\n")

        return jsonify({
            'success': success,
            'message': message,
            'output_path': str(output_video_path) if success and 'output_video_path' in locals() else None
        })

    except Exception as e:
        import traceback
        error_msg = str(e)
        error_trace = traceback.format_exc()

        print(f"❌ [API] 创建视频任务失败: {error_msg}")
        print(f"📋 [API] 错误详情:\n{error_trace}")
        print(f"{'='*100}\n")

        return jsonify({
            'success': False,
            'error': error_msg,
            'details': error_trace if app.debug else None
        }), 500

@app.route('/api/video-task-status/<int:task_id>')
def get_video_task_status(task_id):
    """获取视频任务状态"""
    try:
        status = video_service.get_task_status(task_id)
        if not status:
            return jsonify({'error': '任务不存在'}), 404

        return jsonify({
            'success': True,
            'status': status
        })

    except Exception as e:
        print(f"获取视频任务状态失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/video-task-details/<int:task_id>')
def get_video_task_details(task_id):
    """获取视频任务详细信息，包括所有片段状态"""
    try:
        print(f"[API] 获取任务详细信息: {task_id}")

        # 导入数据库模型
        from database_models import video_task_model, video_segment_model

        # 获取任务信息
        task = video_task_model.get_task_by_id(task_id)
        if not task:
            return jsonify({'error': '任务不存在'}), 404

        # 获取所有片段信息
        segments = video_segment_model.get_segments_by_task(task_id)

        # 统计片段状态
        segment_stats = {
            'total': len(segments),
            'pending': 0,
            'processing': 0,
            'completed': 0,
            'failed': 0
        }

        for segment in segments:
            status = segment.get('status', 'pending')
            if status in segment_stats:
                segment_stats[status] += 1

        task_details = {
            'task': task,
            'segments': segments,
            'segment_stats': segment_stats,
            'progress': {
                'completed': segment_stats['completed'],
                'total': segment_stats['total'],
                'percentage': round((segment_stats['completed'] / segment_stats['total']) * 100, 1) if segment_stats['total'] > 0 else 0
            }
        }

        print(f"[API] 任务详情: 状态={task.get('status')}, 进度={task_details['progress']['percentage']}%")

        return jsonify({
            'success': True,
            'details': task_details
        })

    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        print(f"[API] 获取任务详细信息失败: {e}")
        print(f"[API] 错误详情:\n{error_trace}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/video-tasks/<int:upload_id>')
def get_video_tasks(upload_id):
    """获取指定上传ID的所有视频任务"""
    try:
        tasks = video_service.get_tasks_by_upload(upload_id)

        return jsonify({
            'success': True,
            'tasks': tasks
        })

    except Exception as e:
        print(f"获取视频任务失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/video-file/<path:filename>')
def serve_video_file(filename):
    """提供视频文件下载"""
    try:
        video_path = Path("video_output") / filename
        if video_path.exists():
            return send_file(video_path, as_attachment=True)
        else:
            return jsonify({'error': '视频文件不存在'}), 404

    except Exception as e:
        print(f"提供视频文件失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/version')
def get_version_api():
    """获取版本信息API"""
    try:
        return jsonify({
            'success': True,
            'version_info': get_version_info()
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    version_info = get_version_info()
    print("=" * 60)
    print(f"🎨 可灵AI图生图Web应用 v{version_info['version']}")
    print("=" * 60)
    print("📊 功能状态:")
    print("   ✅ 图片上传（保留中文文件名）")
    print("   ✅ 图片生成")
    print("   ✅ 结果展示")
    print("   ✅ 数据库支持（MD5去重）")
    print("   ✅ 历史记录")
    print("   ✅ 点赞点踩功能")
    print("   ✅ 多图片视频过渡生成")
    print("   ✅ 浏览器自动化视频生成")
    print()
    print(f"📝 版本: {version_info['version']}")
    print(f"👥 作者: {version_info['author']}")
    print(f"📖 描述: {version_info['description']}")
    print()

    # 初始化数据库
    if init_database():
        print("✅ 数据库初始化成功")
    else:
        print("⚠️ 数据库初始化失败，某些功能可能不可用")

    print("🌐 访问地址: http://localhost:5000")
    print()

    # 启动时清空网络请求日志文件
    network_log_file = Path(__file__).parent / "网络请求.txt"
    try:
        with open(network_log_file, 'w', encoding='utf-8') as f:
            f.write(f"=== 网络请求监控日志 ===\n")
            f.write(f"启动时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"=" * 50 + "\n\n")
        print(f"✅ 网络请求日志文件已清空: {network_log_file}")
    except Exception as e:
        print(f"⚠️ 清空网络请求日志文件失败: {e}")

    # 启动Chrome网络监控（静默启动，日志输出到文件）
    chrome_monitor.start_monitoring()

    try:
        app.run(debug=True, host='0.0.0.0', port=5000)
    finally:
        # 应用关闭时停止监控（静默停止，日志输出到文件）
        chrome_monitor.stop_monitoring()
