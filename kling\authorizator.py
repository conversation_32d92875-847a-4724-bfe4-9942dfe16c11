# 导入类型提示模块，用于函数返回值的类型标注
from typing import Optional
# 导入requests库，用于发送HTTP请求
import requests
# 导入json库，用于解析JSON格式的响应数据
import json


# 认证器类，负责处理可灵AI的用户登录认证
class Authorizator:

    # 属性装饰器，将方法转换为只读属性
    @property
    def cookies(self) -> str:
        # 将Session中的所有cookie转换为字符串格式
        # 格式为"name1=value1;name2=value2"，用于后续API调用的身份验证
        return ";".join(
            [f"{Cookie.name}={Cookie.value}" for Cookie in self.__Session.cookies]
        )

    # 初始化方法，创建认证器实例
    def __init__(self, sid: str = "ksi18n.ai.portal"):
        # 设置服务ID，默认为可灵AI门户的标识符
        self.__SID = sid
        # 创建requests会话对象，用于保持cookie和连接状态
        self.__Session = requests.Session()

    # 认证方法，使用邮箱和密码进行登录
    def auth(self, email: str, password: str) -> Optional[dict]:
        # 可灵AI的登录API端点URL
        AUTH_ENDPOINT = "https://id.klingai.com/pass/ksi18n/web/login/emailPassword"

        # 构建登录请求的载荷数据
        Payload = {
            # 服务ID，标识当前登录的服务
            "sid": self.__SID,
            # 用户邮箱地址
            "email": email,
            # 用户密码
            "password": password,
            # 界面语言设置为英文
            "language": "en",
            # Web签名版本标识，设置为False
            "isWebSig4": False,
        }
        # 设置请求头，指定内容类型为表单数据
        Headers = {"Content-Type": "application/x-www-form-urlencoded"}

        # 发送POST请求到登录端点，传递载荷数据和请求头
        Response = self.__Session.post(AUTH_ENDPOINT, data=Payload, headers=Headers)

        # 检查响应状态，如果请求失败则抛出异常
        if not Response.ok:
            # 打印错误响应内容
            print(Response.text)
            # 抛出认证失败异常，包含HTTP状态码
            raise Exception(f"Unable to authorizate. Code: {Response.status_code}.")

        # 尝试解析响应数据并设置认证cookie
        try:
            # 将响应文本解析为JSON格式的字典
            Data = json.loads(Response.text)
            # 从响应数据中提取认证token并设置到Session的cookie中
            # 这个cookie用于后续API调用的身份验证
            self.__Session.cookies.set(
                "ksi18n.ai.portal_st", Data["ksi18n.ai.portal_st"]
            )
            # 返回完整的响应数据
            return Data
        except:
            # 如果解析失败，静默处理（不抛出异常）
            pass
