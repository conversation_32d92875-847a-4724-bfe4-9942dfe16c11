#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强力尾帧注入 - 专门用于在首帧已上传后注入尾帧
"""

import asyncio
import os
import sys
import base64
import mimetypes
from pathlib import Path
from playwright.async_api import async_playwright


def read_image_as_base64(image_path: str):
    """读取图片并转换为Base64"""
    try:
        mime_type, _ = mimetypes.guess_type(image_path)
        if not mime_type or not mime_type.startswith('image/'):
            mime_type = 'image/png'
        
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        return {
            "name": os.path.basename(image_path),
            "data": base64_data,
            "type": mime_type,
            "size": len(image_data)
        }
    except Exception as e:
        print(f"❌ 读取图片失败: {e}")
        return None


async def force_inject_tail_frame():
    """强力注入尾帧"""
    
    # 尾帧图片路径
    last_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (3).png"
    
    print("💪 强力尾帧注入工具")
    print("=" * 50)
    print(f"📸 尾帧路径: {last_frame_path}")
    print(f"📸 尾帧名称: {Path(last_frame_path).name}")
    print()
    
    # 验证文件存在
    if not os.path.exists(last_frame_path):
        print(f"❌ 尾帧图片文件不存在: {last_frame_path}")
        return False
    
    print("✅ 尾帧图片文件验证通过")
    
    # 读取图片数据
    print("📤 读取尾帧数据...")
    last_frame_data = read_image_as_base64(last_frame_path)
    
    if not last_frame_data:
        return False
    
    print(f"✅ 尾帧数据读取成功: {last_frame_data['size']} 字节")
    
    # 检查Chrome调试端口
    debug_port = 9222
    print("🔍 检查Chrome调试端口...")
    
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("❌ Chrome调试端口未开启")
        return False
    
    print("✅ Chrome调试端口已开启")
    
    async with async_playwright() as p:
        try:
            print("🔗 连接到Chrome浏览器...")
            
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            contexts = browser.contexts
            
            if not contexts:
                print("❌ 未找到浏览器上下文")
                return False
            
            # 查找即梦AI页面
            jimeng_page = None
            for context in contexts:
                for page in context.pages:
                    if 'jimeng.jianying.com' in page.url:
                        jimeng_page = page
                        print(f"✅ 找到即梦AI页面: {page.url}")
                        break
                if jimeng_page:
                    break
            
            if not jimeng_page:
                print("❌ 未找到即梦AI页面")
                return False
            
            # 执行强力尾帧注入
            success = await perform_force_injection(jimeng_page, last_frame_data)
            
            if success:
                print("✅ 强力尾帧注入成功！")
                return True
            else:
                print("❌ 强力尾帧注入失败")
                return False
                
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False


async def perform_force_injection(page, last_frame_data):
    """执行强力注入"""
    try:
        print("💪 开始强力尾帧注入...")
        
        # 等待页面稳定
        await asyncio.sleep(2)
        
        # 步骤1: 注入强化版JavaScript函数
        print("🔧 步骤1: 注入强化版JavaScript函数...")
        
        await page.evaluate("""
            () => {
                console.log('💪 注入强化版JavaScript函数');
                
                // 强化版文件创建函数
                window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                    console.log('创建文件:', fileName, mimeType);
                    
                    try {
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        const file = new File([byteArray], fileName, { 
                            type: mimeType,
                            lastModified: Date.now()
                        });
                        console.log('文件创建成功:', file.name, file.size, 'bytes');
                        return file;
                    } catch (error) {
                        console.error('创建文件失败:', error);
                        return null;
                    }
                };
                
                // 强化版文件设置函数
                window.forceSetFileToInput = function(input, file) {
                    console.log('强制设置文件到input:', file.name);
                    
                    try {
                        // 方法1: 使用DataTransfer
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        input.files = dataTransfer.files;
                        
                        // 方法2: 直接设置files属性（备用）
                        Object.defineProperty(input, 'files', {
                            value: dataTransfer.files,
                            writable: false
                        });
                        
                        // 触发所有可能的事件
                        const events = [
                            'change', 'input', 'blur', 'focus',
                            'dragenter', 'dragover', 'drop'
                        ];
                        
                        events.forEach(eventType => {
                            try {
                                const event = new Event(eventType, { 
                                    bubbles: true, 
                                    cancelable: true 
                                });
                                input.dispatchEvent(event);
                                console.log(`触发了 ${eventType} 事件`);
                            } catch (e) {
                                console.log(`${eventType} 事件触发失败:`, e);
                            }
                        });
                        
                        // 特殊处理：模拟文件拖拽事件
                        try {
                            const dropEvent = new DragEvent('drop', {
                                bubbles: true,
                                cancelable: true,
                                dataTransfer: dataTransfer
                            });
                            input.dispatchEvent(dropEvent);
                            console.log('触发了拖拽事件');
                        } catch (e) {
                            console.log('拖拽事件触发失败:', e);
                        }
                        
                        console.log('文件强制设置成功，files.length:', input.files.length);
                        return true;
                    } catch (error) {
                        console.error('强制设置文件失败:', error);
                        return false;
                    }
                };
                
                console.log('✅ 强化版JavaScript函数注入完成');
            }
        """)
        
        await asyncio.sleep(1)
        print("✅ 强化版JavaScript函数注入完成")
        
        # 步骤2: 全面激活尾帧区域
        print("🔧 步骤2: 全面激活尾帧区域...")
        
        activation_result = await page.evaluate("""
            () => {
                console.log('🔧 全面激活尾帧区域');
                
                const result = {
                    clickedElements: [],
                    createdInputs: 0,
                    foundTailFrame: false
                };
                
                // 策略1: 查找并点击所有包含"尾帧"的元素
                const allElements = document.querySelectorAll('*');
                for (const el of allElements) {
                    const text = (el.textContent || '').trim();
                    if (text === '尾帧' || text === '参考帧' || text === '添加尾帧') {
                        if (el.offsetParent && el.tagName !== 'INPUT') {
                            try {
                                console.log('点击尾帧元素:', text, el.tagName, el.className);
                                el.click();
                                result.clickedElements.push(text);
                                result.foundTailFrame = true;
                            } catch (e) {
                                console.error('点击失败:', e);
                            }
                        }
                    }
                }
                
                // 策略2: 查找上传相关的父容器并点击
                const uploadContainers = document.querySelectorAll('[class*="upload"], [class*="reference"]');
                for (const container of uploadContainers) {
                    const children = container.querySelectorAll('*');
                    for (const child of children) {
                        if (child.offsetParent && child.tagName !== 'INPUT') {
                            try {
                                console.log('点击上传容器子元素:', child.className);
                                child.click();
                                result.clickedElements.push('upload container child');
                            } catch (e) {
                                console.error('点击上传容器失败:', e);
                            }
                        }
                    }
                }
                
                // 策略3: 尝试创建新的文件输入（如果页面支持）
                try {
                    const newInput = document.createElement('input');
                    newInput.type = 'file';
                    newInput.accept = 'image/*';
                    newInput.style.display = 'block';
                    newInput.style.position = 'absolute';
                    newInput.style.top = '0px';
                    newInput.style.left = '0px';
                    newInput.style.zIndex = '9999';
                    
                    // 尝试添加到页面
                    const uploadArea = document.querySelector('[class*="upload"]') || document.body;
                    uploadArea.appendChild(newInput);
                    
                    result.createdInputs = 1;
                    console.log('创建了新的文件输入');
                } catch (e) {
                    console.log('创建文件输入失败:', e);
                }
                
                console.log('激活结果:', result);
                return result;
            }
        """)
        
        await asyncio.sleep(3)  # 等待页面响应
        
        print(f"🔧 激活结果:")
        print(f"   点击了 {len(activation_result['clickedElements'])} 个元素")
        print(f"   找到尾帧元素: {activation_result['foundTailFrame']}")
        print(f"   创建新输入: {activation_result['createdInputs']} 个")
        
        # 步骤3: 查找所有可能的文件输入并尝试注入
        print("💉 步骤3: 查找所有文件输入并尝试注入...")
        
        injection_result = await page.evaluate(f"""
            () => {{
                console.log('💉 开始全面文件输入搜索和注入');
                
                const result = {{
                    totalInputs: 0,
                    visibleInputs: 0,
                    hiddenInputs: 0,
                    injectionAttempts: 0,
                    successfulInjections: 0,
                    errors: []
                }};
                
                try {{
                    // 查找所有文件输入（包括隐藏的）
                    const allInputs = document.querySelectorAll('input[type="file"]');
                    result.totalInputs = allInputs.length;
                    
                    console.log('找到文件输入总数:', result.totalInputs);
                    
                    // 分析每个文件输入
                    allInputs.forEach((input, index) => {{
                        const isVisible = input.offsetParent !== null;
                        const computedStyle = getComputedStyle(input);
                        const isDisplayed = computedStyle.display !== 'none';
                        const isVisibilityVisible = computedStyle.visibility !== 'hidden';
                        const hasOpacity = parseFloat(computedStyle.opacity) > 0;
                        
                        console.log(`文件输入 ${{index}}:`, {{
                            visible: isVisible,
                            display: computedStyle.display,
                            visibility: computedStyle.visibility,
                            opacity: computedStyle.opacity,
                            accept: input.accept,
                            className: input.className
                        }});
                        
                        if (isVisible || isDisplayed) {{
                            result.visibleInputs++;
                        }} else {{
                            result.hiddenInputs++;
                        }}
                        
                        // 尝试注入到每个文件输入
                        try {{
                            result.injectionAttempts++;
                            
                            // 强制显示元素
                            input.style.display = 'block !important';
                            input.style.visibility = 'visible !important';
                            input.style.opacity = '1 !important';
                            input.style.position = 'static !important';
                            input.style.width = 'auto !important';
                            input.style.height = 'auto !important';
                            input.style.zIndex = '9999 !important';
                            
                            // 创建文件
                            const file = window.createFileFromBase64(
                                '{last_frame_data["data"]}',
                                '{last_frame_data["name"]}',
                                '{last_frame_data["type"]}'
                            );
                            
                            if (file) {{
                                // 尝试注入
                                const success = window.forceSetFileToInput(input, file);
                                if (success) {{
                                    result.successfulInjections++;
                                    console.log(`✅ 成功注入到文件输入 ${{index}}`);
                                }} else {{
                                    console.log(`❌ 注入失败到文件输入 ${{index}}`);
                                }}
                            }} else {{
                                console.log(`❌ 文件创建失败 ${{index}}`);
                            }}
                            
                        }} catch (error) {{
                            console.error(`文件输入 ${{index}} 注入异常:`, error);
                            result.errors.push(`输入${{index}}: ${{error.message}}`);
                        }}
                    }});
                    
                }} catch (error) {{
                    console.error('全面注入过程出错:', error);
                    result.errors.push('全面注入出错: ' + error.message);
                }}
                
                console.log('注入结果:', result);
                return result;
            }}
        """)
        
        await asyncio.sleep(3)
        
        print(f"💉 注入结果:")
        print(f"   总文件输入: {injection_result['totalInputs']} 个")
        print(f"   可见输入: {injection_result['visibleInputs']} 个")
        print(f"   隐藏输入: {injection_result['hiddenInputs']} 个")
        print(f"   注入尝试: {injection_result['injectionAttempts']} 次")
        print(f"   成功注入: {injection_result['successfulInjections']} 次")
        
        if injection_result['errors']:
            print(f"   错误: {len(injection_result['errors'])} 个")
            for error in injection_result['errors'][:3]:
                print(f"     - {error}")
        
        # 步骤4: 最终验证
        print("🔍 步骤4: 最终验证...")
        
        final_state = await page.evaluate("""
            () => {
                const result = {
                    fileInputs: document.querySelectorAll('input[type="file"]').length,
                    blobImages: document.querySelectorAll('img[src*="blob:"]').length,
                    totalImages: document.querySelectorAll('img').length,
                    success: false
                };
                
                result.success = result.blobImages >= 2 || injection_result.successfulInjections > 0;
                
                console.log('最终验证结果:', result);
                return result;
            }
        """)
        
        print(f"📊 最终验证结果:")
        print(f"   文件输入: {final_state['fileInputs']} 个")
        print(f"   总图片: {final_state['totalImages']} 个")
        print(f"   预览图片: {final_state['blobImages']} 个")
        print(f"   强力注入成功: {'✅' if injection_result['successfulInjections'] > 0 else '❌'}")
        
        # 判断成功条件
        success = (
            injection_result['successfulInjections'] > 0 or 
            final_state['blobImages'] >= 2
        )
        
        return success
        
    except Exception as e:
        print(f"❌ 强力注入失败: {e}")
        return False


async def main():
    """主函数"""
    
    print("🚀 启动强力尾帧注入工具")
    print("💡 此工具专门用于在首帧已上传后强制注入尾帧")
    print()
    
    success = await force_inject_tail_frame()
    
    if success:
        print("\n🎉 强力尾帧注入成功！")
        print("💡 请在Chrome浏览器中查看即梦AI页面")
        print("🔍 检查是否有两张预览图片显示")
    else:
        print("\n❌ 强力尾帧注入失败")
        print("💡 可能需要手动操作激活尾帧功能")


if __name__ == "__main__":
    print("=" * 60)
    print("💪 强力尾帧注入工具")
    print("=" * 60)
    print("📝 使用场景：首帧已成功上传，需要补充注入尾帧")
    print("🎯 目标：强制激活尾帧区域并注入尾帧图片")
    print("=" * 60)
    print()
    
    asyncio.run(main())
