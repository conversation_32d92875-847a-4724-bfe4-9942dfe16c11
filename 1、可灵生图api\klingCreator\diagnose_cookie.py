#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie诊断脚本
帮助检查cookie是否有效
"""

import requests
import os
from kling.kling import BaseGen
from cookie_utils import get_cookie, check_cookie

def test_cookie(cookie_string):
    """测试cookie是否有效"""
    print("🔍 正在测试Cookie...")
    print(f"Cookie长度: {len(cookie_string)} 字符")
    
    # 检查cookie中是否包含关键字段
    required_fields = ['userId', 'kuaishou']
    missing_fields = []
    
    for field in required_fields:
        if field not in cookie_string:
            missing_fields.append(field)
    
    if missing_fields:
        print(f"⚠️  Cookie中缺少关键字段: {missing_fields}")
    else:
        print("✅ Cookie包含必要字段")
    
    # 尝试解析cookie
    try:
        cookiejar, is_cn = BaseGen.parse_cookie_string(cookie_string)
        print(f"🌍 检测到的版本: {'中国版' if is_cn else '国际版'}")
        print(f"📝 解析到的cookie数量: {len(dict(cookiejar))}")
        
        # 显示解析到的cookie
        print("\n📋 解析到的Cookie字段:")
        for name, value in dict(cookiejar).items():
            print(f"  {name}: {value[:20]}..." if len(value) > 20 else f"  {name}: {value}")
        
        return True, is_cn
        
    except Exception as e:
        print(f"❌ Cookie解析失败: {e}")
        return False, False

def main():
    print("🍪 可灵AI Cookie诊断工具")
    print("=" * 50)
    
    # 从cookie.txt文件读取cookie
    cookie = get_cookie()

    if not cookie:
        print("❌ 无法从cookie.txt读取cookie")
        cookie = input("请直接输入你的Cookie: ").strip()
    else:
        print("✅ 从cookie.txt文件读取到Cookie")
    
    if not cookie:
        print("❌ Cookie为空，无法继续")
        return
    
    # 测试cookie
    is_valid, is_cn = test_cookie(cookie)
    
    if is_valid:
        print("\n🚀 尝试连接可灵AI...")
        try:
            # 创建session测试连接
            session = requests.Session()
            cookiejar, is_cn = BaseGen.parse_cookie_string(cookie)
            session.cookies = cookiejar
            
            # 测试API连接
            base_url = "https://klingai.kuaishou.com/" if is_cn else "https://klingai.com/"
            test_url = f"{base_url}api/account/point"
            
            response = session.get(test_url)
            print(f"📡 API响应状态: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ API连接成功！")
                if 'data' in data and 'total' in data['data']:
                    points = data['data']['total'] / 100
                    print(f"💰 账户积分: {points}")
                else:
                    print("⚠️  无法获取积分信息")
            else:
                print(f"❌ API连接失败: {response.text}")
                
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
    
    print("\n📝 获取完整Cookie的步骤:")
    print("1. 访问 https://klingai.kuaishou.com/ (中国版) 或 https://klingai.com/ (国际版)")
    print("2. 登录你的账号")
    print("3. 按F12打开开发者工具")
    print("4. 切换到 Network (网络) 标签")
    print("5. 在网站上进行任何操作（如点击生成图片）")
    print("6. 在网络请求中找到任意一个请求")
    print("7. 在请求头中找到 'Cookie:' 字段")
    print("8. 复制完整的Cookie值（通常很长，包含多个字段）")

if __name__ == "__main__":
    main()
