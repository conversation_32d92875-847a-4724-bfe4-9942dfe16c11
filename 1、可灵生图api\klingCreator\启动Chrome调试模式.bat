@echo off
echo 🚀 启动Chrome调试模式...
echo ================================

REM 关闭现有Chrome进程
echo 📝 关闭现有Chrome进程...
taskkill /f /im chrome.exe >nul 2>&1

REM 等待进程完全关闭
timeout /t 2 >nul

REM 尝试不同的Chrome安装路径
echo 🔍 查找Chrome安装路径...

if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    echo ✅ 找到Chrome: C:\Program Files\Google\Chrome\Application\
    "C:\Program Files\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir=chrome-debug
) else if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    echo ✅ 找到Chrome: C:\Program Files (x86)\Google\Chrome\Application\
    "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" --remote-debugging-port=9222 --user-data-dir=chrome-debug
) else (
    echo ❌ 未找到Chrome安装路径
    echo 💡 请手动输入Chrome路径，或确保Chrome已正确安装
    echo.
    echo 常见路径：
    echo   C:\Program Files\Google\Chrome\Application\chrome.exe
    echo   C:\Program Files (x86)\Google\Chrome\Application\chrome.exe
    echo.
    pause
)

echo.
echo 🎉 Chrome调试模式已启动！
echo 📋 调试端口: 9222
echo 💡 现在可以运行Python脚本了
echo.
pause
