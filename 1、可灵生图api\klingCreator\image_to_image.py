#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI图生图工具
使用方法：直接运行此脚本，按提示输入参数或使用默认值
"""

import os
import json
import requests
import time
from kling import ImageGen
from cookie_utils import get_cookie, check_cookie

class ImageToImageGenerator(ImageGen):
    """图生图生成器，继承自ImageGen并使用修复版API"""
    
    def generate_image_to_image(self, prompt: str, image_path: str = None, 
                               count: int = 1, model_name: str = "2.0") -> list:
        """
        图生图生成方法
        
        Args:
            prompt: 文本提示词
            image_path: 参考图片路径
            count: 生成图片数量
            model_name: 模型版本
            
        Returns:
            生成的图片URL列表
        """
        
        # 设置请求头
        self.session.headers.update({
            "accept": "application/json, text/plain, */*",
            "accept-language": "zh",
            "content-type": "application/json",
            "time-zone": "Asia/Shanghai",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        })
        
        # 检查参考图片
        if not image_path or not os.path.exists(image_path):
            raise ValueError(f"参考图片不存在: {image_path}")
        
        print("📤 正在上传参考图片...")
        image_payload_url = self.image_uploader(image_path)
        print(f"✅ 图片上传成功")
        
        # 构建请求载荷
        payload = {
            "type": "mmu_img2img_aiweb",
            "inputs": [
                {
                    "inputType": "URL",
                    "url": image_payload_url,
                    "name": "input"
                }
            ],
            "arguments": [
                {
                    "name": "biz",
                    "value": "klingai"
                },
                {
                    "name": "prompt",
                    "value": prompt
                },
                {
                    "name": "imageCount",
                    "value": str(count)
                },
                {
                    "name": "kolors_version",
                    "value": model_name
                },
                {
                    "name": "style",
                    "value": "默认"
                },
                {
                    "name": "referenceType",
                    "value": "mmu_img2img_aiweb_v20_stylize"
                }
            ]
        }
        
        print("🚀 正在提交图生图请求...")
        print(f"📝 提示词: {prompt[:50]}...")

        # 发送请求并重试获取请求ID
        request_id = None
        max_retries = 50  # 最多重试50次
        retry_count = 0

        while not request_id and retry_count < max_retries:
            try:
                if retry_count > 0:
                    print(f"🔄 重试获取请求ID ({retry_count + 1}/{max_retries})...")

                response = self.session.post(
                    self.submit_url,
                    json=payload,
                    headers=self.session.headers
                )

                if not response.ok:
                    raise Exception(f"请求失败: {response.status_code} - {response.text}")

                response_body = response.json()

                # 检查响应
                data = response_body.get("data")
                if data and data.get("status") == 7:
                    message = data.get("message")
                    raise Exception(f"请求失败: {message}")

                request_id = data.get("task", {}).get("id") if data else None

                if not request_id:
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"⚠️  无法获取请求ID，11秒后重试 ({retry_count}/{max_retries})")
                        # 倒计时显示
                        for countdown in range(11, 0, -1):
                            print(f"⏳ 重试倒计时: {countdown} 秒", end='\r')
                            time.sleep(1)
                        print()  # 换行
                    else:
                        raise Exception("多次尝试后仍无法获取请求ID")
                else:
                    print(f"✅ 成功获取请求ID: {request_id}")
                    break

            except Exception as e:
                retry_count += 1
                if retry_count < max_retries:
                    print(f"❌ 请求异常，11秒后重试: {str(e)}")
                    # 倒计时显示
                    for countdown in range(11, 0, -1):
                        print(f"⏳ 重试倒计时: {countdown} 秒", end='\r')
                        time.sleep(1)
                    print()  # 换行
                else:
                    raise Exception(f"多次重试后仍然失败: {str(e)}")

        if not request_id:
            raise Exception("无法获取请求ID")
        
        print(f"✅ 获取到请求ID: {request_id}")
        print("⏳ 等待生成结果...")
        
        # 等待结果
        start_wait = time.time()
        while True:
            if int(time.time() - start_wait) > 600:  # 10分钟超时
                raise Exception("请求超时")
            
            image_data, status = self.fetch_metadata(request_id)
            
            if status.name == "PENDING":
                print(".", end="", flush=True)
                time.sleep(5)
            elif status.name == "FAILED":
                print("\n❌ 生成失败")
                return []
            else:
                print("\n✅ 生成完成！")
                result = []
                works = image_data.get("works", [])
                if not works:
                    print("⚠️  未找到生成的图片")
                    return []
                else:
                    for work in works:
                        resource = work.get("resource", {}).get("resource")
                        if resource:
                            result.append(resource)
                            print(f"🎨 生成图片: {resource}")
                return result

def main():
    print("🎨 可灵AI图生图工具")
    print("=" * 60)
    
    # 默认参数
    default_image_path = r"D:\code\批量墙体秀项目\参考图\华创瑞景园-1952x1056.jpg"
    default_prompt = "一座完全由生物发光植物构成的奇幻建筑，孤立在纯黑色背景中。巨大的、发光的奇异花朵和流光溢彩的藤蔓构成了建筑的主体结构，半透明的叶片下透出柔和的内部光芒。空气中漂浮着由它散发出的点点荧光。主色调为梦幻般的翠绿色、宝蓝色和淡紫色，充满生命力。幻想概念艺术，强烈的发光效果，细节丰富，固定视角。"
    
    # 从cookie.txt文件读取cookie
    cookie = get_cookie()

    if not cookie:
        print("❌ 无法读取cookie")
        print("请确保cookie.txt文件存在且包含有效的cookie字符串")
        return

    if not check_cookie():
        print("❌ Cookie格式不正确")
        return
    
    # 获取用户输入
    print("📋 请输入参数（直接回车使用默认值）:")
    print()
    
    # 参考图片路径
    print(f"📁 默认参考图片: {default_image_path}")
    image_path = input("请输入参考图片路径 (回车使用默认): ").strip()
    if not image_path:
        image_path = default_image_path
    
    # 提示词
    print(f"\n📝 默认提示词: {default_prompt[:50]}...")
    prompt = input("请输入提示词 (回车使用默认): ").strip()
    if not prompt:
        prompt = default_prompt
    
    # 生成数量
    count_input = input("请输入生成数量 (默认1张): ").strip()
    try:
        count = int(count_input) if count_input else 1
        if count < 1 or count > 9:
            print("⚠️  数量超出范围，使用默认值1")
            count = 1
    except ValueError:
        print("⚠️  输入无效，使用默认值1")
        count = 1
    
    # 输出目录
    output_dir = input("请输入输出目录 (默认./image_to_image_output): ").strip()
    if not output_dir:
        output_dir = "./image_to_image_output"
    
    print("\n" + "=" * 60)
    print(f"📁 参考图片: {image_path}")
    print(f"📝 提示词: {prompt[:50]}...")
    print(f"🔢 生成数量: {count}张")
    print(f"📂 输出目录: {output_dir}")
    print("=" * 60)
    
    # 检查参考图片
    if not os.path.exists(image_path):
        print(f"❌ 参考图片不存在: {image_path}")
        return
    
    try:
        print("\n🚀 正在初始化图生图生成器...")
        generator = ImageToImageGenerator(cookie)
        
        # 检查账户余额
        balance = generator.get_account_point()
        print(f"💰 当前账户余额: {balance} 积分")
        
        if balance < count * 2:  # 图生图大约消耗2积分/张
            print(f"❌ 账户积分不足，需要约{count * 2}积分")
            return
        
        print("\n🎨 开始图生图生成...")
        
        # 生成图片
        result = generator.generate_image_to_image(
            prompt=prompt,
            image_path=image_path,
            count=count,
            model_name="2.0"
        )
        
        if result:
            print(f"\n✅ 成功生成 {len(result)} 张图片！")
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 下载生成的图片
            for i, url in enumerate(result):
                print(f"📥 正在下载第 {i+1} 张图片...")
                response = requests.get(url)
                if response.status_code == 200:
                    filename = f"image_to_image_{i}.png"
                    filepath = os.path.join(output_dir, filename)
                    with open(filepath, 'wb') as f:
                        f.write(response.content)
                    print(f"✅ 保存成功: {filename}")
                else:
                    print(f"❌ 下载失败: {url}")
            
            print(f"\n📂 所有图片已保存到: {os.path.abspath(output_dir)}")
        else:
            print("⚠️  生成结果为空")
        
        # 显示更新后的余额
        new_balance = generator.get_account_point()
        if new_balance != balance:
            used_points = balance - new_balance
            print(f"💰 消耗积分: {used_points}")
            print(f"💰 剩余积分: {new_balance}")
        
    except Exception as e:
        print(f"❌ 图生图失败: {e}")
        print("\n可能的原因:")
        print("1. Cookie已过期，请重新获取")
        print("2. 账户积分不足")
        print("3. 参考图片格式不支持")
        print("4. 网络连接问题")
        print("5. 提示词包含敏感内容")

if __name__ == "__main__":
    main()
