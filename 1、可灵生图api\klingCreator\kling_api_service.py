#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI独立API服务
提供图生图和积分查询功能的REST API接口
"""

import os
import time
import uuid
import base64
import re
import requests
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename
from kling import ImageGen
from cookie_utils import get_cookie, check_cookie
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'kling-ai-api-service'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB

# 配置
UPLOAD_FOLDER = 'api_uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

class KlingAIService:
    """可灵AI服务类"""
    
    def __init__(self):
        self.cookie = None
        self.generator = None
        self._init_service()
    
    def _init_service(self):
        """初始化服务"""
        try:
            self.cookie = get_cookie()
            if not self.cookie or not check_cookie():
                logger.error("Cookie无效或不存在")
                return False
            
            self.generator = ImageGen(self.cookie)
            logger.info("可灵AI服务初始化成功")
            return True
        except Exception as e:
            logger.error(f"可灵AI服务初始化失败: {e}")
            return False
    
    def check_service(self):
        """检查服务状态"""
        if not self.generator:
            return False, "服务未初始化"
        
        try:
            # 尝试获取积分来验证服务状态
            balance = self.generator.get_account_point()
            return True, f"服务正常，当前积分: {balance}"
        except Exception as e:
            logger.error(f"服务检查失败: {e}")
            return False, f"服务异常: {str(e)}"
    
    def get_balance(self):
        """获取账户积分"""
        if not self.generator:
            raise Exception("服务未初始化")
        
        try:
            balance = self.generator.get_account_point()
            logger.info(f"获取积分成功: {balance}")
            return balance
        except Exception as e:
            logger.error(f"获取积分失败: {e}")
            raise Exception(f"获取积分失败: {str(e)}")
    
    def generate_image(self, image_path, prompt, style="默认"):
        """生成图片"""
        if not self.generator:
            raise Exception("服务未初始化")
        
        try:
            logger.info(f"开始生成图片: {prompt}")
            
            # 设置请求头
            self.generator.session.headers.update({
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh",
                "content-type": "application/json",
                "time-zone": "Asia/Shanghai",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            })
            
            # 上传图片
            image_payload_url = self.generator.image_uploader(image_path)
            logger.info(f"图片上传成功: {image_payload_url}")
            
            # 构建请求载荷
            payload = {
                "type": "mmu_img2img_aiweb",
                "inputs": [
                    {
                        "inputType": "URL",
                        "url": image_payload_url,
                        "name": "input"
                    }
                ],
                "arguments": [
                    {
                        "name": "biz",
                        "value": "klingai"
                    },
                    {
                        "name": "prompt",
                        "value": prompt
                    },
                    {
                        "name": "imageCount",
                        "value": "1"
                    },
                    {
                        "name": "kolors_version",
                        "value": "2.0"
                    },
                    {
                        "name": "style",
                        "value": style
                    },
                    {
                        "name": "referenceType",
                        "value": "mmu_img2img_aiweb_v20_stylize"
                    }
                ]
            }
            
            # 发送请求
            response = self.generator.session.post(
                self.generator.submit_url,
                json=payload,
                headers=self.generator.session.headers
            )
            
            if not response.ok:
                raise Exception(f"请求失败: {response.status_code} - {response.text}")
            
            response_body = response.json()
            data = response_body.get("data")
            
            if data and data.get("status") == 7:
                message = data.get("message")
                raise Exception(f"请求失败: {message}")
            
            request_id = data.get("task", {}).get("id") if data else None
            
            if not request_id:
                raise Exception("无法获取请求ID")
            
            logger.info(f"获取到请求ID: {request_id}")
            
            # 等待结果
            start_wait = time.time()
            while True:
                if int(time.time() - start_wait) > 600:  # 10分钟超时
                    raise Exception("请求超时")
                
                image_data, status = self.generator.fetch_metadata(request_id)
                
                if status.name == "PENDING":
                    logger.info("生成中...")
                    time.sleep(3)
                elif status.name == "FAILED":
                    raise Exception("生成失败")
                else:
                    works = image_data.get("works", [])
                    if not works:
                        raise Exception("未找到生成的图片")
                    
                    resource = works[0].get("resource", {}).get("resource")
                    if resource:
                        logger.info(f"图片生成成功: {resource}")
                        return resource
                    else:
                        raise Exception("无法获取图片URL")
                        
        except Exception as e:
            logger.error(f"生成图片失败: {e}")
            raise Exception(f"生成图片失败: {str(e)}")

# 全局服务实例
kling_service = KlingAIService()

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def sanitize_filename_keep_chinese(filename):
    """清理文件名，保留中文字符"""
    # 移除不安全的字符，但保留中文、英文、数字、点、横线、下划线、空格
    safe_chars = re.sub(r'[<>:"/\\|?*]', '', filename)
    # 移除多余的空格，但保留单个空格
    safe_chars = re.sub(r'\s+', ' ', safe_chars).strip()
    # 如果文件名为空或只有扩展名，使用默认名称
    if not safe_chars or safe_chars.startswith('.'):
        safe_chars = f"image_{uuid.uuid4().hex[:8]}{safe_chars}"
    return safe_chars

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    try:
        is_healthy, message = kling_service.check_service()
        return jsonify({
            'status': 'healthy' if is_healthy else 'unhealthy',
            'message': message,
            'timestamp': time.time()
        }), 200 if is_healthy else 503
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/api/balance', methods=['GET'])
def get_balance():
    """获取账户积分"""
    try:
        balance = kling_service.get_balance()
        return jsonify({
            'success': True,
            'balance': balance,
            'timestamp': time.time()
        })
    except Exception as e:
        logger.error(f"获取积分API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/api/generate', methods=['POST'])
def generate_image():
    """图生图API"""
    try:
        # 检查请求类型
        if request.content_type and 'multipart/form-data' in request.content_type:
            # 文件上传方式
            if 'image' not in request.files:
                return jsonify({
                    'success': False,
                    'error': '没有上传图片文件'
                }), 400
            
            file = request.files['image']
            prompt = request.form.get('prompt', '')
            style = request.form.get('style', '默认')
            
            if file.filename == '':
                return jsonify({
                    'success': False,
                    'error': '没有选择文件'
                }), 400
            
            if not allowed_file(file.filename):
                return jsonify({
                    'success': False,
                    'error': '不支持的文件格式'
                }), 400
            
            # 保存文件，保留中文文件名
            original_filename = file.filename
            name, ext = os.path.splitext(original_filename)
            safe_name = sanitize_filename_keep_chinese(name)
            unique_filename = f"{uuid.uuid4().hex}_{safe_name}{ext}"
            filepath = os.path.join(UPLOAD_FOLDER, unique_filename)
            file.save(filepath)
            
        else:
            # JSON方式
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'error': '请求数据格式错误'
                }), 400
            
            image_base64 = data.get('image_base64')
            image_url = data.get('image_url')
            prompt = data.get('prompt', '')
            style = data.get('style', '默认')
            
            if not prompt:
                return jsonify({
                    'success': False,
                    'error': '缺少提示词参数'
                }), 400
            
            if image_base64:
                # Base64图片
                try:
                    image_data = base64.b64decode(image_base64)
                    unique_filename = f"{uuid.uuid4().hex}.jpg"
                    filepath = os.path.join(UPLOAD_FOLDER, unique_filename)
                    with open(filepath, 'wb') as f:
                        f.write(image_data)
                except Exception as e:
                    return jsonify({
                        'success': False,
                        'error': f'Base64图片解码失败: {str(e)}'
                    }), 400
            
            elif image_url:
                # 图片URL
                try:
                    response = requests.get(image_url)
                    if response.status_code == 200:
                        unique_filename = f"{uuid.uuid4().hex}.jpg"
                        filepath = os.path.join(UPLOAD_FOLDER, unique_filename)
                        with open(filepath, 'wb') as f:
                            f.write(response.content)
                    else:
                        return jsonify({
                            'success': False,
                            'error': f'无法下载图片: {response.status_code}'
                        }), 400
                except Exception as e:
                    return jsonify({
                        'success': False,
                        'error': f'下载图片失败: {str(e)}'
                    }), 400
            else:
                return jsonify({
                    'success': False,
                    'error': '缺少图片参数（image_base64或image_url）'
                }), 400
        
        if not prompt:
            return jsonify({
                'success': False,
                'error': '缺少提示词参数'
            }), 400
        
        # 生成图片
        try:
            result_url = kling_service.generate_image(filepath, prompt, style)
            
            # 清理临时文件
            try:
                os.remove(filepath)
            except:
                pass
            
            return jsonify({
                'success': True,
                'image_url': result_url,
                'prompt': prompt,
                'style': style,
                'timestamp': time.time()
            })
            
        except Exception as e:
            # 清理临时文件
            try:
                os.remove(filepath)
            except:
                pass
            raise e
            
    except Exception as e:
        logger.error(f"生成图片API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': time.time()
        }), 500

@app.route('/api/info', methods=['GET'])
def get_api_info():
    """获取API信息"""
    return jsonify({
        'name': '可灵AI API服务',
        'version': '1.0.0',
        'description': '提供可灵AI图生图和积分查询功能',
        'endpoints': {
            'GET /health': '健康检查',
            'GET /api/balance': '获取账户积分',
            'POST /api/generate': '图生图（支持文件上传和JSON格式）',
            'GET /api/info': '获取API信息'
        },
        'timestamp': time.time()
    })

if __name__ == '__main__':
    print("🚀 启动可灵AI独立API服务")
    print("📊 功能:")
    print("   ✅ 图生图API")
    print("   ✅ 积分查询API") 
    print("   ✅ 健康检查API")
    print("   ✅ 支持文件上传和Base64/URL方式")
    print()
    print("🌐 API文档:")
    print("   健康检查: GET /health")
    print("   积分查询: GET /api/balance")
    print("   图生图: POST /api/generate")
    print("   API信息: GET /api/info")
    print()
    
    # 检查服务状态
    is_healthy, message = kling_service.check_service()
    if is_healthy:
        print(f"✅ {message}")
    else:
        print(f"❌ {message}")
    print()
    
    app.run(debug=True, host='0.0.0.0', port=8080)
