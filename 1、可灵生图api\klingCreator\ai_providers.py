#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI提供商统一接口
"""

import os
import time
import requests
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from kling import ImageGen
from cookie_utils import get_cookie, check_cookie

class AIProvider(ABC):
    """AI提供商抽象基类"""
    
    @abstractmethod
    def generate_image(self, image_path: str, prompt: str, style_name: str = "默认") -> List[str]:
        """生成图片，返回图片URL列表"""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """获取提供商名称"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查提供商是否可用"""
        pass

class KlingProvider(AIProvider):
    """可灵AI提供商"""
    
    def __init__(self):
        self.cookie = get_cookie()
        self.generator = None
        if self.cookie and check_cookie():
            try:
                self.generator = ImageGen(self.cookie)
            except Exception as e:
                print(f"可灵AI初始化失败: {e}")
    
    def generate_image(self, image_path: str, prompt: str, style_name: str = "默认") -> List[str]:
        """使用可灵AI生成图片"""
        if not self.generator:
            raise Exception("可灵AI未初始化")
        
        try:
            # 设置请求头
            self.generator.session.headers.update({
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh",
                "content-type": "application/json",
                "time-zone": "Asia/Shanghai",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            })
            
            # 上传图片
            image_payload_url = self.generator.image_uploader(image_path)
            
            # 构建请求载荷
            payload = {
                "type": "mmu_img2img_aiweb",
                "inputs": [
                    {
                        "inputType": "URL",
                        "url": image_payload_url,
                        "name": "input"
                    }
                ],
                "arguments": [
                    {
                        "name": "biz",
                        "value": "klingai"
                    },
                    {
                        "name": "prompt",
                        "value": prompt
                    },
                    {
                        "name": "imageCount",
                        "value": "1"
                    },
                    {
                        "name": "kolors_version",
                        "value": "2.0"
                    },
                    {
                        "name": "style",
                        "value": "默认"
                    },
                    {
                        "name": "referenceType",
                        "value": "mmu_img2img_aiweb_v20_stylize"
                    }
                ]
            }
            
            # 发送请求
            response = self.generator.session.post(
                self.generator.submit_url,
                json=payload,
                headers=self.generator.session.headers
            )
            
            if not response.ok:
                raise Exception(f"请求失败: {response.status_code} - {response.text}")
            
            response_body = response.json()
            data = response_body.get("data")
            
            if data and data.get("status") == 7:
                message = data.get("message")
                raise Exception(f"请求失败: {message}")
            
            request_id = data.get("task", {}).get("id") if data else None
            
            if not request_id:
                raise Exception("无法获取请求ID")
            
            # 等待结果
            start_wait = time.time()
            while True:
                if int(time.time() - start_wait) > 600:  # 10分钟超时
                    raise Exception("请求超时")
                
                image_data, status = self.generator.fetch_metadata(request_id)
                
                if status.name == "PENDING":
                    time.sleep(3)
                elif status.name == "FAILED":
                    raise Exception("生成失败")
                else:
                    works = image_data.get("works", [])
                    if not works:
                        raise Exception("未找到生成的图片")
                    
                    result = []
                    for work in works:
                        resource = work.get("resource", {}).get("resource")
                        if resource:
                            result.append(resource)
                    return result
                    
        except Exception as e:
            raise Exception(f"可灵AI生成失败: {e}")
    
    def get_provider_name(self) -> str:
        return "kling"
    
    def is_available(self) -> bool:
        return self.generator is not None

class JimengProvider(AIProvider):
    """即梦AI提供商（待实现）"""
    
    def __init__(self):
        # TODO: 初始化即梦AI
        pass
    
    def generate_image(self, image_path: str, prompt: str, style_name: str = "默认") -> List[str]:
        """使用即梦AI生成图片"""
        # TODO: 实现即梦AI图生图
        raise NotImplementedError("即梦AI接口尚未实现")
    
    def get_provider_name(self) -> str:
        return "jimeng"
    
    def is_available(self) -> bool:
        # TODO: 检查即梦AI是否可用
        return False

class AIProviderManager:
    """AI提供商管理器"""
    
    def __init__(self):
        self.providers = {
            "kling": KlingProvider(),
            "jimeng": JimengProvider()
        }
    
    def get_provider(self, provider_name: str) -> Optional[AIProvider]:
        """获取指定的AI提供商"""
        return self.providers.get(provider_name)
    
    def get_available_providers(self) -> List[str]:
        """获取可用的AI提供商列表"""
        return [name for name, provider in self.providers.items() if provider.is_available()]
    
    def generate_image(self, provider_name: str, image_path: str, prompt: str, style_name: str = "默认") -> List[str]:
        """使用指定提供商生成图片"""
        provider = self.get_provider(provider_name)
        if not provider:
            raise Exception(f"未找到AI提供商: {provider_name}")
        
        if not provider.is_available():
            raise Exception(f"AI提供商不可用: {provider_name}")
        
        return provider.generate_image(image_path, prompt, style_name)

# 全局AI提供商管理器实例
ai_manager = AIProviderManager()
