<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员审核 - 可灵AI图生图</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
        }
        
        .admin-controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            align-items: center;
        }
        
        .admin-stats {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4a5568;
        }
        
        .stat-label {
            color: #718096;
            margin-top: 5px;
        }
        
        .pending-item {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .pending-content {
            display: grid;
            grid-template-columns: 200px 200px 1fr auto;
            gap: 20px;
            padding: 20px;
            align-items: center;
        }
        
        .original-image, .generated-image {
            text-align: center;
        }
        
        .original-image img, .generated-image img {
            width: 180px;
            height: 180px;
            object-fit: cover;
            border-radius: 8px;
            cursor: pointer;
        }
        
        .image-label {
            margin-top: 8px;
            font-size: 0.9rem;
            color: #718096;
        }
        
        .item-info {
            flex: 1;
        }
        
        .item-title {
            font-weight: 600;
            color: #4a5568;
            margin-bottom: 10px;
        }
        
        .item-prompt {
            color: #718096;
            line-height: 1.5;
            margin-bottom: 10px;
            max-height: 100px;
            overflow-y: auto;
        }
        
        .item-meta {
            font-size: 0.8rem;
            color: #a0aec0;
        }
        
        .admin-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .btn-approve {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-reject {
            background: linear-gradient(135deg, #e53e3e, #c53030);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-approve:hover, .btn-reject:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #a0aec0;
        }
        
        .empty-state h3 {
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部 -->
        <header class="header admin-header">
            <h1>🛡️ 管理员审核系统</h1>
            <div class="balance-info">
                <a href="/" style="color: white; text-decoration: none;">🏠 返回主页</a>
            </div>
        </header>

        <!-- 统计信息 -->
        <div class="admin-stats">
            <div class="stat-item">
                <div class="stat-number" id="pendingCount">-</div>
                <div class="stat-label">待审核</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="approvedCount">-</div>
                <div class="stat-label">已通过</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="rejectedCount">-</div>
                <div class="stat-label">已拒绝</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="totalCount">-</div>
                <div class="stat-label">总计</div>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="admin-controls">
            <button class="btn-refresh" id="refreshPending">🔄 刷新列表</button>
            <button class="btn-clear" id="batchApprove">✅ 批量通过</button>
            <button class="btn-clear" id="batchReject">❌ 批量拒绝</button>
        </div>

        <!-- 待审核列表 -->
        <section class="results-section">
            <h2><span class="step-number">📋</span> 待审核图片</h2>
            <div id="pendingList"></div>
        </section>
    </div>

    <!-- 图片查看模态框 -->
    <div class="image-modal" id="imageModal" style="display: none;">
        <div class="image-modal-content">
            <span class="image-modal-close" id="modalClose">&times;</span>
            <img id="modalImage" src="" alt="大图预览">
            <div class="image-modal-info">
                <h3 id="modalTitle"></h3>
            </div>
        </div>
    </div>

    <script>
        // DOM元素
        const pendingList = document.getElementById('pendingList');
        const refreshBtn = document.getElementById('refreshPending');
        const batchApproveBtn = document.getElementById('batchApprove');
        const batchRejectBtn = document.getElementById('batchReject');
        const imageModal = document.getElementById('imageModal');
        const modalClose = document.getElementById('modalClose');
        const modalImage = document.getElementById('modalImage');
        const modalTitle = document.getElementById('modalTitle');

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPendingImages();
            
            refreshBtn.addEventListener('click', loadPendingImages);
            modalClose.addEventListener('click', closeModal);
            imageModal.addEventListener('click', (e) => {
                if (e.target === imageModal) closeModal();
            });
        });

        // 加载待审核图片
        async function loadPendingImages() {
            try {
                const response = await fetch('/api/admin/pending');
                const data = await response.json();
                
                if (data.images) {
                    displayPendingImages(data.images);
                    updateStats(data.images);
                } else {
                    console.error('加载失败:', data.error);
                }
            } catch (error) {
                console.error('加载异常:', error);
            }
        }

        // 显示待审核图片
        function displayPendingImages(images) {
            pendingList.innerHTML = '';
            
            if (images.length === 0) {
                pendingList.innerHTML = `
                    <div class="empty-state">
                        <h3>🎉 太棒了！</h3>
                        <p>暂无待审核的图片</p>
                    </div>
                `;
                return;
            }
            
            images.forEach(image => {
                const item = createPendingItem(image);
                pendingList.appendChild(item);
            });
        }

        // 创建待审核项
        function createPendingItem(image) {
            const item = document.createElement('div');
            item.className = 'pending-item';
            item.dataset.imageId = image.id;
            
            item.innerHTML = `
                <div class="pending-content">
                    <div class="original-image">
                        <img src="/uploads/${image.original_image_name}" alt="原始图片" 
                             onclick="openModal('/uploads/${image.original_image_name}', '原始图片')">
                        <div class="image-label">原始图片</div>
                    </div>
                    <div class="generated-image">
                        <img src="/api/image/${image.image_name}" alt="生成图片"
                             onclick="openModal('/api/image/${image.image_name}', '生成图片')">
                        <div class="image-label">生成图片</div>
                    </div>
                    <div class="item-info">
                        <div class="item-title">${image.style_name}</div>
                        <div class="item-prompt">${image.prompt}</div>
                        <div class="item-meta">
                            生成时间: ${new Date(image.created_at).toLocaleString()}<br>
                            点赞: ${image.likes || 0} | 点踩: ${image.dislikes || 0}
                        </div>
                    </div>
                    <div class="admin-actions">
                        <button class="btn-approve" onclick="approveImage(${image.id})">
                            ✅ 通过
                        </button>
                        <button class="btn-reject" onclick="rejectImage(${image.id})">
                            ❌ 拒绝
                        </button>
                    </div>
                </div>
            `;
            
            return item;
        }

        // 审核通过
        async function approveImage(imageId) {
            try {
                const response = await fetch(`/api/admin/approve/${imageId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        approved_by: 'admin'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 移除该项
                    const item = document.querySelector(`[data-image-id="${imageId}"]`);
                    if (item) {
                        item.remove();
                    }
                    
                    // 刷新列表
                    loadPendingImages();
                } else {
                    alert('审核失败: ' + data.error);
                }
            } catch (error) {
                console.error('审核异常:', error);
                alert('审核失败: ' + error.message);
            }
        }

        // 审核拒绝
        async function rejectImage(imageId) {
            try {
                const response = await fetch(`/api/admin/reject/${imageId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        approved_by: 'admin'
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 移除该项
                    const item = document.querySelector(`[data-image-id="${imageId}"]`);
                    if (item) {
                        item.remove();
                    }
                    
                    // 刷新列表
                    loadPendingImages();
                } else {
                    alert('审核失败: ' + data.error);
                }
            } catch (error) {
                console.error('审核异常:', error);
                alert('审核失败: ' + error.message);
            }
        }

        // 更新统计信息
        function updateStats(images) {
            document.getElementById('pendingCount').textContent = images.length;
            // TODO: 从API获取其他统计数据
        }

        // 打开图片模态框
        function openModal(imageSrc, title) {
            modalImage.src = imageSrc;
            modalTitle.textContent = title;
            imageModal.style.display = 'flex';
        }

        // 关闭模态框
        function closeModal() {
            imageModal.style.display = 'none';
        }
    </script>
</body>
</html>
