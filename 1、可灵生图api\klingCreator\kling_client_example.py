#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI API客户端示例
演示如何使用可灵AI API服务
"""

import requests
import base64
import os
import time
from typing import Optional

class KlingAPIClient:
    """可灵AI API客户端"""
    
    def __init__(self, base_url: str = 'http://localhost:8080'):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = 300  # 5分钟超时
    
    def check_health(self) -> dict:
        """检查服务健康状态"""
        response = self.session.get(f'{self.base_url}/health')
        return response.json()
    
    def get_balance(self) -> int:
        """获取账户积分"""
        response = self.session.get(f'{self.base_url}/api/balance')
        data = response.json()
        
        if data.get('success'):
            return data['balance']
        else:
            raise Exception(f"获取积分失败: {data.get('error')}")
    
    def generate_image_from_file(self, image_path: str, prompt: str, style: str = '默认') -> str:
        """从文件生成图片"""
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        with open(image_path, 'rb') as f:
            files = {'image': f}
            data = {
                'prompt': prompt,
                'style': style
            }
            
            response = self.session.post(f'{self.base_url}/api/generate', 
                                       files=files, data=data)
            result = response.json()
            
            if result.get('success'):
                return result['image_url']
            else:
                raise Exception(f"生成图片失败: {result.get('error')}")
    
    def generate_image_from_base64(self, image_base64: str, prompt: str, style: str = '默认') -> str:
        """从Base64生成图片"""
        data = {
            'image_base64': image_base64,
            'prompt': prompt,
            'style': style
        }
        
        response = self.session.post(f'{self.base_url}/api/generate', json=data)
        result = response.json()
        
        if result.get('success'):
            return result['image_url']
        else:
            raise Exception(f"生成图片失败: {result.get('error')}")
    
    def generate_image_from_url(self, image_url: str, prompt: str, style: str = '默认') -> str:
        """从URL生成图片"""
        data = {
            'image_url': image_url,
            'prompt': prompt,
            'style': style
        }
        
        response = self.session.post(f'{self.base_url}/api/generate', json=data)
        result = response.json()
        
        if result.get('success'):
            return result['image_url']
        else:
            raise Exception(f"生成图片失败: {result.get('error')}")
    
    def get_api_info(self) -> dict:
        """获取API信息"""
        response = self.session.get(f'{self.base_url}/api/info')
        return response.json()

def download_image(url: str, save_path: str) -> bool:
    """下载图片"""
    try:
        response = requests.get(url)
        if response.status_code == 200:
            with open(save_path, 'wb') as f:
                f.write(response.content)
            return True
        return False
    except Exception as e:
        print(f"下载图片失败: {e}")
        return False

def main():
    """主函数 - 演示API使用"""
    print("🚀 可灵AI API客户端示例")
    print("=" * 40)
    
    # 创建客户端
    client = KlingAPIClient()
    
    try:
        # 1. 检查服务状态
        print("1️⃣ 检查服务状态...")
        health = client.check_health()
        print(f"服务状态: {health.get('status')}")
        print(f"消息: {health.get('message')}")
        
        if health.get('status') != 'healthy':
            print("❌ 服务不健康，退出")
            return
        
        # 2. 获取积分
        print("\n2️⃣ 获取账户积分...")
        balance = client.get_balance()
        print(f"当前积分: {balance}")
        
        if balance <= 0:
            print("❌ 积分不足，无法生成图片")
            return
        
        # 3. 查找测试图片
        print("\n3️⃣ 查找测试图片...")
        test_images = [
            'uploads/华创瑞景园-1952x1056-小_deb0f4db..jpg',
            'uploads/yingwen_c0b3da3d..jpg',
            'examples/test_images/test.jpg'
        ]
        
        test_image = None
        for img_path in test_images:
            if os.path.exists(img_path):
                test_image = img_path
                break
        
        if not test_image:
            print("❌ 未找到测试图片")
            return
        
        print(f"使用测试图片: {test_image}")
        
        # 4. 生成图片 - 文件方式
        print("\n4️⃣ 生成图片 - 文件上传方式...")
        prompt1 = "一座现代化的摩天大楼，外墙是蓝色玻璃，周围有绿色植物装饰"
        
        print(f"提示词: {prompt1}")
        print("正在生成...")
        
        start_time = time.time()
        result_url1 = client.generate_image_from_file(test_image, prompt1)
        end_time = time.time()
        
        print(f"✅ 生成成功! 耗时: {end_time - start_time:.2f}秒")
        print(f"图片URL: {result_url1}")
        
        # 下载生成的图片
        output_file1 = f"generated_image_1_{int(time.time())}.png"
        if download_image(result_url1, output_file1):
            print(f"✅ 图片已保存到: {output_file1}")
        
        # 5. 生成图片 - Base64方式
        print("\n5️⃣ 生成图片 - Base64方式...")
        prompt2 = "一座古典的欧式城堡，坐落在山顶上，周围云雾缭绕"
        
        # 读取图片转换为Base64
        with open(test_image, 'rb') as f:
            image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode()
        
        print(f"提示词: {prompt2}")
        print("正在生成...")
        
        start_time = time.time()
        result_url2 = client.generate_image_from_base64(image_base64, prompt2)
        end_time = time.time()
        
        print(f"✅ 生成成功! 耗时: {end_time - start_time:.2f}秒")
        print(f"图片URL: {result_url2}")
        
        # 下载生成的图片
        output_file2 = f"generated_image_2_{int(time.time())}.png"
        if download_image(result_url2, output_file2):
            print(f"✅ 图片已保存到: {output_file2}")
        
        # 6. 再次检查积分
        print("\n6️⃣ 检查剩余积分...")
        final_balance = client.get_balance()
        used_points = balance - final_balance
        print(f"剩余积分: {final_balance}")
        print(f"本次消耗: {used_points} 积分")
        
        print("\n🎉 演示完成!")
        
    except Exception as e:
        print(f"❌ 发生错误: {e}")

if __name__ == '__main__':
    main()
