#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI图生图Web应用 - 完整版本（SQLite + 并发生成）
"""

import os
import json
import time
import uuid
import threading
import asyncio
from concurrent.futures import ThreadPoolExecutor
from pathlib import Path
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
from kling import ImageGen
from cookie_utils import get_cookie, check_cookie
import requests
from models_sqlite import db_manager, generation_record, generated_image, user_action, system_config
from PIL import Image
import re

app = Flask(__name__)
app.config['SECRET_KEY'] = 'kling-ai-image-generator'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 配置目录
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'web_output'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

# 确保目录存在
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# 线程池用于并发生成
executor = ThreadPoolExecutor(max_workers=5)

# 提示词预设
PROMPT_TEMPLATES = [
    {
        "id": "bio_building",
        "name": "生物发光建筑",
        "prompt": "一座完全由生物发光植物构成的奇幻建筑，孤立在纯黑色背景中。巨大的、发光的奇异花朵和流光溢彩的藤蔓构成了建筑的主体结构，半透明的叶片下透出柔和的内部光芒。空气中漂浮着由它散发出的点点荧光。主色调为梦幻般的翠绿色、宝蓝色和淡紫色，充满生命力。幻想概念艺术，强烈的发光效果，细节丰富，固定视角。"
    },
    {
        "id": "steampunk_fortress",
        "name": "蒸汽朋克堡垒",
        "prompt": "一座孤立在纯黑色空间中的蒸汽朋克机械堡垒。建筑内部的锅炉透过格栅和窗口发出炽热的橙红色光芒，裸露的黄铜管道和玻璃真空管闪烁着微光。复杂的齿轮结构缓慢转动，表面有精密的金属质感和铆钉。整体色调为浓郁的古铜色和炽热的橙色，充满工业动力美感。数字绘画，高度详细，4K，固定机位，纯黑色背景。"
    },
    {
        "id": "cyberpunk_style",
        "name": "赛博朋克风格",
        "prompt": "保留黑色底背景，楼体的风格转换成赛博朋克风格，绚丽多彩，线条丰富"
    }
]

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def sanitize_filename_keep_chinese(filename):
    """清理文件名，保留中文字符"""
    # 移除不安全的字符，但保留中文、英文、数字、点、横线、下划线
    safe_chars = re.sub(r'[<>:"/\\|?*]', '', filename)
    # 移除多余的空格
    safe_chars = re.sub(r'\s+', ' ', safe_chars).strip()
    # 如果文件名为空或只有扩展名，使用默认名称
    if not safe_chars or safe_chars.startswith('.'):
        safe_chars = f"image_{uuid.uuid4().hex[:8]}{safe_chars}"
    return safe_chars

def get_unique_filename(base_name, extension, output_dir):
    """生成唯一的文件名，避免覆盖，保留中文"""
    # 清理基础文件名，保留中文
    clean_base_name = sanitize_filename_keep_chinese(base_name)
    
    counter = 1
    while True:
        filename = f"{clean_base_name}_{counter}.{extension}"
        filepath = os.path.join(output_dir, filename)
        if not os.path.exists(filepath):
            return filename
        counter += 1

def get_image_dimensions(image_path):
    """获取图片尺寸"""
    try:
        with Image.open(image_path) as img:
            return img.size  # (width, height)
    except Exception:
        return None, None

def get_client_ip():
    """获取客户端IP"""
    if request.environ.get('HTTP_X_FORWARDED_FOR') is None:
        return request.environ['REMOTE_ADDR']
    else:
        return request.environ['HTTP_X_FORWARDED_FOR']

class ConcurrentImageGenerator:
    """支持并发的图生图生成器"""
    
    def __init__(self):
        self.cookie = get_cookie()
        if not self.cookie or not check_cookie():
            raise Exception("Cookie无效或不存在")
    
    def generate_single_image(self, image_path, prompt, record_id, style_name, base_name):
        """生成单张图片（在线程中运行）"""
        try:
            print(f"开始生成图片 - 记录ID: {record_id}, 风格: {style_name}")
            
            # 创建新的生成器实例（每个线程独立）
            generator = ImageGen(self.cookie)
            
            # 设置请求头
            generator.session.headers.update({
                "accept": "application/json, text/plain, */*",
                "accept-language": "zh",
                "content-type": "application/json",
                "time-zone": "Asia/Shanghai",
                "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            })
            
            # 上传图片
            image_payload_url = generator.image_uploader(image_path)
            
            # 构建请求载荷
            payload = {
                "type": "mmu_img2img_aiweb",
                "inputs": [
                    {
                        "inputType": "URL",
                        "url": image_payload_url,
                        "name": "input"
                    }
                ],
                "arguments": [
                    {
                        "name": "biz",
                        "value": "klingai"
                    },
                    {
                        "name": "prompt",
                        "value": prompt
                    },
                    {
                        "name": "imageCount",
                        "value": "1"
                    },
                    {
                        "name": "kolors_version",
                        "value": "2.0"
                    },
                    {
                        "name": "style",
                        "value": "默认"
                    },
                    {
                        "name": "referenceType",
                        "value": "mmu_img2img_aiweb_v20_stylize"
                    }
                ]
            }
            
            # 发送请求
            response = generator.session.post(
                generator.submit_url,
                json=payload,
                headers=generator.session.headers
            )
            
            if not response.ok:
                raise Exception(f"请求失败: {response.status_code} - {response.text}")
            
            response_body = response.json()
            data = response_body.get("data")
            
            if data and data.get("status") == 7:
                message = data.get("message")
                raise Exception(f"请求失败: {message}")
            
            request_id = data.get("task", {}).get("id") if data else None
            
            if not request_id:
                raise Exception("无法获取请求ID")
            
            print(f"获取到请求ID: {request_id}, 开始等待结果...")
            
            # 等待结果
            start_wait = time.time()
            while True:
                if int(time.time() - start_wait) > 600:  # 10分钟超时
                    raise Exception("请求超时")
                
                image_data, status = generator.fetch_metadata(request_id)
                
                if status.name == "PENDING":
                    time.sleep(3)
                elif status.name == "FAILED":
                    raise Exception("生成失败")
                else:
                    works = image_data.get("works", [])
                    if not works:
                        raise Exception("未找到生成的图片")
                    
                    resource = works[0].get("resource", {}).get("resource")
                    if resource:
                        # 下载并保存图片
                        response = requests.get(resource)
                        if response.status_code == 200:
                            # 生成唯一文件名，保留中文
                            output_filename = get_unique_filename(base_name, 'png', OUTPUT_FOLDER)
                            output_path = os.path.join(OUTPUT_FOLDER, output_filename)
                            
                            with open(output_path, 'wb') as f:
                                f.write(response.content)
                            
                            # 获取图片尺寸
                            width, height = get_image_dimensions(output_path)
                            file_size = os.path.getsize(output_path)
                            
                            # 保存到数据库
                            image_id = generated_image.add_image(
                                record_id=record_id,
                                image_path=output_path,
                                image_name=output_filename,
                                image_url=resource,
                                file_size=file_size,
                                width=width,
                                height=height
                            )
                            
                            print(f"图片生成成功: {output_filename}")
                            
                            return {
                                'success': True,
                                'filename': output_filename,
                                'prompt': prompt,
                                'style_name': style_name,
                                'record_id': record_id,
                                'image_id': image_id
                            }
                        else:
                            raise Exception(f"下载图片失败: {response.status_code}")
                    else:
                        raise Exception("无法获取图片URL")
                        
        except Exception as e:
            print(f"生成图片失败: {e}")
            # 更新状态为失败
            generation_record.update_status(record_id, 'failed')
            
            return {
                'success': False,
                'error': str(e),
                'prompt': prompt,
                'style_name': style_name,
                'record_id': record_id
            }

# 初始化数据库
def init_db():
    """初始化数据库连接"""
    if not db_manager.connect():
        print("警告: 数据库连接失败，某些功能可能不可用")
    else:
        print("✅ SQLite数据库连接成功")

@app.route('/')
def index():
    """主页"""
    return render_template('index.html', prompt_templates=PROMPT_TEMPLATES)

@app.route('/admin')
def admin():
    """管理员审核页面"""
    return render_template('admin.html')

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """上传文件"""
    try:
        print("收到上传请求")
        
        if 'file' not in request.files:
            return jsonify({'error': '没有文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if file and allowed_file(file.filename):
            # 保留中文文件名
            original_filename = file.filename
            print(f"原始文件名: {original_filename}")
            
            # 使用新的文件名处理，保留中文字符
            name, ext = os.path.splitext(original_filename)
            safe_name = sanitize_filename_keep_chinese(name)
            unique_id = str(uuid.uuid4())[:8]
            unique_filename = f"{safe_name}_{unique_id}.{ext}"
            
            filepath = os.path.join(UPLOAD_FOLDER, unique_filename)
            print(f"保存路径: {filepath}")
            file.save(filepath)
            
            return jsonify({
                'success': True,
                'filename': unique_filename,
                'original_name': original_filename,
                'safe_name': safe_name
            })
        else:
            return jsonify({'error': '不支持的文件格式'}), 400
            
    except Exception as e:
        print(f"上传异常: {e}")
        return jsonify({'error': f'上传失败: {str(e)}'}), 500

@app.route('/api/generate', methods=['POST'])
def generate_images():
    """并发生成图片"""
    try:
        data = request.get_json()
        filename = data.get('filename')
        prompts = data.get('prompts', [])
        ai_provider = data.get('ai_provider', 'kling')
        original_name = data.get('original_name', filename)

        if not filename or not prompts:
            return jsonify({'error': '缺少必要参数'}), 400

        filepath = os.path.join(UPLOAD_FOLDER, filename)
        if not os.path.exists(filepath):
            return jsonify({'error': '文件不存在'}), 400

        # 获取原始文件名（不含扩展名），保留中文
        base_name = os.path.splitext(original_name)[0]
        print(f"原始文件名: {original_name}, 基础名称: {base_name}")

        # 初始化生成器
        generator = ConcurrentImageGenerator()

        # 创建生成记录并提交并发任务
        futures = []
        record_ids = []

        for i, prompt_data in enumerate(prompts):
            # 支持新的数据格式
            if isinstance(prompt_data, dict):
                prompt = prompt_data.get('prompt', '')
                style_name = prompt_data.get('style_name', '默认')
            else:
                prompt = prompt_data
                style_name = '默认'

            # 创建生成记录
            record_id = generation_record.create_record(
                original_image_path=filepath,
                original_image_name=original_name,
                prompt=prompt,
                ai_provider=ai_provider,
                style_name=style_name
            )

            if record_id:
                record_ids.append(record_id)
                # 更新状态为生成中
                generation_record.update_status(record_id, 'generating')

                # 提交并发任务
                future = executor.submit(
                    generator.generate_single_image,
                    filepath, prompt, record_id, style_name, base_name
                )
                futures.append(future)

                print(f"已提交生成任务 {i+1}/{len(prompts)}: {style_name}")

        # 立即返回，不等待完成
        return jsonify({
            'success': True,
            'message': f'已提交 {len(futures)} 个生成任务，正在并发处理...',
            'record_ids': record_ids,
            'total_tasks': len(futures)
        })

    except Exception as e:
        print(f"生成失败: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/status/<int:record_id>')
def get_generation_status(record_id):
    """获取生成状态"""
    try:
        record = generation_record.get_record(record_id)
        if not record:
            return jsonify({'error': '记录不存在'}), 404

        images = generated_image.get_images_by_record(record_id)

        return jsonify({
            'record': dict(record),
            'images': images,
            'status': record['status']
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/image/<filename>')
def get_image(filename):
    """获取生成的图片"""
    filepath = os.path.join(OUTPUT_FOLDER, filename)
    if os.path.exists(filepath):
        return send_file(filepath)
    return jsonify({'error': '文件不存在'}), 404

@app.route('/api/balance')
def get_balance():
    """获取账户余额"""
    try:
        cookie = get_cookie()
        if not cookie or not check_cookie():
            return jsonify({'error': 'Cookie无效'}), 400

        generator = ImageGen(cookie)
        balance = generator.get_account_point()
        return jsonify({'balance': balance})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/history')
def get_history():
    """获取生成历史"""
    try:
        limit = request.args.get('limit', 50, type=int)
        records = generation_record.get_recent_records(limit)

        # 为每个记录获取生成的图片
        for record in records:
            record['images'] = generated_image.get_images_by_record(record['id'])

        return jsonify({'records': records})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/like/<int:image_id>', methods=['POST'])
def like_image(image_id):
    """点赞图片"""
    try:
        client_ip = get_client_ip()
        user_agent = request.headers.get('User-Agent', '')

        # 记录用户操作
        user_action.record_action(image_id, 'like', client_ip, user_agent)

        # 获取当前图片信息
        images = generated_image.db.execute_query(
            "SELECT likes, dislikes FROM generated_images WHERE id = ?",
            (image_id,)
        )

        if images:
            current_likes = images[0]['likes']
            current_dislikes = images[0]['dislikes']

            # 更新点赞数
            generated_image.update_likes(image_id, current_likes + 1, current_dislikes)

            return jsonify({
                'success': True,
                'likes': current_likes + 1,
                'dislikes': current_dislikes
            })
        else:
            return jsonify({'error': '图片不存在'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/dislike/<int:image_id>', methods=['POST'])
def dislike_image(image_id):
    """点踩图片"""
    try:
        client_ip = get_client_ip()
        user_agent = request.headers.get('User-Agent', '')

        # 记录用户操作
        user_action.record_action(image_id, 'dislike', client_ip, user_agent)

        # 获取当前图片信息
        images = generated_image.db.execute_query(
            "SELECT likes, dislikes FROM generated_images WHERE id = ?",
            (image_id,)
        )

        if images:
            current_likes = images[0]['likes']
            current_dislikes = images[0]['dislikes']

            # 更新点踩数
            generated_image.update_likes(image_id, current_likes, current_dislikes + 1)

            return jsonify({
                'success': True,
                'likes': current_likes,
                'dislikes': current_dislikes + 1
            })
        else:
            return jsonify({'error': '图片不存在'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/pending')
def get_pending_approval():
    """获取待审核的图片"""
    try:
        pending_images = generated_image.get_pending_approval()
        return jsonify({'images': pending_images})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/approve/<int:image_id>', methods=['POST'])
def approve_image(image_id):
    """审核通过图片"""
    try:
        data = request.get_json()
        approved_by = data.get('approved_by', 'admin')

        success = generated_image.approve_image(image_id, True, approved_by)

        if success:
            return jsonify({'success': True, 'message': '审核通过'})
        else:
            return jsonify({'error': '审核失败'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/admin/reject/<int:image_id>', methods=['POST'])
def reject_image(image_id):
    """审核拒绝图片"""
    try:
        data = request.get_json()
        approved_by = data.get('approved_by', 'admin')

        success = generated_image.approve_image(image_id, False, approved_by)

        if success:
            return jsonify({'success': True, 'message': '审核拒绝'})
        else:
            return jsonify({'error': '审核失败'}), 500

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/providers')
def get_providers():
    """获取可用的AI提供商"""
    try:
        # 目前只支持可灵
        providers = ['kling']
        return jsonify({'providers': providers})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 启动可灵AI图生图Web应用（完整版本）")
    print("📊 功能状态:")
    print("   ✅ 图片上传（保留中文文件名）")
    print("   ✅ 并发图片生成")
    print("   ✅ 结果展示")
    print("   ✅ SQLite数据库支持")
    print("   ✅ 历史记录功能")
    print("   ✅ 点赞点踩功能")
    print("   ✅ 管理员审核功能")
    print()

    # 初始化数据库
    init_db()

    print("🌐 访问地址:")
    print("   主页: http://localhost:5000")
    print("   管理页面: http://localhost:5000/admin")
    print()

    app.run(debug=True, host='0.0.0.0', port=5000)
