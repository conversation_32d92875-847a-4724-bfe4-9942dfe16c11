#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Playwright直接查看和操作已打开的Chrome浏览器页面
"""

import asyncio
from playwright.async_api import async_playwright


async def main():
    """连接到Chrome调试浏览器并查看页面"""
    print("连接到Chrome调试浏览器...")
    
    async with async_playwright() as p:
        try:
            # 连接到已打开的Chrome浏览器
            browser = await p.chromium.connect_over_cdp("http://localhost:9222")
            contexts = browser.contexts
            
            if not contexts:
                print("❌ 没有找到浏览器上下文")
                return
            
            # 使用第一个上下文
            context = contexts[0]
            pages = context.pages
            
            if not pages:
                print("❌ 没有找到打开的页面")
                return
            
            # 查找即梦AI页面
            target_page = None
            for page in pages:
                url = page.url
                if "jimeng.jianying.com" in url:
                    target_page = page
                    break
            
            if not target_page:
                print("❌ 未找到即梦AI页面")
                return
            
            print(f"✅ 找到即梦AI页面: {target_page.url}")
            
            # 查看页面中的首尾帧元素
            print("\n=== 查看首尾帧元素 ===")
            
            # 查找首帧区域
            first_frame_groups = await target_page.query_selector_all('.reference-group-h6uyrf:not(.last-frame-ZAyskt)')
            print(f"首帧区域数量: {len(first_frame_groups)}")
            
            # 查找尾帧区域
            last_frame_groups = await target_page.query_selector_all('.reference-group-h6uyrf.last-frame-ZAyskt')
            print(f"尾帧区域数量: {len(last_frame_groups)}")
            
            # 查看首帧详情
            if first_frame_groups:
                print("\n--- 首帧详情 ---")
                for i, group in enumerate(first_frame_groups):
                    frame_img = await group.query_selector('.reference-mgqKPd img')
                    if frame_img:
                        src = await frame_img.get_attribute('src')
                        print(f"首帧 {i+1}: {src[:50]}...")
                        
                        # 测试悬停显示删除按钮
                        print(f"测试首帧 {i+1} 悬停效果...")
                        await frame_img.hover()
                        await asyncio.sleep(1000)
                        
                        # 检查删除按钮
                        remove_container = await group.query_selector('.remove-button-container-x2kHww')
                        if remove_container:
                            is_visible = await remove_container.is_visible()
                            print(f"  删除按钮容器: {'可见' if is_visible else '不可见'}")
                            
                            if is_visible:
                                remove_button = await remove_container.query_selector('.remove-button-CGHPzk')
                                if remove_button:
                                    button_visible = await remove_button.is_visible()
                                    print(f"  删除按钮: {'可见' if button_visible else '不可见'}")
                                    
                                    if button_visible:
                                        print(f"  ✅ 首帧 {i+1} 删除按钮可用")
                                        # 这里可以选择是否真的点击删除
                                        # await remove_button.click()
                                    else:
                                        print(f"  ❌ 首帧 {i+1} 删除按钮不可见")
                                else:
                                    print(f"  ❌ 首帧 {i+1} 未找到删除按钮")
                        else:
                            print(f"  ❌ 首帧 {i+1} 未找到删除按钮容器")
            
            # 查看尾帧详情
            if last_frame_groups:
                print("\n--- 尾帧详情 ---")
                for i, group in enumerate(last_frame_groups):
                    frame_img = await group.query_selector('.reference-mgqKPd img')
                    if frame_img:
                        src = await frame_img.get_attribute('src')
                        print(f"尾帧 {i+1}: {src[:50]}...")
                        
                        # 测试悬停显示删除按钮
                        print(f"测试尾帧 {i+1} 悬停效果...")
                        await frame_img.hover()
                        await asyncio.sleep(1000)
                        
                        # 检查删除按钮
                        remove_container = await group.query_selector('.remove-button-container-x2kHww')
                        if remove_container:
                            is_visible = await remove_container.is_visible()
                            print(f"  删除按钮容器: {'可见' if is_visible else '不可见'}")
                            
                            if is_visible:
                                remove_button = await remove_container.query_selector('.remove-button-CGHPzk')
                                if remove_button:
                                    button_visible = await remove_button.is_visible()
                                    print(f"  删除按钮: {'可见' if button_visible else '不可见'}")
                                    
                                    if button_visible:
                                        print(f"  ✅ 尾帧 {i+1} 删除按钮可用")
                                        # 这里可以选择是否真的点击删除
                                        # await remove_button.click()
                                    else:
                                        print(f"  ❌ 尾帧 {i+1} 删除按钮不可见")
                                else:
                                    print(f"  ❌ 尾帧 {i+1} 未找到删除按钮")
                        else:
                            print(f"  ❌ 尾帧 {i+1} 未找到删除按钮容器")
            
            print("\n=== 页面查看完成 ===")
            print("如需实际清空首尾帧，请取消注释相应的 await remove_button.click() 行")
            
        except Exception as e:
            print(f"❌ 连接失败: {e}")


if __name__ == "__main__":
    print("使用Playwright查看页面中的首尾帧")
    print("确保Chrome浏览器已开启调试模式: chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug")
    print()
    
    asyncio.run(main())
