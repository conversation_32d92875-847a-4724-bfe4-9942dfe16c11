#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量墙体秀项目 - 即梦AI首尾帧视频生成器
基于即梦AI生产就绪API的批量视频生成工具
"""

import os
import time
import json
from typing import List, Dict, Any
from jimeng_production_ready import JimengProductionAPI

class BatchVideoGenerator:
    """批量视频生成器"""
    
    def __init__(self, cookie: str = None):
        """
        初始化批量视频生成器
        
        Args:
            cookie: 即梦AI的Cookie (从浏览器获取)
        """
        self.api = JimengProductionAPI(cookie)
        self.generated_videos = []
        
        print("🎬 批量墙体秀视频生成器初始化完成")
    
    def generate_video_from_images(
        self,
        first_image_path: str,
        last_image_path: str,
        prompt: str,
        output_name: str = None,
        duration: str = "5s",
        quality: str = "720P"
    ) -> Dict[str, Any]:
        """
        从本地图片生成视频
        
        Args:
            first_image_path: 首帧图片路径
            last_image_path: 尾帧图片路径
            prompt: 过渡描述
            output_name: 输出视频名称
            duration: 视频时长
            quality: 视频质量
            
        Returns:
            生成结果
        """
        print(f"🎯 开始生成视频: {output_name or '未命名'}")
        
        # 检查图片文件是否存在
        if not os.path.exists(first_image_path):
            return {
                'success': False,
                'message': f'首帧图片不存在: {first_image_path}',
                'output_name': output_name
            }
        
        if not os.path.exists(last_image_path):
            return {
                'success': False,
                'message': f'尾帧图片不存在: {last_image_path}',
                'output_name': output_name
            }
        
        # 注意: 这里需要先将本地图片上传到即梦AI
        # 由于图片上传需要复杂的签名算法，这里使用占位符URL
        # 在实际使用中，你需要先通过浏览器上传图片，然后获取URL
        
        print("⚠️ 注意: 需要先将图片上传到即梦AI获取URL")
        print(f"   首帧图片: {first_image_path}")
        print(f"   尾帧图片: {last_image_path}")
        print("   请先在浏览器中上传图片，然后使用返回的URL")
        
        # 示例URL格式 (需要替换为实际上传后的URL)
        first_image_url = "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/your-first-image-id~tplv-tb4s082cfz-resize:2048:2048.jpg"
        last_image_url = "https://p26-dreamina-sign.byteimg.com/tos-cn-i-tb4s082cfz/your-last-image-id~tplv-tb4s082cfz-resize:2048:2048.jpg"
        
        # 生成视频
        result = self.api.generate_first_last_frame_video(
            first_image_url=first_image_url,
            last_image_url=last_image_url,
            prompt=prompt,
            duration=duration,
            quality=quality
        )
        
        if result['success']:
            video_info = {
                'output_name': output_name,
                'first_image': first_image_path,
                'last_image': last_image_path,
                'prompt': prompt,
                'duration': duration,
                'quality': quality,
                'draft_id': result['draft_id'],
                'timestamp': time.time()
            }
            self.generated_videos.append(video_info)
            
            print(f"✅ 视频生成任务创建成功: {output_name}")
            return {
                'success': True,
                'message': '视频生成任务已创建',
                'video_info': video_info
            }
        else:
            print(f"❌ 视频生成失败: {result['message']}")
            return result
    
    def batch_generate_from_folder(
        self,
        images_folder: str,
        prompts: List[str],
        duration: str = "5s",
        quality: str = "720P"
    ) -> List[Dict[str, Any]]:
        """
        从文件夹批量生成视频
        
        Args:
            images_folder: 图片文件夹路径
            prompts: 提示词列表
            duration: 视频时长
            quality: 视频质量
            
        Returns:
            生成结果列表
        """
        print(f"📁 开始批量生成视频，文件夹: {images_folder}")
        
        if not os.path.exists(images_folder):
            print(f"❌ 文件夹不存在: {images_folder}")
            return []
        
        # 获取图片文件
        image_files = []
        for file in os.listdir(images_folder):
            if file.lower().endswith(('.png', '.jpg', '.jpeg')):
                image_files.append(file)
        
        image_files.sort()
        print(f"📊 找到 {len(image_files)} 个图片文件")
        
        if len(image_files) < 2:
            print("❌ 至少需要2张图片才能生成视频")
            return []
        
        results = []
        
        # 批量生成视频 (每两张图片生成一个视频)
        for i in range(0, len(image_files) - 1, 2):
            if i + 1 >= len(image_files):
                break
            
            first_image = os.path.join(images_folder, image_files[i])
            last_image = os.path.join(images_folder, image_files[i + 1])
            
            # 选择提示词
            prompt_index = (i // 2) % len(prompts)
            prompt = prompts[prompt_index]
            
            output_name = f"video_{i//2 + 1:03d}_{image_files[i].split('.')[0]}_to_{image_files[i+1].split('.')[0]}"
            
            print(f"\n🎬 生成视频 {i//2 + 1}/{(len(image_files)-1)//2 + 1}")
            
            result = self.generate_video_from_images(
                first_image_path=first_image,
                last_image_path=last_image,
                prompt=prompt,
                output_name=output_name,
                duration=duration,
                quality=quality
            )
            
            results.append(result)
            
            # 避免请求过于频繁
            time.sleep(2)
        
        print(f"\n📊 批量生成完成，共处理 {len(results)} 个视频")
        return results
    
    def get_all_status(self) -> Dict[str, Any]:
        """获取所有生成任务的状态"""
        print("📊 获取所有生成任务状态...")
        
        status_result = self.api.get_generation_status()
        
        if status_result['success']:
            print("✅ 状态查询成功")
            return status_result['data']
        else:
            print(f"❌ 状态查询失败: {status_result['error']}")
            return {}
    
    def save_generation_log(self, log_file: str = "video_generation_log.json"):
        """保存生成日志"""
        log_data = {
            'timestamp': time.time(),
            'total_videos': len(self.generated_videos),
            'videos': self.generated_videos
        }
        
        with open(log_file, 'w', encoding='utf-8') as f:
            json.dump(log_data, f, indent=2, ensure_ascii=False)
        
        print(f"📝 生成日志已保存: {log_file}")


def main():
    """主函数 - 演示批量视频生成"""
    print("🎬 批量墙体秀视频生成器演示")
    print("=" * 60)
    
    # 配置参数
    images_folder = "D:\\1、王云领\\7.18华创瑞景园\\ai生成的图2"
    
    # 墙体秀相关的提示词
    prompts = [
        "赛博朋克风格的建筑逐渐转换成生物发光植物建筑，科技感与自然感的完美融合，炫酷的光效过渡，电影级别的视觉效果",
        "现代建筑外立面从简约风格转换为华丽装饰风格，材质从玻璃幕墙变为石材雕刻，光影效果自然过渡",
        "建筑从白天的明亮外观转换为夜晚的灯光效果，窗户逐渐亮起，整体氛围从清新转为温馨",
        "建筑外观从春季的清新绿意转换为秋季的金黄色调，植被颜色变化，光线从明亮转为温暖",
        "固定镜头，完美过渡效果，电影感调色，炫酷过渡"
    ]
    
    try:
        # 初始化生成器
        # 注意: 需要从浏览器获取最新的Cookie
        generator = BatchVideoGenerator()
        
        # 获取用户积分
        credit_result = generator.api.get_user_credit()
        if credit_result['success']:
            print(f"💰 当前积分: {credit_result['data']}")
        
        # 批量生成视频
        print(f"\n📁 开始批量生成，图片文件夹: {images_folder}")
        
        if os.path.exists(images_folder):
            results = generator.batch_generate_from_folder(
                images_folder=images_folder,
                prompts=prompts,
                duration="5s",
                quality="720P"
            )
            
            # 显示结果
            print(f"\n📊 批量生成结果:")
            success_count = sum(1 for r in results if r.get('success'))
            print(f"✅ 成功: {success_count}")
            print(f"❌ 失败: {len(results) - success_count}")
            
            # 保存日志
            generator.save_generation_log()
            
            # 查询状态
            print(f"\n📊 查询生成状态...")
            status = generator.get_all_status()
            
        else:
            print(f"❌ 图片文件夹不存在: {images_folder}")
            print("💡 请修改 images_folder 变量为你的图片文件夹路径")
        
        print(f"\n🎯 批量视频生成演示完成!")
        print(f"📚 使用说明:")
        print(f"  1. 确保图片文件夹存在且包含图片文件")
        print(f"  2. 从浏览器获取最新的Cookie")
        print(f"  3. 先在浏览器中上传图片获取URL")
        print(f"  4. 修改代码中的图片URL")
        print(f"  5. 运行批量生成")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
