<?xml version='1.0' encoding="utf-8"?>

<charsets>

<copyright>
 Copyright (c) 2003, 2023, Oracle and/or its affiliates.
   Use is subject to license terms

  This program is free software; you can redistribute it and/or modify
  it under the terms of the GNU General Public License, version 2.0,
  as published by the Free Software Foundation.

  This program is also distributed with certain software (including
  but not limited to OpenSSL) that is licensed under separate terms,
  as designated in a particular file or component or in included license
  documentation.  The authors of MySQL hereby grant you an additional
  permission to link the program and your derivative works with the
  separately licensed software that they have included with MySQL.

  This program is distributed in the hope that it will be useful,
  but WITHOUT ANY WARRANTY; without even the implied warranty of
  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  GNU General Public License, version 2.0, for more details.

  You should have received a copy of the GNU General Public License
  along with this program; if not, write to the Free Software
  Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
</copyright>

<charset name="latin7">

<ctype>
<map>
 00
 20 20 20 20 20 20 20 20 20 28 28 28 28 28 20 20
 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20 20
 48 10 10 10 10 10 10 10 10 10 10 10 10 10 10 10
 84 84 84 84 84 84 84 84 84 84 10 10 10 10 10 10
 10 81 81 81 81 81 81 01 01 01 01 01 01 01 01 01
 01 01 01 01 01 01 01 01 01 01 01 10 10 10 10 10
 10 82 82 82 82 82 82 02 02 02 02 02 02 02 02 02
 02 02 02 02 02 02 02 02 02 02 02 10 10 10 10 20
 01 20 10 20 10 10 00 00 20 10 20 10 20 10 10 10
 20 10 10 10 10 10 10 10 20 00 20 10 20 10 10 20
 48 20 10 10 10 20 10 10 10 10 01 10 10 10 10 01
 10 10 10 10 10 10 10 10 10 10 02 10 10 10 10 02
 01 01 01 01 01 01 01 01 01 01 01 01 01 01 01 01
 01 01 01 01 01 01 01 10 01 01 01 01 01 01 01 02
 02 02 02 02 02 02 02 02 02 02 02 02 02 02 02 02
 02 02 02 02 02 02 02 10 02 02 02 02 02 02 02 10
</map>
</ctype>


<lower>
<map>
 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
 10 11 12 13 14 15 16 17 18 19 1A 1B 1C 1D 1E 1F
 20 21 22 23 24 25 26 27 28 29 2A 2B 2C 2D 2E 2F
 30 31 32 33 34 35 36 37 38 39 3A 3B 3C 3D 3E 3F
 40 61 62 63 64 65 66 67 68 69 6A 6B 6C 6D 6E 6F
 70 71 72 73 74 75 76 77 78 79 7A 5B 5C 5D 5E 5F
 60 61 62 63 64 65 66 67 68 69 6A 6B 6C 6D 6E 6F
 70 71 72 73 74 75 76 77 78 79 7A 7B 7C 7D 7E 7F
 80 81 82 83 84 85 86 87 88 89 8A 8B 8C 8D 8E 8F
 90 91 92 93 94 95 96 97 98 99 9A 9B 9C 9D 9E 9F
 A0 A1 A2 A3 A4 A5 A6 A7 B8 A9 BA AB AC AD AE BF
 B0 B1 B2 B3 B4 B5 B6 B7 B8 B9 BA BB BC BD BE BF
 E0 E1 E2 E3 E4 E5 E6 E7 E8 E9 EA EB EC ED EE EF
 F0 F1 F2 F3 F4 F5 F6 D7 F8 F9 FA FB FC FD FE DF
 E0 E1 E2 E3 E4 E5 E6 E7 E8 E9 EA EB EC ED EE EF
 F0 F1 F2 F3 F4 F5 F6 F7 F8 F9 FA FB FC FD FE FF
</map>
</lower>


<upper>
<map>
 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
 10 11 12 13 14 15 16 17 18 19 1A 1B 1C 1D 1E 1F
 20 21 22 23 24 25 26 27 28 29 2A 2B 2C 2D 2E 2F
 30 31 32 33 34 35 36 37 38 39 3A 3B 3C 3D 3E 3F
 40 41 42 43 44 45 46 47 48 49 4A 4B 4C 4D 4E 4F
 50 51 52 53 54 55 56 57 58 59 5A 5B 5C 5D 5E 5F
 60 41 42 43 44 45 46 47 48 49 4A 4B 4C 4D 4E 4F
 50 51 52 53 54 55 56 57 58 59 5A 7B 7C 7D 7E 7F
 80 81 82 83 84 85 86 87 88 89 8A 8B 8C 8D 8E 8F
 90 91 92 93 94 95 96 97 98 99 9A 9B 9C 9D 9E 9F
 A0 A1 A2 A3 A4 A5 A6 A7 A8 A9 AA AB AC AD AE AF
 B0 B1 B2 B3 B4 B5 B6 B7 A8 B9 AA BB BC BD BE AF
 C0 C1 C2 C3 C4 C5 C6 C7 C8 C9 CA CB CC CD CE CF
 D0 D1 D2 D3 D4 D5 D6 D7 D8 D9 DA DB DC DD DE DF
 C0 C1 C2 C3 C4 C5 C6 C7 C8 C9 CA CB CC CD CE CF
 D0 D1 D2 D3 D4 D5 D6 F7 D8 D9 DA DB DC DD DE FF
</map>
</upper>


<unicode>
<map>
0000 0001 0002 0003 0004 0005 0006 0007 0008 0009 000A 000B 000C 000D 000E 000F
0010 0011 0012 0013 0014 0015 0016 0017 0018 0019 001A 001B 001C 001D 001E 001F
0020 0021 0022 0023 0024 0025 0026 0027 0028 0029 002A 002B 002C 002D 002E 002F
0030 0031 0032 0033 0034 0035 0036 0037 0038 0039 003A 003B 003C 003D 003E 003F
0040 0041 0042 0043 0044 0045 0046 0047 0048 0049 004A 004B 004C 004D 004E 004F
0050 0051 0052 0053 0054 0055 0056 0057 0058 0059 005A 005B 005C 005D 005E 005F
0060 0061 0062 0063 0064 0065 0066 0067 0068 0069 006A 006B 006C 006D 006E 006F
0070 0071 0072 0073 0074 0075 0076 0077 0078 0079 007A 007B 007C 007D 007E 007F
0080 0081 0082 0083 0084 0085 0086 0087 0088 0089 008A 008B 008C 008D 008E 008F
0090 0091 0092 0093 0094 0095 0096 0097 0098 0099 009A 009B 009C 009D 009E 009F
00A0 201D 00A2 00A3 00A4 201E 00A6 00A7 00D8 00A9 0156 00AB 00AC 00AD 00AE 00C6
00B0 00B1 00B2 00B3 201C 00B5 00B6 00B7 00F8 00B9 0157 00BB 00BC 00BD 00BE 00E6
0104 012E 0100 0106 00C4 00C5 0118 0112 010C 00C9 0179 0116 0122 0136 012A 013B
0160 0143 0145 00D3 014C 00D5 00D6 00D7 0172 0141 015A 016A 00DC 017B 017D 00DF
0105 012F 0101 0107 00E4 00E5 0119 0113 010D 00E9 017A 0117 0123 0137 012B 013C
0161 0144 0146 00F3 014D 00F5 00F6 00F7 0173 0142 015B 016B 00FC 017C 017E 2019
</map>
</unicode>


<collation name="latin7_estonian_cs">
<map>
 00 02 03 04 05 06 07 08 09 2E 2F 30 31 32 0A 0B
 0C 0D 0E 0F 10 11 12 13 14 15 16 17 18 19 1A 1B
 2C 33 34 35 36 37 38 27 39 3A 3B 5D 3C 28 3D 3E
 76 7A 7C 7E 80 81 82 83 84 85 3F 40 5E 5F 60 41
 42 86 90 92 98 9A A4 A6 AA AC B2 B4 B8 BE C0 C6
 CE D0 D2 D6 E5 E8 EE F0 FA FC DD 43 44 45 46 47
 48 87 91 93 99 9B A5 A7 AB AD B3 B5 B9 BF C1 C7
 CF D1 D3 D7 E6 E9 EF F1 FB FD DE 49 4A 4B 4C 1C
 01 1D 57 1E 5A 74 71 72 1F 75 20 5B 21 4E 52 51
 22 55 56 58 59 73 2A 2B 23 E7 24 5C 25 4F 54 26
 2D FE 66 67 68 FF 4D 69 CC 6A D4 62 6B 29 6C 8E
 6D 61 7D 7F 50 6E 6F 70 CD 7B D5 63 77 78 79 8F
 8C B0 88 94 F4 8A A2 A0 96 9C DF 9E A8 B6 AE BA
 DB C2 C4 C8 CA F2 F6 64 EC BC D8 EA F8 E1 E3 DA
 8D B1 89 95 F5 8B A3 A1 97 9D E0 9F A9 B7 AF BB
 DC C3 C5 C9 CB F3 F7 65 ED BD D9 EB F9 E2 E4 53
</map>
</collation>


<collation name="latin7_general_cs">
<!-- Created for case-sensitive record search              -->
<!-- by Andis Grasis & Rihards Grasis e-mail:<EMAIL> -->
<map>
 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
 10 11 12 13 14 15 16 17 18 19 1A 1B 1C 1D 1E 1F
 30 32 33 34 35 36 37 2B 38 39 3A 5C 3B 2C 3C 3D
 76 7A 7C 7E 80 81 82 83 84 85 3E 3F 5D 5E 5F 40
 41 86 92 94 9A 9C A6 A8 AC AE B4 B6 BA C0 C2 C8
 D4 D6 D8 DC E3 E6 EE F0 F2 F4 F6 42 43 44 45 46
 47 87 93 95 9B 9D A7 A9 AD AF B5 B7 BB C1 C3 C9
 D5 D7 D9 DD E4 E7 EF F1 F3 F5 F7 48 49 4A 4B 20
 75 21 56 22 59 73 70 71 23 74 24 5A 25 4D 51 50
 26 54 55 57 58 72 2E 2F 27 E5 28 5B 29 4E 53 2A
 31 FE 65 66 67 FF 4C 68 D3 69 DA 61 6A 2D 6B 90
 6C 60 7D 7F 4F 6D 6E 6F D2 7B DB 62 77 78 79 91
 8E B2 8A 96 88 8C A4 A2 98 9E F8 A0 AA B8 B0 BE
 E1 C4 C6 CA CE D0 CC 63 EC BC DE EA E8 FA FC E0
 8F B3 8B 97 89 8D A5 A3 99 9F F9 A1 AB B9 B1 BF
 E2 C5 C7 CB CF D1 CD 64 ED BD DF EB E9 FB FD 52
</map>
</collation>


<collation name="latin7_general_ci">
<!-- Created for case-insensitive record search -->
<!-- Created by Andis & Rihards                 -->
<map>
 00 01 02 03 04 05 06 07 08 09 0A 0B 0C 0D 0E 0F
 10 11 12 13 14 15 16 17 18 19 1A 1B 1C 1D 1E 1F
 30 32 33 34 35 36 37 2B 38 39 3A 5C 3B 2C 3C 3D
 76 7A 7C 7E 80 81 82 83 84 85 3E 3F 5D 5E 5F 40
 41 86 92 94 9A 9C A6 A8 AC AE B4 B6 BA C0 C2 C8
 D4 D6 D8 DC E3 E6 EE F0 F2 F4 F6 42 43 44 45 46
 47 86 92 94 9A 9C A6 A8 AC AE B4 B6 BA C0 C2 C8
 D4 D6 D8 DC E2 E6 EE F0 F2 F4 F6 48 49 4A 4B 20
 75 21 56 22 59 73 70 71 23 74 24 5A 25 4D 51 50
 26 54 55 57 58 72 2E 2F 27 E5 28 5B 29 4E 53 2A
 31 FE 65 66 67 FF 4C 68 2D 69 DA 61 6A 2D 6B 90
 6C 60 7D 7F 4F 6D 6E 6F D3 7B DB 62 77 78 79 90
 8E B2 8A 96 88 8C A4 A2 98 9E F8 A0 AA B8 B0 BE
 E1 C4 C6 CA CE D0 CC 63 EC BC DE EA E8 FA FC E0
 8E B2 8A 96 88 8C A4 A2 98 9E F8 A0 AA B8 B0 BE
 E1 C4 C6 CA CE D0 CC 64 EC BC DE EA E8 FA FC 52
</map>
</collation>

<collation name="latin7_bin"	flag="binary"/>

</charset>

</charsets>
