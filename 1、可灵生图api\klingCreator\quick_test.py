#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可灵AI快速测试脚本
"""

from kling import ImageGen
import os
from cookie_utils import get_cookie, check_cookie

def main():
    print("🎨 可灵AI图片生成器 - 快速测试")
    print("=" * 50)
    
    # 从cookie.txt文件读取cookie
    cookie = get_cookie()

    if not cookie:
        print("❌ 无法读取cookie！")
        print("请确保cookie.txt文件存在且包含有效的cookie字符串")
        return

    if not check_cookie():
        print("❌ Cookie格式不正确")
        return
    
    # 获取提示词
    prompt = input("请输入图片描述 (默认: 一只可爱的小猫): ").strip()
    if not prompt:
        prompt = "一只可爱的小猫"
    
    try:
        print("\n🚀 正在初始化...")
        image_gen = ImageGen(cookie)
        
        print("🎨 开始生成图片...")
        print(f"📝 提示词: {prompt}")
        
        # 创建输出目录
        output_dir = "./test_output"
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成图片
        image_gen.save_images(
            prompt=prompt,
            output_dir=output_dir,
            count=2,  # 生成2张图片
            ratio="1:1",  # 正方形
            model_name="2.1"  # 最新模型
        )
        
        print("✅ 图片生成完成！")
        print(f"📂 图片保存在: {os.path.abspath(output_dir)}")
        
        # 显示余额
        balance = image_gen.get_account_point()
        print(f"💰 账户余额: {balance} 积分")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        print("\n可能的原因:")
        print("1. Cookie已过期，请重新获取")
        print("2. 账户积分不足")
        print("3. 网络连接问题")
        print("4. 提示词包含敏感内容")

if __name__ == "__main__":
    main()
