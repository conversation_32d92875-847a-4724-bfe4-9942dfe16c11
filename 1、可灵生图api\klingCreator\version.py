"""
版本信息
"""

__version__ = "0.1.0"
__author__ = "批量墙体秀项目团队"
__description__ = "AI图片生成与视频过渡生成系统"

# 版本历史
VERSION_HISTORY = {
    "0.1.0": {
        "date": "2025-08-01",
        "description": "初始版本发布",
        "未解决问题": [
            "自动化提交的时候，积分不足，在前端报错应该要显示积分不足",
            "没有添加任务到视频生成任务"
        ]
    }
}

def get_version():
    """获取当前版本号"""
    return __version__

def get_version_info():
    """获取详细版本信息"""
    return {
        "version": __version__,
        "author": __author__,
        "description": __description__,
        "history": VERSION_HISTORY
    }
