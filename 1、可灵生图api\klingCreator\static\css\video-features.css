/* 视频功能相关样式 */

/* 多选工具栏 */
.multi-select-toolbar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.toolbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.selected-info {
    font-size: 16px;
    font-weight: 500;
}

.toolbar-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.toolbar-actions .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.toolbar-actions .btn-primary {
    background: #4CAF50;
    color: white;
}

.toolbar-actions .btn-primary:hover:not(:disabled) {
    background: #45a049;
    transform: translateY(-2px);
}

.toolbar-actions .btn-primary:disabled {
    background: #cccccc;
    cursor: not-allowed;
}

.toolbar-actions .btn-secondary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.toolbar-actions .btn-secondary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* 多选模式样式 */
.multi-select-btn.active {
    background: #667eea !important;
    color: white !important;
}

.generated-card.multi-select-mode {
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.generated-card.multi-select-mode:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.generated-card.selected {
    border: 3px solid #4CAF50;
    box-shadow: 0 0 20px rgba(76, 175, 80, 0.4);
}

.selection-order {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #4CAF50;
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 14px;
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 视频任务区域 */
.video-tasks-section {
    margin-top: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.video-tasks-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 20px;
}

.video-task-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.video-task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.task-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.task-header h4 {
    margin: 0;
    color: #333;
    font-size: 16px;
}

.task-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.task-status.pending {
    background: #fff3cd;
    color: #856404;
}

.task-status.processing {
    background: #d1ecf1;
    color: #0c5460;
}

.task-status.completed {
    background: #d4edda;
    color: #155724;
}

.task-status.failed {
    background: #f8d7da;
    color: #721c24;
}

.task-info {
    margin-bottom: 15px;
}

.task-detail {
    margin-bottom: 8px;
    color: #666;
    font-size: 14px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-top: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.3s ease;
}

.task-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.task-actions .btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.task-actions .btn-primary {
    background: #007bff;
    color: white;
}

.task-actions .btn-primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
}

.task-actions .btn-secondary {
    background: #6c757d;
    color: white;
}

.task-actions .btn-secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.error-message {
    background: #f8d7da;
    color: #721c24;
    padding: 10px;
    border-radius: 6px;
    margin-top: 10px;
    font-size: 14px;
}

.empty-state {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .toolbar-content {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar-actions {
        justify-content: center;
    }
    
    .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .task-actions {
        justify-content: center;
    }
}

/* 通知样式 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: #4CAF50;
}

.notification.error {
    background: #f44336;
}

.notification.warning {
    background: #ff9800;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
