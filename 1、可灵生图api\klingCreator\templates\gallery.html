<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片画廊 - 可灵AI图生图</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/gallery.css') }}">
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="nav-brand">
                <h1>🎨 可灵AI图生图 - 图片画廊</h1>
            </div>
            <div class="nav-links">
                <a href="/" class="nav-link">🏠 首页</a>
                <a href="/gallery" class="nav-link active">🖼️ 画廊</a>
            </div>
        </nav>

        <!-- 页面标题 -->
        <div class="page-header">
            <h2>📸 已上传图片概览</h2>
            <p class="page-description">查看所有已上传的图片和生成统计</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-section">
            <div class="stat-card">
                <div class="stat-icon">📁</div>
                <div class="stat-info">
                    <div class="stat-number" id="total-uploads">0</div>
                    <div class="stat-label">已上传图片</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎨</div>
                <div class="stat-info">
                    <div class="stat-number" id="total-generated">0</div>
                    <div class="stat-label">生成图片总数</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">⭐</div>
                <div class="stat-info">
                    <div class="stat-number" id="total-likes">0</div>
                    <div class="stat-label">收藏图片</div>
                </div>
            </div>

        </div>

        <!-- 搜索和筛选 -->
        <div class="filter-section">
            <div class="search-box">
                <input type="text" id="search-input" placeholder="🔍 搜索图片文件名...">
            </div>
            <div class="filter-options">
                <select id="filter-select">
                    <option value="all">📋 全部图片</option>
                    <option value="favorited">⭐ 已收藏</option>
                    <option value="marked">🗑️ 标记删除</option>
                    <option value="unmarked">📷 未标记</option>
                </select>
                <select id="sort-select">
                    <option value="newest">📅 最新上传</option>
                    <option value="oldest">📅 最早上传</option>
                    <option value="latest-generated">🆕 最新生成</option>
                    <option value="most-generated">🎨 生成最多</option>
                    <option value="most-liked">⭐ 收藏最多</option>
                </select>
            </div>
        </div>

        <!-- 图片网格 -->
        <div class="gallery-grid" id="gallery-grid">
            <!-- 动态加载图片卡片 -->
        </div>

        <!-- 加载状态 -->
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
            <p>正在加载图片...</p>
        </div>

        <!-- 空状态 -->
        <div class="empty-state" id="empty-state" style="display: none;">
            <div class="empty-icon">📷</div>
            <h3>还没有上传任何图片</h3>
            <p>去首页上传第一张图片开始创作吧！</p>
            <a href="/" class="btn btn-primary">🚀 开始创作</a>
        </div>
    </div>



    <script src="{{ url_for('static', filename='js/gallery_fixed.js') }}?v=7"></script>
</body>
</html>
