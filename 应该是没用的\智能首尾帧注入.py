#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能首尾帧注入 - 根据当前页面状态智能决定注入首帧还是尾帧
"""

import asyncio
import os
import sys
import base64
import mimetypes
from pathlib import Path
from playwright.async_api import async_playwright


def read_image_as_base64(image_path: str):
    """读取图片并转换为Base64"""
    try:
        mime_type, _ = mimetypes.guess_type(image_path)
        if not mime_type or not mime_type.startswith('image/'):
            mime_type = 'image/png'
        
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        return {
            "name": os.path.basename(image_path),
            "data": base64_data,
            "type": mime_type,
            "size": len(image_data)
        }
    except Exception as e:
        print(f"❌ 读取图片失败: {e}")
        return None


async def inject_frame(page, frame_data, frame_name):
    """注入单个帧"""
    try:
        inject_result = await page.evaluate(f"""
            () => {{
                console.log('💉 开始注入{frame_name}');

                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');
                    if (inputs.length === 0) {{
                        console.error('未找到文件输入元素');
                        return false;
                    }}

                    const input = inputs[0];  // 使用第一个文件输入
                    console.log('使用文件输入:', input);

                    // 强制显示元素
                    input.style.display = 'block !important';
                    input.style.visibility = 'visible !important';
                    input.style.opacity = '1 !important';
                    input.style.position = 'static !important';

                    // 创建文件对象
                    const file = window.createFileFromBase64(
                        '{frame_data["data"]}',
                        '{frame_data["name"]}',
                        '{frame_data["type"]}'
                    );

                    if (!file) {{
                        console.error('{frame_name}文件创建失败');
                        return false;
                    }}

                    // 设置文件到input
                    const success = window.setFileToInput(input, file);

                    if (success) {{
                        console.log('✅ {frame_name}注入成功');
                        return true;
                    }} else {{
                        console.error('❌ {frame_name}注入失败');
                        return false;
                    }}

                }} catch (error) {{
                    console.error('{frame_name}注入异常:', error);
                    return false;
                }}
            }}
        """)

        return inject_result

    except Exception as e:
        print(f"❌ {frame_name}注入异常: {e}")
        return False


async def smart_inject_frames():
    """智能注入首尾帧"""
    
    # 🔧 配置区域 - 请修改这里的图片路径
    # ==========================================
    first_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\保留黑色底背景_保留楼体轮廓_楼体的风格转换成赛博朋克风格.png"
    last_frame_path = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (3).png"
    # ==========================================
    
    print("🧠 智能首尾帧注入工具")
    print("=" * 50)
    print(f"📸 首帧路径: {Path(first_frame_path).name}")
    print(f"📸 尾帧路径: {Path(last_frame_path).name}")
    print()
    
    # 验证文件存在
    if not os.path.exists(first_frame_path):
        print(f"❌ 首帧图片不存在: {first_frame_path}")
        return False
    
    if not os.path.exists(last_frame_path):
        print(f"❌ 尾帧图片不存在: {last_frame_path}")
        return False
    
    print("✅ 图片文件验证通过")
    
    # 读取图片数据
    print("📤 读取图片数据...")
    first_frame_data = read_image_as_base64(first_frame_path)
    last_frame_data = read_image_as_base64(last_frame_path)
    
    if not first_frame_data or not last_frame_data:
        return False
    
    print(f"✅ 首帧数据: {first_frame_data['size']} 字节")
    print(f"✅ 尾帧数据: {last_frame_data['size']} 字节")
    
    # 检查Chrome调试端口
    debug_port = 9222
    print("🔍 检查Chrome调试端口...")
    
    import socket
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("❌ Chrome调试端口未开启")
        return False
    
    print("✅ Chrome调试端口已开启")
    
    async with async_playwright() as p:
        try:
            print("🔗 连接到Chrome浏览器...")
            
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debug_port}")
            contexts = browser.contexts
            
            if not contexts:
                print("❌ 未找到浏览器上下文")
                return False
            
            # 查找即梦AI页面
            jimeng_page = None
            for context in contexts:
                for page in context.pages:
                    if 'jimeng.jianying.com' in page.url:
                        jimeng_page = page
                        print(f"✅ 找到即梦AI页面: {page.url}")
                        break
                if jimeng_page:
                    break
            
            if not jimeng_page:
                print("❌ 未找到即梦AI页面")
                return False
            
            # 执行智能注入
            success = await perform_smart_injection(jimeng_page, first_frame_data, last_frame_data)
            
            if success:
                print("✅ 智能注入成功！")
                return True
            else:
                print("❌ 智能注入失败")
                return False
                
        except Exception as e:
            print(f"❌ 连接浏览器失败: {e}")
            return False


async def perform_smart_injection(page, first_frame_data, last_frame_data):
    """执行智能注入 - 清空后依次注入首尾帧"""
    try:
        print("🧠 开始智能清空并重新注入...")

        # 等待页面稳定
        await asyncio.sleep(2)

        # 步骤1: 检测并清空已有注入
        print("�️ 步骤1: 检测并清空已有注入...")

        clear_result = await page.evaluate("""
            () => {
                console.log('�️ 检测并清空已有注入');

                const result = {
                    originalBlobImages: 0,
                    clearedImages: 0,
                    resetInputs: 0,
                    errors: []
                };

                try {
                    // 检查当前预览图片数量
                    const blobImages = document.querySelectorAll('img[src*="blob:"]');
                    result.originalBlobImages = blobImages.length;

                    console.log('发现预览图片:', result.originalBlobImages, '个');

                    if (result.originalBlobImages > 0) {
                        console.log('开始清空已有图片...');

                        // 方法1: 尝试移除预览图片
                        blobImages.forEach((img, index) => {
                            try {
                                // 尝试移除图片元素
                                if (img.parentElement) {
                                    img.parentElement.removeChild(img);
                                    result.clearedImages++;
                                    console.log(`移除了预览图片 ${index}`);
                                }
                            } catch (e) {
                                console.log(`移除预览图片 ${index} 失败:`, e);
                            }
                        });

                        // 方法2: 重置所有文件输入
                        const fileInputs = document.querySelectorAll('input[type="file"]');
                        fileInputs.forEach((input, index) => {
                            try {
                                input.value = '';  // 清空文件
                                input.files = new DataTransfer().files;  // 重置files属性
                                result.resetInputs++;
                                console.log(`重置了文件输入 ${index}`);
                            } catch (e) {
                                console.log(`重置文件输入 ${index} 失败:`, e);
                            }
                        });

                        // 方法3: 尝试点击清空或重置按钮
                        const clearButtons = document.querySelectorAll('button, div[role="button"], span[role="button"]');
                        for (const btn of clearButtons) {
                            const text = (btn.textContent || '').toLowerCase();
                            if (text.includes('清空') || text.includes('重置') || text.includes('删除') ||
                                text.includes('clear') || text.includes('reset') || text.includes('remove')) {
                                try {
                                    if (btn.offsetParent && !btn.disabled) {
                                        btn.click();
                                        console.log('点击了清空按钮:', text);
                                        break;
                                    }
                                } catch (e) {
                                    console.log('点击清空按钮失败:', e);
                                }
                            }
                        }

                        // 方法4: 刷新上传区域
                        const uploadAreas = document.querySelectorAll('[class*="upload"], [class*="reference"]');
                        uploadAreas.forEach(area => {
                            try {
                                // 触发重置事件
                                const resetEvent = new Event('reset', { bubbles: true });
                                area.dispatchEvent(resetEvent);
                                console.log('触发了上传区域重置事件');
                            } catch (e) {
                                console.log('触发重置事件失败:', e);
                            }
                        });
                    }

                } catch (error) {
                    console.error('清空过程出错:', error);
                    result.errors.push('清空出错: ' + error.message);
                }

                console.log('清空结果:', result);
                return result;
            }
        """)

        print(f"🗑️ 清空结果:")
        print(f"   原有预览图片: {clear_result['originalBlobImages']} 个")
        print(f"   清空图片: {clear_result['clearedImages']} 个")
        print(f"   重置输入: {clear_result['resetInputs']} 个")

        if clear_result['errors']:
            print(f"   清空错误: {len(clear_result['errors'])} 个")

        # 等待清空操作完成
        await asyncio.sleep(3)

        # 步骤2: 验证清空结果
        print("🔍 步骤2: 验证清空结果...")

        after_clear_state = await page.evaluate("""
            () => {
                const state = {
                    blobImages: document.querySelectorAll('img[src*="blob:"]').length,
                    fileInputs: document.querySelectorAll('input[type="file"]').length
                };
                console.log('清空后状态:', state);
                return state;
            }
        """)

        print(f"📊 清空后状态:")
        print(f"   剩余预览图片: {after_clear_state['blobImages']} 个")
        print(f"   文件输入: {after_clear_state['fileInputs']} 个")
        
        # 步骤3: 注入JavaScript辅助函数
        print("🔧 步骤3: 注入JavaScript辅助函数...")

        await page.evaluate("""
            () => {
                if (!window.createFileFromBase64) {
                    window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                        try {
                            const byteCharacters = atob(base64Data);
                            const byteNumbers = new Array(byteCharacters.length);
                            for (let i = 0; i < byteCharacters.length; i++) {
                                byteNumbers[i] = byteCharacters.charCodeAt(i);
                            }
                            const byteArray = new Uint8Array(byteNumbers);
                            return new File([byteArray], fileName, { type: mimeType });
                        } catch (error) {
                            console.error('创建文件失败:', error);
                            return null;
                        }
                    };

                    window.setFileToInput = function(input, file) {
                        try {
                            const dataTransfer = new DataTransfer();
                            dataTransfer.items.add(file);
                            input.files = dataTransfer.files;

                            const changeEvent = new Event('change', { bubbles: true });
                            input.dispatchEvent(changeEvent);

                            const inputEvent = new Event('input', { bubbles: true });
                            input.dispatchEvent(inputEvent);

                            return true;
                        } catch (error) {
                            console.error('设置文件失败:', error);
                            return false;
                        }
                    };
                }
            }
        """)

        print("✅ JavaScript辅助函数注入完成")

        # 步骤4: 依次注入首帧和尾帧
        print("💉 步骤4: 依次注入首帧和尾帧...")

        # 注入首帧
        print("� 4.1: 注入首帧...")
        first_inject_result = await inject_frame(page, first_frame_data, "首帧")

        if not first_inject_result:
            print("❌ 首帧注入失败")
            return False

        print("✅ 首帧注入成功")
        await asyncio.sleep(5)  # 等待首帧处理

        # 尝试激活尾帧区域
        print("🔧 4.2: 激活尾帧区域...")
        await page.evaluate("""
            () => {
                console.log('🔧 尝试激活尾帧区域');

                // 查找并点击尾帧相关元素
                const elements = document.querySelectorAll('*');
                for (const el of elements) {
                    const text = (el.textContent || '').trim();
                    if (text === '尾帧' && el.offsetParent && el.tagName !== 'INPUT') {
                        try {
                            el.click();
                            console.log('点击了尾帧按钮');
                            break;
                        } catch (e) {
                            console.error('点击尾帧按钮失败:', e);
                        }
                    }
                }
            }
        """)
        await asyncio.sleep(3)

        # 注入尾帧
        print("📸 4.3: 注入尾帧...")
        last_inject_result = await inject_frame(page, last_frame_data, "尾帧")

        if not last_inject_result:
            print("⚠️ 尾帧注入失败，但首帧已成功")
        else:
            print("✅ 尾帧注入成功")

        await asyncio.sleep(3)

        # 步骤5: 最终验证
        print("🔍 步骤5: 最终验证...")

        final_state = await page.evaluate("""
            () => {
                const result = {
                    fileInputs: document.querySelectorAll('input[type="file"]').length,
                    blobImages: document.querySelectorAll('img[src*="blob:"]').length,
                    totalImages: document.querySelectorAll('img').length
                };

                console.log('最终验证结果:', result);
                return result;
            }
        """)

        print(f"📊 最终验证结果:")
        print(f"   文件输入: {final_state['fileInputs']} 个")
        print(f"   总图片: {final_state['totalImages']} 个")
        print(f"   预览图片: {final_state['blobImages']} 个")

        # 判断成功条件
        if final_state['blobImages'] >= 2:
            print("🎉 首尾帧都注入成功！")
            success = True
        elif final_state['blobImages'] == 1:
            print("⚠️ 只有一帧注入成功")
            success = True  # 部分成功也算成功
        else:
            print("❌ 注入失败")
            success = False

        return success
        
    except Exception as e:
        print(f"❌ 智能注入失败: {e}")
        return False


async def main():
    """主函数"""
    
    print("🚀 启动智能首尾帧注入工具")
    print("💡 此工具会根据当前页面状态智能决定注入首帧还是尾帧")
    print()
    
    success = await smart_inject_frames()
    
    if success:
        print("\n🎉 智能注入成功！")
        print("💡 请在Chrome浏览器中查看即梦AI页面")
        print("🔄 如果只看到一张图片，请再次运行此脚本注入另一帧")
    else:
        print("\n❌ 智能注入失败")


if __name__ == "__main__":
    print("=" * 60)
    print("🧠 智能首尾帧注入工具")
    print("=" * 60)
    print("🎯 特点：根据页面状态自动判断应该注入首帧还是尾帧")
    print("🔄 使用：多次运行直到两帧都注入完成")
    print("=" * 60)
    print()
    
    asyncio.run(main())
