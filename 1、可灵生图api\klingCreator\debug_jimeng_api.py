#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试即梦AI API调用
详细查看请求和响应信息
"""

import requests
import json
import time
import hashlib
import uuid
import random

def debug_api_call():
    """调试API调用"""
    print("🔍 调试即梦AI API调用")
    print("=" * 50)
    
    # 基础参数
    session_id = "a7eb745aec44bb3186dbc2083ea9e1a6"
    base_url = "https://jimeng.jianying.com"
    
    # 常量
    DEFAULT_ASSISTANT_ID = "513695"
    VERSION_CODE = "5.8.0"
    PLATFORM_CODE = "7"
    WEB_ID = random.randint(7000000000000000000, 7999999999999999999)
    USER_ID = str(uuid.uuid4()).replace('-', '')
    
    # 生成Cookie
    timestamp = int(time.time())
    cookie = "; ".join([
        f"_tea_web_id={WEB_ID}",
        "is_staff_user=false",
        "store-region=cn-gd",
        "store-region-src=uid",
        f"sid_guard={session_id}%7C{timestamp}%7C5184000%7CMon%2C+03-Feb-2025+08%3A17%3A09+GMT",
        f"uid_tt={USER_ID}",
        f"uid_tt_ss={USER_ID}",
        f"sid_tt={session_id}",
        f"sessionid={session_id}",
        f"sessionid_ss={session_id}"
    ])
    
    # 生成签名
    uri = "/commerce/v1/benefits/user_credit"
    device_time = int(time.time())
    sign_string = f"9e2c|{uri[-7:]}|{PLATFORM_CODE}|{VERSION_CODE}|{device_time}||11ac"
    sign = hashlib.md5(sign_string.encode()).hexdigest()
    
    # 构建请求
    url = f"{base_url}{uri}"
    params = {
        'aid': DEFAULT_ASSISTANT_ID,
        'device_platform': 'web',
        'region': 'CN',
        'web_id': str(WEB_ID)
    }
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Encoding': 'gzip, deflate, br, zstd',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Cache-Control': 'no-cache',
        'Origin': 'https://jimeng.jianying.com',
        'Pragma': 'no-cache',
        'Referer': 'https://jimeng.jianying.com',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'Appid': DEFAULT_ASSISTANT_ID,
        'Appvr': VERSION_CODE,
        'Pf': PLATFORM_CODE,
        'Cookie': cookie,
        'Device-Time': str(device_time),
        'Sign': sign,
        'Sign-Ver': '1',
        'Content-Type': 'application/json'
    }
    
    print("📋 请求信息:")
    print(f"URL: {url}")
    print(f"Method: POST")
    print(f"Params: {params}")
    print(f"Headers (部分):")
    print(f"  Cookie: {cookie[:50]}...")
    print(f"  Sign: {sign}")
    print(f"  Device-Time: {device_time}")
    print()
    
    try:
        # 发送请求
        print("📤 发送请求...")
        session = requests.Session()
        response = session.post(
            url,
            params=params,
            headers=headers,
            json={},
            timeout=30
        )
        
        print(f"📥 响应信息:")
        print(f"Status Code: {response.status_code}")
        print(f"Headers: {dict(response.headers)}")
        print(f"Content Length: {len(response.content)}")
        print()
        
        if response.content:
            print("📄 响应内容:")
            try:
                # 尝试解析JSON响应
                result = response.json()
                print(json.dumps(result, indent=2, ensure_ascii=False))

                # 分析响应
                if 'ret' in result:
                    if result['ret'] == '0':
                        print("✅ API调用成功")
                    else:
                        print(f"❌ API返回错误: {result.get('errmsg', '未知错误')}")
                        print(f"错误代码: {result.get('ret')}")
                else:
                    print("⚠️ 响应格式异常")

            except json.JSONDecodeError:
                print("❌ 响应不是有效的JSON格式")
                print(f"响应编码: {response.encoding}")
                print(f"Content-Type: {response.headers.get('content-type')}")

                # 尝试不同的解码方式
                try:
                    # 尝试使用response.text (自动处理编码)
                    text_content = response.text
                    print(f"文本内容: {text_content[:500]}")

                    # 如果文本内容看起来像JSON，尝试解析
                    if text_content.strip().startswith('{'):
                        result = json.loads(text_content)
                        print("✅ 成功解析JSON:")
                        print(json.dumps(result, indent=2, ensure_ascii=False))

                except Exception as decode_error:
                    print(f"解码失败: {decode_error}")
                    print(f"原始字节: {response.content[:100]}")
        else:
            print("❌ 响应内容为空")
        
        return response.status_code == 200
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_simple_request():
    """测试简单请求"""
    print("\n🔍 测试简单请求")
    print("=" * 30)
    
    try:
        # 测试基础连接
        response = requests.get("https://jimeng.jianying.com", timeout=10)
        print(f"基础连接: {response.status_code}")
        
        # 测试API端点
        api_url = "https://jimeng.jianying.com/commerce/v1/benefits/user_credit"
        response = requests.post(api_url, json={}, timeout=10)
        print(f"API端点: {response.status_code}")
        print(f"响应长度: {len(response.content)}")
        
        if response.content:
            print(f"响应内容: {response.text[:200]}")
        
        return True
        
    except Exception as e:
        print(f"简单请求失败: {e}")
        return False


def main():
    """主函数"""
    print("🧪 即梦AI API调试工具")
    print("=" * 60)
    
    # 1. 测试简单请求
    simple_ok = test_simple_request()
    
    # 2. 测试完整API调用
    if simple_ok:
        print("\n" + "="*60)
        api_ok = debug_api_call()
        
        if api_ok:
            print("\n🎉 API调试完成!")
        else:
            print("\n❌ API调用失败")
    else:
        print("\n❌ 基础连接失败")
    
    print("\n💡 调试建议:")
    print("1. 检查网络连接")
    print("2. 确认session_id是否有效")
    print("3. 检查API端点是否正确")
    print("4. 验证请求参数和签名算法")


if __name__ == "__main__":
    main()
