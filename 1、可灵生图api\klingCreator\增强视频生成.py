#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强视频生成脚本 - 包含完整的验证和调试功能
这个脚本用于自动化即梦AI视频生成流程，包括：
1. 连接Chrome浏览器调试端口
2. 自动注入首尾帧图片
3. 监控视频生成进度
4. 自动下载生成的视频
"""

# 导入异步编程库，用于处理异步操作
import asyncio
# 导入命令行参数解析库
import argparse
# 导入base64编码库，用于图片数据编码
import base64
# 导入操作系统接口库
import os
# 导入系统相关参数和函数
import sys
# 导入子进程管理库，用于启动Chrome浏览器
import subprocess
# 导入socket网络编程库，用于检查端口状态
import socket
# 导入时间处理库
import time
# 导入HTTP请求库
import requests
# 导入路径处理库，用于文件路径操作
from pathlib import Path
# 导入Playwright浏览器自动化库
from playwright.async_api import async_playwright
# 导入日期时间处理库
from datetime import datetime

def log_network_request(url, status=None, method="GET", extra_info=""):
    """
    将网络请求记录到文件的函数
    参数:
        url: 请求的URL地址
        status: HTTP状态码（可选）
        method: HTTP请求方法，默认为GET
        extra_info: 额外的信息（可选）
    """
    try:
        # 获取日志文件路径，与当前脚本在同一目录下
        log_file = Path(__file__).parent / "网络请求.txt"
        # 获取当前时间戳，格式为：年-月-日 时:分:秒
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 以追加模式打开日志文件，使用UTF-8编码
        with open(log_file, 'a', encoding='utf-8') as f:
            # 如果有状态码，记录完整的请求信息
            if status:
                f.write(f"[{timestamp}] {method} {status} - {url}\n")
            else:
                # 如果没有状态码，只记录基本信息
                f.write(f"[{timestamp}] {method} - {url}\n")

            # 如果有额外信息，添加到日志中
            if extra_info:
                f.write(f"    {extra_info}\n")
            # 添加空行分隔不同的日志条目
            f.write("\n")

    except Exception as e:
        # 记录网络请求失败时不输出到终端，避免干扰主程序运行
        pass

class 视频监控下载器:
    """视频监控和下载器类 - 负责监控网络请求并下载生成的视频"""

    def __init__(self, 输出目录="video_output"):
        """
        初始化视频监控下载器
        参数:
            输出目录: 视频保存的目录路径
        """
        self.视频链接列表 = []  # 存储发现的视频下载链接
        self.正在监控 = False  # 监控状态标志
        self.输出目录 = Path(输出目录)
        self.输出目录.mkdir(exist_ok=True)  # 确保输出目录存在

    async def 监控网络获取视频(self, 页面对象, 超时时间=600):
        """
        监控网络请求，等待视频生成完成并获取下载链接
        参数:
            页面对象: Playwright页面对象
            超时时间: 监控超时时间（秒），默认600秒（10分钟）
        返回:
            布尔值: True表示找到视频，False表示超时未找到
        """
        log_network_request("VIDEO_MONITOR_START", None, "INFO", "开始监控视频生成...")

        找到视频 = False
        开始时间 = time.time()

        def 处理网络响应(response):
            """处理网络响应的回调函数"""
            nonlocal 找到视频
            网址 = response.url

            # 检查是否是视频文件 - 通过URL特征判断
            if any(keyword in 网址.lower() for keyword in ['vlabvod.com', '.mp4']) and 'video' in 网址:
                print(f"[VIDEO] 发现视频链接: {网址}")
                if 网址 not in self.视频链接列表:
                    self.视频链接列表.append(网址)
                    找到视频 = True

        # 设置网络响应监听器
        页面对象.on("response", 处理网络响应)

        # 循环等待视频生成完成
        while not 找到视频 and (time.time() - 开始时间) < 超时时间:
            await asyncio.sleep(2)  # 每2秒检查一次

            # 检查页面状态 - 查找完成、下载按钮等元素
            try:
                页面状态 = await 页面对象.evaluate("""
                    () => {
                        // 检查是否有完成状态的元素
                        const completedElements = document.querySelectorAll('[class*="completed"], [class*="success"], [class*="done"]');
                        const downloadButtons = document.querySelectorAll('button[class*="download"], a[class*="download"]');

                        return {
                            hasCompleted: completedElements.length > 0,
                            hasDownload: downloadButtons.length > 0,
                            pageTitle: document.title
                        };
                    }
                """)

                if 页面状态['hasDownload']:
                    log_network_request("DOWNLOAD_BUTTON_DETECTED", None, "INFO", "检测到下载按钮视频可能已生成完成")
                    break

            except Exception as e:
                log_network_request("PAGE_STATUS_CHECK_FAILED", None, "ERROR", f"页面状态检查失败: {e}")

        # 移除网络监听器
        页面对象.remove_listener("response", 处理网络响应)

        if 找到视频:
            log_network_request("VIDEO_MONITOR_COMPLETE", None, "INFO", f"监控完成发现 {len(self.视频链接列表)} 个视频链接")
            return True
        else:
            log_network_request("VIDEO_MONITOR_TIMEOUT", None, "WARNING", f"监控超时{超时时间}秒未发现视频链接")
            return False

    async def download_all_videos(self):
        """
        下载所有发现的视频文件
        返回: 下载成功的文件路径列表
        """
        # 初始化下载文件列表
        downloaded_files = []

        print(f"[下载] 开始下载 {len(self.视频链接列表)} 个视频文件")

        # 遍历所有发现的视频链接
        for i, video_url in enumerate(self.视频链接列表):
            try:
                print(f"[下载]  正在下载视频 {i+1}/{len(self.视频链接列表)}")
                # 显示URL（如果太长则截断）
                print(f"[下载]  网址: {video_url[:100]}{'...' if len(video_url) > 100 else ''}")

                # 生成带时间戳的文件名，避免重名
                timestamp = time.strftime("%Y%m%d_%H%M%S")
                filename = f"即梦视频_{i+1}_{timestamp}.mp4"
                file_path = self.输出目录 / filename

                print(f"[下载]  保存路径: {file_path}")

                # 发起HTTP请求下载视频
                print(f"[下载]  发起HTTP请求...")
                response = requests.get(video_url, stream=True, timeout=30)
                response.raise_for_status()  # 检查HTTP错误

                print(f"[下载]  HTTP响应成功: {response.status_code}")
                # 获取文件大小信息
                content_length = response.headers.get('content-length')
                if content_length:
                    total_size = int(content_length)
                    print(f"[下载]  文件大小: {total_size:,} 字节 ({total_size/1024/1024:.2f} MB)")

                # 初始化已下载字节数
                downloaded_size = 0
                print(f"[下载]  开始写入文件...")

                # 以二进制写入模式打开文件
                with open(file_path, 'wb') as f:
                    # 分块下载，每次8KB，避免内存占用过大
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:  # 过滤掉空的chunk
                            f.write(chunk)  # 写入文件
                            downloaded_size += len(chunk)  # 累计下载大小

                            # 每下载1MB输出一次进度，避免输出过于频繁
                            if downloaded_size % (1024 * 1024) == 0 or downloaded_size < 1024 * 1024:
                                if content_length:
                                    # 计算下载进度百分比
                                    progress = (downloaded_size / total_size) * 100
                                    print(f"[下载]  下载进度: {downloaded_size:,}/{total_size:,} 字节 ({progress:.1f}%)")
                                else:
                                    print(f"[下载]  已下载: {downloaded_size:,} 字节")

                # 获取最终文件大小并显示下载完成信息
                file_size = file_path.stat().st_size
                print(f"[下载]  下载完成: {file_path}")
                print(f"[下载]  最终文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
                # 将成功下载的文件路径添加到列表中
                downloaded_files.append(str(file_path))

            except Exception as e:
                # 捕获并显示下载过程中的任何错误
                print(f"[下载]  下载失败: {e}")
                print(f"[下载]  错误详情: {type(e).__name__}: {str(e)}")

        # 显示下载任务总结
        print(f"[下载]  下载任务完成，成功下载 {len(downloaded_files)} 个文件")
        return downloaded_files

def update_database_with_video(task_id, segment_id, video_path):
    """
    更新数据库中的视频信息
    参数:
        task_id: 任务ID
        segment_id: 片段ID
        video_path: 视频文件路径
    返回:
        布尔值: True表示更新成功，False表示更新失败
    """
    # 检查必要参数是否存在
    if not task_id or not segment_id:
        print("[数据库]   跳过数据库更新，缺少任务ID或片段ID")
        print(f"[数据库]  任务ID: {task_id}, 片段ID: {segment_id}")
        return False

    try:
        print("[数据库]  开始数据库更新操作...")
        print(f"[数据库]  任务ID: {task_id}")
        print(f"[数据库]  片段ID: {segment_id}")
        print(f"[数据库]  视频路径: {video_path}")

        # 导入数据库模型
        print("[数据库]  导入数据库模型...")
        from database_models import video_task_model, video_segment_model

        # 更新任务状态
        print("[DB]  更新任务状态为 'completed'...")
        video_task_model.update_task_status(task_id, 'completed', str(video_path))
        print("[DB]  任务状态更新成功")

        # 更新片段状态
        print("[DB]  更新片段状态为 'completed'...")
        video_segment_model.update_segment_status(segment_id, 'completed', str(video_path))
        print("[DB]  片段状态更新成功")

        print("[DB]  数据库更新完成")
        return True

    except Exception as e:
        # 避免在打印中使用可能导致编码错误的字符
        error_msg = str(e).encode('utf-8', errors='ignore').decode('utf-8')
        print(f"[DB]  数据库更新失败: {error_msg}")
        print(f"[DB]  错误类型: {type(e).__name__}")
        return False

def read_image_as_base64(image_path):
    """读取图片并转换为base64"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 获取MIME类型
        if image_path.lower().endswith('.png'):
            mime_type = 'image/png'
        elif image_path.lower().endswith(('.jpg', '.jpeg')):
            mime_type = 'image/jpeg'
        elif image_path.lower().endswith('.webp'):
            mime_type = 'image/webp'
        else:
            mime_type = 'image/jpeg'  # 默认
        
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        return {
            "data": base64_data,
            "type": mime_type,
            "size": len(image_data),
            "name": Path(image_path).name
        }
    except Exception as e:
        print(f"[ERROR] 读取图片失败: {e}")
        return None

async def check_and_open_browser():
    """检查并打开浏览器"""
    debug_port = 9222
    
    # 检查Chrome调试端口
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("[ERROR] Chrome调试端口未开启正在尝试打开浏览器...")
        try:
            if sys.platform == "win32":
                subprocess.Popen([
                    "chrome.exe", 
                    "--remote-debugging-port=9222",
                    "--user-data-dir=chrome-debug",
                    "https://jimeng.jianying.com/ai-tool/generate?type=video"
                ])
            else:
                subprocess.Popen([
                    "google-chrome", 
                    "--remote-debugging-port=9222",
                    "--user-data-dir=chrome-debug",
                    "https://jimeng.jianying.com/ai-tool/generate?type=video"
                ])
            
            print("[WAIT] 等待浏览器启动...")
            await asyncio.sleep(5)
            
            # 再次检查端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', debug_port))
            sock.close()
            
            if result != 0:
                print("[ERROR] 浏览器启动失败")
                print("请手动打开Chrome并访问: https://jimeng.jianying.com/ai-tool/generate?type=video")
                print("启动命令: chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug")
                return False
                
        except Exception as e:
            print(f"[ERROR] 启动浏览器失败: {e}")
            return False
    
    print("[OK] Chrome调试端口已开启")
    return True

async def find_or_navigate_to_video_page(browser):
    """查找或导航到视频生成页面"""
    contexts = browser.contexts
    
    jimeng_page = None
    for context in contexts:
        for page in context.pages:
            if 'jimeng.jianying.com' in page.url:
                jimeng_page = page
                break
        if jimeng_page:
            break
    
    if not jimeng_page:
        print("[ERROR] 未找到即梦AI页面正在跳转...")
        # 尝试在现有标签页中跳转
        if contexts and contexts[0].pages:
            page = contexts[0].pages[0]
            await page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
            await asyncio.sleep(3)
            jimeng_page = page
        else:
            print("[ERROR] 无法找到可用的浏览器页面")
            return None
    
    print(f"[OK] 找到即梦AI页面: {jimeng_page.url}")
    
    # 验证页面是否为视频生成页面
    if 'type=video' not in jimeng_page.url:
        print("[WARN] 当前页面不是视频生成页面正在跳转...")
        await jimeng_page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
        await asyncio.sleep(3)
    
    return jimeng_page

async def clear_existing_frames(page):
    """清空已存在的首尾帧"""
    print("[CLEAR] 开始清空已存在的首尾帧...")
    
    try:
        # 智能清空 - 只清空编辑区域的帧，保留历史记录
        cleared_count = await page.evaluate("""
            () => {
                let cleared = 0;
                const frames = document.querySelectorAll('.reference-mgqKPd, .reference-image-X6b5b7');
                
                frames.forEach(frame => {
                    // 检查是否在历史记录区域
                    const historyArea = document.querySelector('.history-list, .record-list, .generated-list, .record-reference-ymEKWc');
                    if (historyArea && historyArea.contains(frame)) {
                        return; // 跳过历史记录中的帧
                    }
                    
                    // 尝试点击删除按钮
                    const removeButton = frame.querySelector('.remove-button-CGHPzk, [class*="remove-button"]');
                    if (removeButton) {
                        removeButton.click();
                        cleared++;
                    } else {
                        // 直接移除DOM元素
                        frame.remove();
                        cleared++;
                    }
                });
                
                return cleared;
            }
        """)
        
        if cleared_count > 0:
            print(f"[CLEAR] 成功清空 {cleared_count} 个帧图片")
            await asyncio.sleep(2)  # 等待清空完成
        else:
            print("[CLEAR] 没有发现需要清空的帧图片")
        
        return True
        
    except Exception as e:
        print(f"[CLEAR] 清空帧图片时出错: {e}")
        return False

async def smart_clear_frames(page):
    """智能清空首尾帧 - 根据实际页面结构动态适应"""
    print("[SMART_CLEAR] 开始智能清空首尾帧...")

    try:
        # 步骤1: 分析页面结构，识别不同类型的帧图片
        frame_analysis = await page.evaluate("""
            () => {
                const result = {
                    editAreaFrames: [],
                    historyFrames: [],
                    uploadAreaFrames: [],
                    allFrameSelectors: []
                };

                // 定义可能的帧图片选择器
                const frameSelectors = [
                    '.reference-mgqKPd',
                    '.reference-image-X6b5b7',
                    '[class*="reference"]',
                    '[class*="frame-image"]',
                    '[class*="preview-image"]'
                ];

                // 定义区域选择器
                const editAreaSelectors = ['.main-content-dP2xNO', '.edit-area', '.upload-area', '.prompt-container-rcKZJC'];
                const historyAreaSelectors = ['.history-list', '.record-list', '.generated-list', '.record-reference-ymEKWc'];
                const uploadAreaSelectors = ['.upload-container', '.file-upload', '[class*="upload"]'];

                // 查找各个区域
                const editAreas = editAreaSelectors.map(sel => document.querySelector(sel)).filter(el => el);
                const historyAreas = historyAreaSelectors.map(sel => document.querySelector(sel)).filter(el => el);
                const uploadAreas = uploadAreaSelectors.map(sel => document.querySelector(sel)).filter(el => el);

                // 分析每个帧图片的位置
                frameSelectors.forEach(selector => {
                    const frames = document.querySelectorAll(selector);
                    if (frames.length > 0) {
                        result.allFrameSelectors.push({selector, count: frames.length});

                        frames.forEach((frame, index) => {
                            const frameInfo = {
                                element: frame,
                                selector: selector,
                                index: index,
                                hasRemoveButton: !!frame.querySelector('.remove-button-CGHPzk, [class*="remove-button"], [class*="delete-button"]'),
                                isVisible: frame.offsetParent !== null,
                                rect: frame.getBoundingClientRect()
                            };

                            // 判断帧图片所在的区域
                            let inEditArea = false;
                            let inHistoryArea = false;
                            let inUploadArea = false;

                            editAreas.forEach(area => {
                                if (area && area.contains(frame)) inEditArea = true;
                            });

                            historyAreas.forEach(area => {
                                if (area && area.contains(frame)) inHistoryArea = true;
                            });

                            uploadAreas.forEach(area => {
                                if (area && area.contains(frame)) inUploadArea = true;
                            });

                            if (inHistoryArea) {
                                result.historyFrames.push(frameInfo);
                            } else if (inUploadArea) {
                                result.uploadAreaFrames.push(frameInfo);
                            } else if (inEditArea || (!inHistoryArea && !inUploadArea)) {
                                result.editAreaFrames.push(frameInfo);
                            }
                        });
                    }
                });

                return result;
            }
        """)

        print(f"[SMART_CLEAR] 页面帧图片分析结果:")
        print(f"   编辑区域帧数: {len(frame_analysis['editAreaFrames'])}")
        print(f"   历史区域帧数: {len(frame_analysis['historyFrames'])}")
        print(f"   上传区域帧数: {len(frame_analysis['uploadAreaFrames'])}")
        print(f"   发现的选择器: {[s['selector'] + '(' + str(s['count']) + ')' for s in frame_analysis['allFrameSelectors']]}")

        # 步骤2: 只清空编辑区域和上传区域的帧图片
        frames_to_clear = frame_analysis['editAreaFrames'] + frame_analysis['uploadAreaFrames']

        if len(frames_to_clear) == 0:
            print("[SMART_CLEAR] 没有发现需要清空的帧图片")
            return True

        print(f"[SMART_CLEAR] 准备清空 {len(frames_to_clear)} 个帧图片")

        cleared_count = 0
        for i, frame_info in enumerate(frames_to_clear):
            try:
                print(f"[SMART_CLEAR] 处理第 {i+1}/{len(frames_to_clear)} 个帧图片 (选择器: {frame_info['selector']})")

                # 获取元素引用
                frame_element = await page.evaluate_handle("""
                    (frameInfo) => {
                        const frames = document.querySelectorAll(frameInfo.selector);
                        return frames[frameInfo.index];
                    }
                """, frame_info)

                if not frame_element:
                    print(f"[SMART_CLEAR] 第 {i+1} 个帧图片元素已不存在，跳过")
                    continue

                # 方法1: 悬停显示删除按钮
                await frame_element.hover()
                await page.wait_for_timeout(1000)  # 等待悬停效果

                # 查找删除按钮
                remove_button = await frame_element.query_selector('.remove-button-CGHPzk')
                if not remove_button:
                    # 尝试其他删除按钮选择器
                    remove_selectors = [
                        '.remove-button-container-x2kHww .remove-button-CGHPzk',
                        '[class*="remove-button"]',
                        '[class*="delete-button"]',
                        '[class*="close-button"]',
                        'svg[width="8"][height="8"]'
                    ]

                    for selector in remove_selectors:
                        remove_button = await frame_element.query_selector(selector)
                        if remove_button:
                            print(f"[SMART_CLEAR] 使用选择器 {selector} 找到删除按钮")
                            break

                if remove_button:
                    print(f"[SMART_CLEAR] 点击删除按钮删除第 {i+1} 个帧图片")
                    await remove_button.click()
                    await page.wait_for_timeout(1500)  # 等待删除动画完成
                    cleared_count += 1
                    print(f"[SMART_CLEAR] 第 {i+1} 个帧图片已删除")
                else:
                    print(f"[SMART_CLEAR] 第 {i+1} 个帧图片未找到删除按钮，尝试直接移除")

                    # 直接从DOM中移除
                    removed = await page.evaluate("""
                        (element) => {
                            if (element && element.parentNode) {
                                element.parentNode.removeChild(element);
                                return true;
                            }
                            return false;
                        }
                    """, frame_element)

                    if removed:
                        cleared_count += 1
                        print(f"[SMART_CLEAR] 第 {i+1} 个帧图片已通过DOM移除")
                    else:
                        print(f"[SMART_CLEAR] 第 {i+1} 个帧图片移除失败")

            except Exception as e:
                print(f"[SMART_CLEAR] 处理第 {i+1} 个帧图片时出错: {e}")
                continue

        print(f"[SMART_CLEAR] 智能清空完成，成功清除 {cleared_count}/{len(frames_to_clear)} 个帧图片")
        return cleared_count > 0

    except Exception as e:
        print(f"[SMART_CLEAR] 智能清空过程出错: {e}")
        return False

async def verify_upload_areas(page):
    """验证上传区域"""
    print("[CHECK] 验证上传区域...")

    # 检查页面元素
    areas_info = await page.evaluate("""
        () => {
            const result = {
                fileInputs: [],
                uploadAreas: [],
                firstFrameArea: null,
                lastFrameArea: null
            };
            
            // 查找所有文件输入
            const inputs = document.querySelectorAll('input[type="file"]');
            inputs.forEach((input, index) => {
                const rect = input.getBoundingClientRect();
                result.fileInputs.push({
                    index: index,
                    visible: input.style.display !== 'none',
                    rect: {
                        x: rect.x,
                        y: rect.y,
                        width: rect.width,
                        height: rect.height
                    },
                    parent: input.parentElement?.className || 'unknown'
                });
            });
            
            // 查找上传区域
            const uploadDivs = document.querySelectorAll('[class*="upload"], [class*="Upload"]');
            uploadDivs.forEach((div, index) => {
                result.uploadAreas.push({
                    index: index,
                    className: div.className,
                    text: div.textContent?.substring(0, 50) || ''
                });
            });
            
            // 查找首帧和尾帧相关元素
            const firstFrameElements = document.querySelectorAll('[class*="first"], [class*="First"], [class*="首帧"]');
            const lastFrameElements = document.querySelectorAll('[class*="last"], [class*="Last"], [class*="尾帧"]');
            
            if (firstFrameElements.length > 0) {
                result.firstFrameArea = firstFrameElements[0].className;
            }
            
            if (lastFrameElements.length > 0) {
                result.lastFrameArea = lastFrameElements[0].className;
            }
            
            return result;
        }
    """)
    
    print(f"[DATA] 页面分析结果:")
    print(f"  文件输入数量: {len(areas_info['fileInputs'])}")
    print(f"  上传区域数量: {len(areas_info['uploadAreas'])}")
    print(f"  首帧区域: {areas_info['firstFrameArea']}")
    print(f"  尾帧区域: {areas_info['lastFrameArea']}")
    
    for i, input_info in enumerate(areas_info['fileInputs']):
        print(f"  输入{i}: 可见={input_info['visible']}, 父元素={input_info['parent']}")
    
    return areas_info

def safe_print(text):
    """安全打印函数避免Unicode编码错误"""
    try:
        print(text)
    except UnicodeEncodeError:
        # 移除emoji字符只保留基本文本
        import re
        clean_text = re.sub(r'[^\x00-\x7F]+', '', text)
        print(clean_text)

# 设置控制台编码
import sys
import os
if sys.platform == "win32":
    os.system("chcp 65001 > nul")

async def enhanced_video_generation(first_frame_path, last_frame_path, output_path, debug=False, task_id=None, segment_id=None):
    """增强的视频生成函数"""

    print("=" * 80)
    print("                    增强视频生成系统启动                    ")
    print("=" * 80)
    print(f"[图片] 首帧图片: {first_frame_path}")
    print(f"[图片] 尾帧图片: {last_frame_path}")
    print(f"[输出] 输出路径: {output_path}")
    print(f"[调试] 调试模式: {'启用' if debug else '禁用'}")
    print(f"[任务] 任务ID: {task_id if task_id else '未指定'}")
    print(f"[片段] 片段ID: {segment_id if segment_id else '未指定'}")
    print("=" * 80)
    print("")

    # 记录脚本启动信息到网络请求日志
    log_network_request("SCRIPT_START", None, "INFO", f"视频生成脚本启动 - 任务ID: {task_id}, 片段ID: {segment_id}")

    # 初始化视频监控器
    output_dir = Path(output_path).parent if isinstance(output_path, Path) else Path(output_path).parent
    video_monitor = 视频监控下载器(output_dir)
    log_network_request("VIDEO_MONITOR_INIT", None, "INFO", f"视频监控器已初始化，输出目录: {output_dir}")
    print("")

    # 1. 验证文件存在
    print("[步骤] 步骤1: 验证文件...")

    # 规范化路径格式，处理不同操作系统的路径分隔符
    first_frame_path = os.path.normpath(first_frame_path)
    last_frame_path = os.path.normpath(last_frame_path)

    print(f"[调试] 首帧路径: {first_frame_path}")
    print(f"[调试] 尾帧路径: {last_frame_path}")

    # 检查首帧文件是否存在
    if not os.path.exists(first_frame_path):
        print(f"[错误] 首帧文件不存在: {first_frame_path}")
        return False

    # 检查尾帧文件是否存在
    if not os.path.exists(last_frame_path):
        print(f"[错误] 尾帧文件不存在: {last_frame_path}")
        return False

    print(f"[成功] 文件验证通过")

    # 2. 读取图片数据
    print("[步骤] 步骤2: 读取图片数据...")
    first_frame_data = read_image_as_base64(first_frame_path)
    last_frame_data = read_image_as_base64(last_frame_path)

    # 检查图片数据是否读取成功
    if not first_frame_data or not last_frame_data:
        print("[错误] 图片数据读取失败")
        return False

    print(f"[成功] 首帧: {first_frame_data['size']} 字节, {first_frame_data['type']}")
    print(f"[成功] 尾帧: {last_frame_data['size']} 字节, {last_frame_data['type']}")

    # 3. 检查并打开浏览器
    print("[步骤] 步骤3: 检查浏览器...")
    if not await check_and_open_browser():
        return False

    # 4. 连接到浏览器并执行注入
    print("[步骤] 步骤4: 连接浏览器...")
    async with async_playwright() as p:
        try:
            # 通过CDP协议连接到Chrome调试端口
            browser = await p.chromium.connect_over_cdp("http://localhost:9222")

            # 5. 查找或导航到视频页面
            print("[步骤..] 步骤5: 查找视频页面...")
            page = await find_or_navigate_to_video_page(browser)
            if not page:
                return FalseS

            # 6. 立即开始网络监控
            # print("[步骤] 步骤6: 开始网络监控...")
            # log_network_request("NETWORK_LISTENER_SETUP", None, "INFO", "正在设置网络请求监听器...")

            #     """处理网络响应的回调函数"""
            #     url = response.url
            #     status = response.status

            #     # 记录所有网络请求到文件（不在控制台显示）
            #     log_network_request(url, status, "GET")

            #     # 只在控制台记录关键网络请求
            #     if any(keyword in url.lower() for keyword in ['jimeng', 'jianying', 'vlabvod', 'video', 'api', 'generate']):
            #         url_display = url[:80] + '...' if len(url) > 80 else url
            #         print(f"[网络] {status} - {url_display}")

            #     # 检查是否是视频文件
            #     if any(keyword in url.lower() for keyword in ['vlabvod.com', '.mp4']) and 'video' in url:
            #         print(f"[视频] 发现视频链接: {url}")
            #         # 记录视频链接到文件
            #         log_network_request(url, status, "GET", "*** 视频文件链接 ***")

            #         # 添加到视频监控器的链接列表中
            #         if url not in video_monitor.视频链接列表:
            #             video_monitor.视频链接列表.append(url)
            #             log_network_request("VIDEO_LINK_ADDED", None, "INFO", f"视频链接已添加到监控列表 (总数: {len(video_monitor.视频链接列表)})")

            # # 设置网络响应监听器
            # page.on("response", handle_response)
            # log_network_request("NETWORK_MONITOR_STARTED", None, "INFO", "网络监控已启动开始监听所有网络请求")

            # 7. 智能清空已存在的首尾帧
            print("步骤7-3: 清空首尾帧..")
            input("按回车键继续...")
            clear_success = await clear_existing_frames(page)
            if clear_success:
                print("[OK] 首尾帧清空完成")
            else:
                print("[WARN] 首尾帧清空失败，继续注入新图片")

            # 如果是调试模式，等待用户确认
            if debug:
                print("[调试] 调试模式等待用户确认...")
                input("按回车键继续...")
         
            await asyncio.sleep(200)
            # 9. 执行增强的图片注入（包含预清空检查）
            print("[步骤] 步骤9: 执行增强的图片注入...")
            success = await perform_enhanced_injection_with_clear(page, first_frame_data, last_frame_data, debug)

            if success:
                print("[成功] 图片注入完成，开始等待视频生成...")

                # 10. 等待视频生成完成并下载
                print("[步骤] 步骤10: 等待视频生成完成...")
                log_network_request("VIDEO_GENERATION_MONITOR_START", None, "INFO", "开始监控视频生成进度，监控超时设置: 600秒 (10分钟)")

                # 等待视频生成完成，最多10分钟
                start_time = time.time()
                timeout = 600  # 10分钟超时
                check_count = 0

                # 循环监控直到找到视频链接或超时
                while len(video_monitor.视频链接列表) == 0 and (time.time() - start_time) < timeout:
                    check_count += 1
                    elapsed_time = int(time.time() - start_time)
                    remaining_time = timeout - elapsed_time

                    # 每30秒输出一次监控进度
                    if check_count % 15 == 1:
                        log_network_request("MONITOR_PROGRESS", None, "INFO", f"监控进度: {elapsed_time}s / {timeout}s (剩余: {remaining_time}s), 已检查: {check_count} 次发现视频链接: {len(video_monitor.视频链接列表)} 个")

                    # 每2秒检查一次
                    await asyncio.sleep(2)

                    # 检查页面状态
                    try:
                        page_status = await page.evaluate("""
                            () => {
                                // 检查是否有完成状态的元素
                                const completedElements = document.querySelectorAll('[class*="completed"], [class*="success"], [class*="done"]');
                                const downloadButtons = document.querySelectorAll('button[class*="download"], a[class*="download"]');
                                const progressElements = document.querySelectorAll('[class*="progress"], [class*="loading"]');

                                return {
                                    hasCompleted: completedElements.length > 0,
                                    hasDownload: downloadButtons.length > 0,
                                    hasProgress: progressElements.length > 0,
                                    pageTitle: document.title,
                                    url: window.location.href
                                };
                            }
                        """)

                        if check_count % 15 == 1:  # 每30秒输出一次页面状态
                            log_network_request("PAGE_STATUS", None, "INFO", f"页面状态: 完成元素={page_status['hasCompleted']}, 下载按钮={page_status['hasDownload']}, 进度元素={page_status['hasProgress']}")

                        if page_status['hasDownload']:
                            log_network_request("DOWNLOAD_BUTTON_DETECTED", None, "INFO", "检测到下载按钮视频可能已生成完成")
                            break

                    except Exception as e:
                        log_network_request("PAGE_STATUS_CHECK_ERROR", None, "ERROR", f"页面状态检查失败: {e}")

                elapsed_total = int(time.time() - start_time)
                log_network_request("MONITOR_END", None, "INFO", f"监控结束: 总耗时 {elapsed_total}s, 检查次数 {check_count}, 发现视频 {len(video_monitor.video_links)} 个")

                # 10. 下载视频
                if len(video_monitor.video_links) > 0:
                    print("步骤10: 下载生成的视频...")
                    print(f"[DOWNLOAD]  准备下载 {len(video_monitor.video_links)} 个视频文件")
                    for i, link in enumerate(video_monitor.video_links):
                        print(f"[DOWNLOAD]  视频链接 {i+1}: {link[:100]}{'...' if len(link) > 100 else ''}")

                    downloaded_files = await video_monitor.download_all_videos()

                    if downloaded_files:
                        # 使用第一个下载的文件作为主要输出
                        downloaded_video_path = downloaded_files[0]
                        file_size = Path(downloaded_video_path).stat().st_size
                        print(f"[DOWNLOAD]  视频下载完成: {downloaded_video_path}")
                        print(f"[DOWNLOAD]  文件大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")

                        # 11. 重命名为期望的输出文件名
                        print("步骤11: 重命名视频文件...")
                        expected_output_path = Path(output_path)
                        print(f"[RENAME]  源文件: {downloaded_video_path}")
                        print(f"[RENAME]  目标文件: {expected_output_path}")

                        try:
                            # 确保输出目录存在
                            expected_output_path.parent.mkdir(parents=True, exist_ok=True)
                            print(f"[RENAME]  输出目录已确保存在: {expected_output_path.parent}")

                            # 移动并重命名文件
                            import shutil
                            shutil.move(str(downloaded_video_path), str(expected_output_path))
                            final_size = expected_output_path.stat().st_size
                            print(f"[RENAME]  视频文件已重命名: {expected_output_path}")
                            print(f"[RENAME]  最终文件大小: {final_size:,} 字节")

                            # 12. 更新数据库
                            if task_id or segment_id:
                                print("步骤12: 更新数据库...")
                                print(f"[DATABASE]  任务ID: {task_id}, 片段ID: {segment_id}")
                                print(f"[DATABASE]  视频路径: {expected_output_path}")

                                db_success = update_database_with_video(task_id, segment_id, str(expected_output_path))
                                if db_success:
                                    print("[DATABASE]  数据库更新成功")
                                else:
                                    print("[DATABASE]  数据库更新失败")
                            else:
                                print("[DATABASE]   跳过数据库更新缺少任务ID或片段ID")

                            print("[VIDEO]  完整流程完成")
                            print("=" * 80)
                            return True

                        except Exception as e:
                            print(f"[RENAME]  文件重命名失败: {e}")
                            return False
                    else:
                        print("[DOWNLOAD]  视频下载失败")
                        return False
                else:
                    log_network_request("MONITOR_TIMEOUT", None, "WARNING", f"视频监控超时未检测到视频生成完成，最终统计: 监控时长 {elapsed_total}s, 发现视频 {len(video_monitor.video_links)} 个")
                    return False
            else:
                print("[ERROR] 视频生成流程失败")
                return False
                
        except Exception as e:
            print(f"[ERROR] 浏览器连接失败: {e}")
            return False

async def perform_enhanced_injection_with_clear(page, first_frame_data, last_frame_data, debug=False):
    """
    执行增强的图片注入，包含预清空检查 - 完全使用手动注入.py的成功逻辑
    参数:
        page: Playwright页面对象
        first_frame_data: 首帧图片数据
        last_frame_data: 尾帧图片数据
        debug: 调试模式标志（未使用）
    返回:
        布尔值: True表示注入成功，False表示注入失败
    """
    try:
        print("[注入] 开始增强注入流程（使用手动注入.py的成功逻辑）...")

        # 步骤1: 注入新的首尾帧图片 - 完全使用手动注入.py的inject_images逻辑
        print("[注入] 步骤2: 注入新的首尾帧图片...")
        return await inject_images_simple(page, first_frame_data, last_frame_data)

    except Exception as e:
        print(f"[注入] 增强注入过程出错: {e}")
        return False

async def inject_images_simple(page, first_frame_data, last_frame_data):
    """
    注入首尾帧图片 - 完全复制手动注入.py的inject_images逻辑
    参数:
        page: Playwright页面对象
        first_frame_data: 首帧图片数据字典
        last_frame_data: 尾帧图片数据字典
    返回:
        布尔值: True表示注入成功，False表示注入失败
    """
    print("[注入] 开始注入首尾帧图片...")

    try:
        # 注入JavaScript辅助函数到页面中
        await page.evaluate("""
            () => {
                // 创建文件对象的辅助函数
                window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                    try {
                        // 将base64数据转换为字节数组
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        // 创建File对象
                        return new File([byteArray], fileName, { type: mimeType });
                    } catch (error) {
                        console.error('创建文件失败:', error);
                        return null;
                    }
                };

                // 设置文件到输入框的辅助函数
                window.setFileToInput = function(input, file) {
                    try {
                        // 使用DataTransfer API设置文件
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        input.files = dataTransfer.files;

                        // 触发change事件
                        const changeEvent = new Event('change', { bubbles: true });
                        input.dispatchEvent(changeEvent);

                        // 触发input事件
                        const inputEvent = new Event('input', { bubbles: true });
                        input.dispatchEvent(inputEvent);

                        return true;
                    } catch (error) {
                        console.error('设置文件失败:', error);
                        return false;
                    }
                };
            }
        """)

        # 注入首帧图片
        print("[注入] 注入首帧...")
        first_result = await page.evaluate(f"""
            () => {{
                try {{
                    // 查找所有文件输入框
                    const inputs = document.querySelectorAll('input[type="file"]');
                    if (inputs.length === 0) {{
                        console.error('未找到文件输入');
                        return false;
                    }}

                    // 使用第一个文件输入框
                    const input = inputs[0];
                    // 确保输入框可见
                    input.style.display = 'block !important';
                    input.style.visibility = 'visible !important';
                    input.style.opacity = '1 !important';

                    // 创建文件对象
                    const file = window.createFileFromBase64(
                        '{first_frame_data["data"]}',
                        '{first_frame_data["name"]}',
                        '{first_frame_data["type"]}'
                    );

                    if (!file) return false;
                    // 设置文件到输入框
                    return window.setFileToInput(input, file);

                }} catch (error) {{
                    console.error('首帧注入失败:', error);
                    return false;
                }}
            }}
        """)

        if first_result:
            print("[成功] 首帧注入成功")
        else:
            print("[错误] 首帧注入失败")
            return False

        # 等待1秒让首帧处理完成
        await asyncio.sleep(1)

        # 注入尾帧图片
        print("[注入] 注入尾帧...")
        last_result = await page.evaluate(f"""
            () => {{
                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');

                    // 尝试所有文件输入框
                    for (let i = 0; i < inputs.length; i++) {{
                        const input = inputs[i];
                        // 确保输入框可见
                        input.style.display = 'block !important';
                        input.style.visibility = 'visible !important';
                        input.style.opacity = '1 !important';

                        // 创建文件对象
                        const file = window.createFileFromBase64(
                            '{last_frame_data["data"]}',
                            '{last_frame_data["name"]}',
                            '{last_frame_data["type"]}'
                        );

                        // 尝试设置文件到当前输入框
                        if (file && window.setFileToInput(input, file)) {{
                            console.log(`尾帧注入成功到输入 ${{i}}`);
                            return true;
                        }}
                    }}
                    return false;

                }} catch (error) {{
                    console.error('尾帧注入失败:', error);
                    return false;
                }}
            }}
        """)

        if last_result:
            print("[成功] 尾帧注入成功")
        else:
            print("[警告] 尾帧注入失败，但首帧已成功")

        # 等待2秒让注入完成
        await asyncio.sleep(2)
        return True

    except Exception as e:
        print(f"[注入] 图片注入过程出错: {e}")
        return False

async def perform_enhanced_injection(page, first_frame_data, last_frame_data, debug=False):
    """执行增强的图片注入 - 完全使用手动注入.py的成功逻辑"""
    print("[INJECT] 开始注入首尾帧图片...")

    try:
        # 注入JavaScript辅助函数 - 完全复制手动注入.py的逻辑
        await page.evaluate("""
            () => {
                window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                    try {
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        return new File([byteArray], fileName, { type: mimeType });
                    } catch (error) {
                        console.error('创建文件失败:', error);
                        return null;
                    }
                };

                window.setFileToInput = function(input, file) {
                    try {
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        input.files = dataTransfer.files;

                        const changeEvent = new Event('change', { bubbles: true });
                        input.dispatchEvent(changeEvent);

                        const inputEvent = new Event('input', { bubbles: true });
                        input.dispatchEvent(inputEvent);

                        return true;
                    } catch (error) {
                        console.error('设置文件失败:', error);
                        return false;
                    }
                };
            }
        """)

        # 注入首帧 - 完全复制手动注入.py的逻辑
        print("[INJECT] 注入首帧...")
        first_result = await page.evaluate(f"""
            () => {{
                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');
                    if (inputs.length === 0) {{
                        console.error('未找到文件输入');
                        return false;
                    }}

                    const input = inputs[0];
                    input.style.display = 'block !important';
                    input.style.visibility = 'visible !important';
                    input.style.opacity = '1 !important';

                    const file = window.createFileFromBase64(
                        '{first_frame_data["data"]}',
                        '{first_frame_data["name"]}',
                        '{first_frame_data["type"]}'
                    );

                    if (!file) return false;
                    return window.setFileToInput(input, file);

                }} catch (error) {{
                    console.error('首帧注入失败:', error);
                    return false;
                }}
            }}
        """)

        if first_result:
            print("[OK] 首帧注入成功")
        else:
            print("[ERROR] 首帧注入失败")
            return False

        await asyncio.sleep(1)

        # 注入尾帧 - 完全复制手动注入.py的逻辑
        print("[INJECT] 注入尾帧...")
        last_result = await page.evaluate(f"""
            () => {{
                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');

                    // 尝试所有文件输入
                    for (let i = 0; i < inputs.length; i++) {{
                        const input = inputs[i];
                        input.style.display = 'block !important';
                        input.style.visibility = 'visible !important';
                        input.style.opacity = '1 !important';

                        const file = window.createFileFromBase64(
                            '{last_frame_data["data"]}',
                            '{last_frame_data["name"]}',
                            '{last_frame_data["type"]}'
                        );

                        if (file && window.setFileToInput(input, file)) {{
                            console.log(`尾帧注入成功到输入 ${{i}}`);
                            return true;
                        }}
                    }}
                    return false;

                }} catch (error) {{
                    console.error('尾帧注入失败:', error);
                    return false;
                }}
            }}
        """)

        if last_result:
            print("[OK] 尾帧注入成功")
        else:
            print("[WARN] 尾帧注入失败，但首帧已成功")

        await asyncio.sleep(2)

        # 步骤3: 注入提示词 - 使用手动注入.py的简洁逻辑
        print("[PROMPT] 注入提示词...")
        prompt_text = "固定镜头，完美过渡效果，电影感调色，炫酷过渡"

        prompt_result = await page.evaluate(f"""
            async () => {{
                try {{
                    const promptText = '{prompt_text}';

                    // 确保输入框处于展开状态
                    const mainContent = document.querySelector('.main-content-dP2xNO');
                    const isCollapsed = mainContent && mainContent.classList.contains('collapsed-BEoOVP');

                    if (isCollapsed) {{
                        const promptContainer = document.querySelector('.prompt-container-rcKZJC');
                        if (promptContainer) {{
                            promptContainer.click();
                            await new Promise(resolve => setTimeout(resolve, 300));
                        }}
                    }}

                    // 找到textarea
                    const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
                    if (textarea) {{
                        // 确保textarea可见和可编辑
                        textarea.style.display = 'block';
                        textarea.style.visibility = 'visible';
                        textarea.style.height = '88px';

                        // 聚焦并清空
                        textarea.click();
                        await new Promise(resolve => setTimeout(resolve, 200));
                        textarea.focus();
                        await new Promise(resolve => setTimeout(resolve, 200));

                        // 清空现有内容
                        textarea.select();
                        document.execCommand('selectAll');
                        document.execCommand('delete');

                        // 插入新文本
                        textarea.focus();
                        document.execCommand('insertText', false, promptText);

                        // 触发事件
                        const inputEvent = new Event('input', {{ bubbles: true }});
                        textarea.dispatchEvent(inputEvent);

                        const changeEvent = new Event('change', {{ bubbles: true }});
                        textarea.dispatchEvent(changeEvent);

                        return true;
                    }}

                    return false;

                }} catch (error) {{
                    console.error('提示词注入失败:', error);
                    return false;
                }}
            }}
        """)

        if prompt_result:
            print("[OK] 提示词注入成功")
        else:
            print("[ERROR] 提示词注入失败")

        await asyncio.sleep(1)

        return True

    except Exception as e:
        print(f"[INJECT] 图片注入过程出错: {e}")
        return False

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='增强视频生成脚本')
    parser.add_argument('--first-image', required=True, help='首帧图片路径')
    parser.add_argument('--last-image', required=True, help='尾帧图片路径')
    parser.add_argument('--output', required=True, help='输出视频路径')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--task-id', type=int, help='视频任务ID用于数据库更新')
    parser.add_argument('--segment-id', type=int, help='视频片段ID用于数据库更新')

    args = parser.parse_args()

    # 运行异步函数
    result = asyncio.run(enhanced_video_generation(
        args.first_image,
        args.last_image,
        args.output,
        args.debug,
        args.task_id,
        args.segment_id
    ))
    
    sys.exit(0 if result else 1)
