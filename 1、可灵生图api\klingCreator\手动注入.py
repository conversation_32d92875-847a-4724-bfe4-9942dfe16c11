#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动注入脚本 - 简化版本，专注于注入首尾帧、提示词、改时长、点击生成
基于增强视频生成.py的核心功能
"""

import asyncio
import base64
import os
import sys
import subprocess
import socket
from pathlib import Path
from playwright.async_api import async_playwright

def read_image_as_base64(image_path):
    """读取图片并转换为base64"""
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()
        
        # 获取MIME类型
        if image_path.lower().endswith('.png'):
            mime_type = 'image/png'
        elif image_path.lower().endswith(('.jpg', '.jpeg')):
            mime_type = 'image/jpeg'
        elif image_path.lower().endswith('.webp'):
            mime_type = 'image/webp'
        else:
            mime_type = 'image/jpeg'  # 默认
        
        base64_data = base64.b64encode(image_data).decode('utf-8')
        
        return {
            "data": base64_data,
            "type": mime_type,
            "size": len(image_data),
            "name": Path(image_path).name
        }
    except Exception as e:
        print(f"[ERROR] 读取图片失败: {e}")
        return None

async def check_and_open_browser():
    """检查并打开浏览器"""
    debug_port = 9222
    
    # 检查Chrome调试端口
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    result = sock.connect_ex(('localhost', debug_port))
    sock.close()
    
    if result != 0:
        print("[ERROR] Chrome调试端口未开启，正在尝试打开浏览器...")
        try:
            if sys.platform == "win32":
                subprocess.Popen([
                    "chrome.exe", 
                    "--remote-debugging-port=9222",
                    "--user-data-dir=chrome-debug",
                    "https://jimeng.jianying.com/ai-tool/generate?type=video"
                ])
            else:
                subprocess.Popen([
                    "google-chrome", 
                    "--remote-debugging-port=9222",
                    "--user-data-dir=chrome-debug",
                    "https://jimeng.jianying.com/ai-tool/generate?type=video"
                ])
            
            print("[WAIT] 等待浏览器启动...")
            await asyncio.sleep(5)
            
            # 再次检查端口
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', debug_port))
            sock.close()
            
            if result != 0:
                print("[ERROR] 浏览器启动失败")
                print("请手动打开Chrome并访问: https://jimeng.jianying.com/ai-tool/generate?type=video")
                print("启动命令: chrome.exe --remote-debugging-port=9222 --user-data-dir=chrome-debug")
                return False
                
        except Exception as e:
            print(f"[ERROR] 启动浏览器失败: {e}")
            return False
    
    print("[OK] Chrome调试端口已开启")
    return True

async def find_or_navigate_to_video_page(browser):
    """查找或导航到视频生成页面"""
    contexts = browser.contexts
    
    jimeng_page = None
    for context in contexts:
        for page in context.pages:
            if 'jimeng.jianying.com' in page.url:
                jimeng_page = page
                break
        if jimeng_page:
            break
    
    if not jimeng_page:
        print("[ERROR] 未找到即梦AI页面，正在跳转...")
        # 尝试在现有标签页中跳转
        if contexts and contexts[0].pages:
            page = contexts[0].pages[0]
            await page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
            await asyncio.sleep(3)
            jimeng_page = page
        else:
            print("[ERROR] 无法找到可用的浏览器页面")
            return None
    
    print(f"[OK] 找到即梦AI页面: {jimeng_page.url}")
    
    # 验证页面是否为视频生成页面
    if 'type=video' not in jimeng_page.url:
        print("[WARN] 当前页面不是视频生成页面，正在跳转...")
        await jimeng_page.goto("https://jimeng.jianying.com/ai-tool/generate?type=video")
        await asyncio.sleep(3)
    
    return jimeng_page

async def clear_existing_frames(page):
    """清空已存在的首尾帧"""
    print("[CLEAR] 开始清空已存在的首尾帧...")
    
    try:
        # 智能清空 - 只清空编辑区域的帧，保留历史记录
        cleared_count = await page.evaluate("""
            () => {
                let cleared = 0;
                const frames = document.querySelectorAll('.reference-mgqKPd, .reference-image-X6b5b7');
                
                frames.forEach(frame => {
                    // 检查是否在历史记录区域
                    const historyArea = document.querySelector('.history-list, .record-list, .generated-list, .record-reference-ymEKWc');
                    if (historyArea && historyArea.contains(frame)) {
                        return; // 跳过历史记录中的帧
                    }
                    
                    // 尝试点击删除按钮
                    const removeButton = frame.querySelector('.remove-button-CGHPzk, [class*="remove-button"]');
                    if (removeButton) {
                        removeButton.click();
                        cleared++;
                    } else {
                        // 直接移除DOM元素
                        frame.remove();
                        cleared++;
                    }
                });
                
                return cleared;
            }
        """)
        
        if cleared_count > 0:
            print(f"[CLEAR] 成功清空 {cleared_count} 个帧图片")
            await asyncio.sleep(2)  # 等待清空完成
        else:
            print("[CLEAR] 没有发现需要清空的帧图片")
        
        return True
        
    except Exception as e:
        print(f"[CLEAR] 清空帧图片时出错: {e}")
        return False

async def inject_images(page, first_frame_data, last_frame_data):
    """注入首尾帧图片"""
    print("[INJECT] 开始注入首尾帧图片...")
    
    try:
        # 注入JavaScript辅助函数
        await page.evaluate("""
            () => {
                window.createFileFromBase64 = function(base64Data, fileName, mimeType) {
                    try {
                        const byteCharacters = atob(base64Data);
                        const byteNumbers = new Array(byteCharacters.length);
                        for (let i = 0; i < byteCharacters.length; i++) {
                            byteNumbers[i] = byteCharacters.charCodeAt(i);
                        }
                        const byteArray = new Uint8Array(byteNumbers);
                        return new File([byteArray], fileName, { type: mimeType });
                    } catch (error) {
                        console.error('创建文件失败:', error);
                        return null;
                    }
                };

                window.setFileToInput = function(input, file) {
                    try {
                        const dataTransfer = new DataTransfer();
                        dataTransfer.items.add(file);
                        input.files = dataTransfer.files;

                        const changeEvent = new Event('change', { bubbles: true });
                        input.dispatchEvent(changeEvent);

                        const inputEvent = new Event('input', { bubbles: true });
                        input.dispatchEvent(inputEvent);

                        return true;
                    } catch (error) {
                        console.error('设置文件失败:', error);
                        return false;
                    }
                };
            }
        """)
        
        # 注入首帧
        print("[INJECT] 注入首帧...")
        first_result = await page.evaluate(f"""
            () => {{
                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');
                    if (inputs.length === 0) {{
                        console.error('未找到文件输入');
                        return false;
                    }}

                    const input = inputs[0];
                    input.style.display = 'block !important';
                    input.style.visibility = 'visible !important';
                    input.style.opacity = '1 !important';

                    const file = window.createFileFromBase64(
                        '{first_frame_data["data"]}',
                        '{first_frame_data["name"]}',
                        '{first_frame_data["type"]}'
                    );

                    if (!file) return false;
                    return window.setFileToInput(input, file);

                }} catch (error) {{
                    console.error('首帧注入失败:', error);
                    return false;
                }}
            }}
        """)
        
        if first_result:
            print("[OK] 首帧注入成功")
        else:
            print("[ERROR] 首帧注入失败")
            return False
        
        await asyncio.sleep(1)
        
        # 注入尾帧
        print("[INJECT] 注入尾帧...")
        last_result = await page.evaluate(f"""
            () => {{
                try {{
                    const inputs = document.querySelectorAll('input[type="file"]');
                    
                    // 尝试所有文件输入
                    for (let i = 0; i < inputs.length; i++) {{
                        const input = inputs[i];
                        input.style.display = 'block !important';
                        input.style.visibility = 'visible !important';
                        input.style.opacity = '1 !important';

                        const file = window.createFileFromBase64(
                            '{last_frame_data["data"]}',
                            '{last_frame_data["name"]}',
                            '{last_frame_data["type"]}'
                        );

                        if (file && window.setFileToInput(input, file)) {{
                            console.log(`尾帧注入成功到输入 ${{i}}`);
                            return true;
                        }}
                    }}
                    return false;

                }} catch (error) {{
                    console.error('尾帧注入失败:', error);
                    return false;
                }}
            }}
        """)
        
        if last_result:
            print("[OK] 尾帧注入成功")
        else:
            print("[WARN] 尾帧注入失败，但首帧已成功")
        
        await asyncio.sleep(2)
        return True

    except Exception as e:
        print(f"[INJECT] 图片注入过程出错: {e}")
        return False

async def inject_prompt(page, prompt_text="固定镜头，完美过渡效果，电影感调色，炫酷过渡"):
    """注入提示词"""
    print(f"[PROMPT] 开始注入提示词: {prompt_text}")

    try:
        # 注入提示词
        prompt_result = await page.evaluate(f"""
            async () => {{
                try {{
                    const promptText = '{prompt_text}';

                    // 确保输入框处于展开状态
                    const mainContent = document.querySelector('.main-content-dP2xNO');
                    const isCollapsed = mainContent && mainContent.classList.contains('collapsed-BEoOVP');

                    if (isCollapsed) {{
                        const promptContainer = document.querySelector('.prompt-container-rcKZJC');
                        if (promptContainer) {{
                            promptContainer.click();
                            await new Promise(resolve => setTimeout(resolve, 300));
                        }}
                    }}

                    // 找到textarea
                    const textarea = document.querySelector('textarea.prompt-textarea-XfqAoB');
                    if (textarea) {{
                        // 确保textarea可见和可编辑
                        textarea.style.display = 'block';
                        textarea.style.visibility = 'visible';
                        textarea.style.height = '88px';

                        // 聚焦并清空
                        textarea.click();
                        await new Promise(resolve => setTimeout(resolve, 200));
                        textarea.focus();
                        await new Promise(resolve => setTimeout(resolve, 200));

                        // 清空现有内容
                        textarea.select();
                        document.execCommand('selectAll');
                        document.execCommand('delete');

                        // 插入新文本
                        textarea.focus();
                        document.execCommand('insertText', false, promptText);

                        // 触发事件
                        const inputEvent = new Event('input', {{ bubbles: true }});
                        textarea.dispatchEvent(inputEvent);

                        const changeEvent = new Event('change', {{ bubbles: true }});
                        textarea.dispatchEvent(changeEvent);

                        return true;
                    }}

                    return false;

                }} catch (error) {{
                    console.error('提示词注入失败:', error);
                    return false;
                }}
            }}
        """)

        if prompt_result:
            print("[OK] 提示词注入成功")
        else:
            print("[ERROR] 提示词注入失败")

        await asyncio.sleep(1)
        return prompt_result

    except Exception as e:
        print(f"[PROMPT] 提示词注入过程出错: {e}")
        return False

async def set_duration(page, duration="10s"):
    """设置视频时长 - 增强版本"""
    print(f"[DURATION] 开始设置视频时长: {duration}")

    try:
        # 首先检查当前时长选择器状态
        duration_check = await page.evaluate("""
            () => {
                console.log('[DURATION] 检查时长选择器状态');

                const result = {
                    currentDuration: '',
                    selectElements: [],
                    durationOptions: []
                };

                // 查找时长选择器
                const selectElements = document.querySelectorAll('.lv-select, [role="combobox"]');
                selectElements.forEach((select, index) => {
                    const valueSpan = select.querySelector('.lv-select-view-value');
                    const currentValue = valueSpan ? valueSpan.textContent.trim() : '';

                    result.selectElements.push({
                        index: index,
                        currentValue: currentValue,
                        visible: select.offsetParent !== null
                    });

                    if (currentValue.includes('s') || currentValue.includes('秒')) {
                        result.currentDuration = currentValue;
                    }
                });

                console.log('时长选择器检查结果:', result);
                return result;
            }
        """)

        print(f"[DURATION] 时长选择器检查结果:")
        print(f"   当前时长: {duration_check['currentDuration']}")
        print(f"   选择器数量: {len(duration_check['selectElements'])}")

        for i, select in enumerate(duration_check['selectElements']):
            if select['visible']:
                print(f"   选择器 {i}: {select['currentValue']} - 可见: {select['visible']}")

        # 设置目标时长
        target_duration = duration
        duration_result = await page.evaluate(f"""
            () => {{
                console.log('[DURATION] 开始精确设置视频时长为{target_duration}');

                try {{
                    // 精确查找显示"5s"的时长选择器
                    let durationSelect = null;
                    const allSelects = document.querySelectorAll('.lv-select, [role="combobox"]');

                    for (const select of allSelects) {{
                        const valueSpan = select.querySelector('.lv-select-view-value');
                        if (valueSpan) {{
                            const currentValue = valueSpan.textContent.trim();
                            console.log('检查选择器:', currentValue);

                            if (currentValue === '5s' || currentValue.includes('5s') || currentValue.includes('s') || currentValue.includes('秒')) {{
                                durationSelect = select;
                                console.log('找到时长选择器当前值:', currentValue);
                                break;
                            }}
                        }}
                    }}

                    if (!durationSelect) {{
                        console.error('未找到时长选择器');
                        return false;
                    }}

                    // 点击选择器打开下拉菜单
                    durationSelect.click();
                    console.log('点击了时长选择器');

                    // 等待下拉菜单出现并查找目标时长选项
                    return new Promise((resolve) => {{
                        setTimeout(() => {{
                            console.log('开始查找{target_duration}选项');

                            let found = false;
                            const dropdownOptions = document.querySelectorAll('.lv-select-option, .lv-option, [role="option"]');

                            console.log('找到下拉选项数量:', dropdownOptions.length);

                            for (const option of dropdownOptions) {{
                                const optionText = option.textContent.trim();
                                console.log('检查选项:', optionText);

                                if (optionText === '{target_duration}' || optionText.includes('{target_duration}')) {{
                                    console.log('找到目标时长选项:', optionText);
                                    option.click();
                                    found = true;
                                    break;
                                }}
                            }}

                            if (!found) {{
                                console.log('未找到{target_duration}选项，尝试其他方法');
                                // 尝试查找包含目标数字的选项
                                const targetNumber = '{target_duration}'.replace('s', '');
                                for (const option of dropdownOptions) {{
                                    const optionText = option.textContent.trim();
                                    if (optionText.includes(targetNumber)) {{
                                        console.log('找到包含目标数字的选项:', optionText);
                                        option.click();
                                        found = true;
                                        break;
                                    }}
                                }}
                            }}

                            resolve(found);
                        }}, 300);
                    }});

                }} catch (error) {{
                    console.error('精确设置时长失败:', error);
                    return false;
                }}
            }}
        """)

        if duration_result:
            print(f"[OK] 视频时长设置为{duration}成功")
        else:
            print(f"[WARN] 视频时长设置失败，可能需要手动选择{duration}")

        await asyncio.sleep(1)

        # 验证时长设置结果
        duration_verify = await page.evaluate("""
            () => {
                console.log('[DURATION] 验证时长设置结果');

                const result = {
                    currentDuration: '',
                    selectValue: '',
                    allSelectors: []
                };

                // 检查所有选择器找到时长相关的
                const allSelects = document.querySelectorAll('.lv-select, [role="combobox"]');

                for (const select of allSelects) {
                    const valueSpan = select.querySelector('.lv-select-view-value');
                    if (valueSpan) {
                        const currentValue = valueSpan.textContent.trim();
                        result.allSelectors.push(currentValue);

                        // 如果包含秒数这就是时长选择器
                        if (currentValue.includes('s') || currentValue.includes('秒')) {
                            result.selectValue = currentValue;
                            result.currentDuration = currentValue;
                            console.log('找到时长选择器:', currentValue);
                        }
                    }
                }

                console.log('时长验证结果:', result);
                return result;
            }
        """)

        print(f"[DURATION] 时长验证结果:")
        print(f"   当前显示时长: {duration_verify['currentDuration']}")

        # 检查是否设置成功
        success = duration_verify['currentDuration'] == duration or duration in duration_verify['currentDuration']
        print(f"   设置结果: {'[OK]' if success else '[WARN]'}")

        await asyncio.sleep(1)
        return duration_result

    except Exception as e:
        print(f"[DURATION] 时长设置过程出错: {e}")
        return False

async def click_generate_button(page):
    """点击生成按钮 - 增强版本"""
    print("[GENERATE] 开始查找并点击生成按钮...")

    try:
        # 使用增强版本的提交按钮点击逻辑
        submit_result = await page.evaluate("""
            () => {
                console.log('[GENERATE] 查找并点击提交按钮');

                const result = {
                    buttonFound: false,
                    buttonClicked: false,
                    taskId: null,
                    error: null,
                    buttonInfo: []
                };

                try {
                    // 查找提交按钮 - 使用增强版本的选择器
                    const submitButton = document.querySelector('button.submit-button-VW0U_J, button[class*="submit-button"]');

                    if (submitButton && submitButton.offsetParent !== null && !submitButton.disabled) {
                        result.buttonFound = true;
                        result.buttonInfo.push({
                            method: 'primary',
                            className: submitButton.className,
                            text: submitButton.textContent.trim(),
                            disabled: submitButton.disabled,
                            visible: submitButton.offsetParent !== null
                        });
                        console.log('[GENERATE] 找到提交按钮准备点击');

                        // 点击提交按钮
                        submitButton.click();
                        result.buttonClicked = true;
                        console.log('[GENERATE] 提交按钮已点击');

                        return result;
                    } else {
                        // 备用方法查找所有可能的提交按钮
                        const allButtons = document.querySelectorAll('button');
                        console.log(`[GENERATE] 主要方法失败，尝试备用方法，共找到 ${allButtons.length} 个按钮`);

                        for (const btn of allButtons) {
                            const className = btn.className || '';
                            const textContent = btn.textContent || '';
                            const hasSubmitClass = className.includes('submit') || className.includes('button-wtoV7J');
                            const hasGenerateText = textContent.includes('生成') || textContent.includes('Generate') || textContent.includes('提交');
                            const hasIcon = btn.querySelector('svg');
                            const isVisible = btn.offsetParent !== null;
                            const isEnabled = !btn.disabled;

                            result.buttonInfo.push({
                                method: 'backup',
                                className: className,
                                text: textContent.trim(),
                                disabled: btn.disabled,
                                visible: isVisible,
                                hasSubmitClass: hasSubmitClass,
                                hasGenerateText: hasGenerateText,
                                hasIcon: hasIcon
                            });

                            // 优先查找有提交类名的按钮
                            if (hasSubmitClass && hasIcon && isVisible && isEnabled) {
                                result.buttonFound = true;
                                btn.click();
                                result.buttonClicked = true;
                                console.log('[GENERATE] 通过备用方法(类名)点击了提交按钮');
                                break;
                            }

                            // 其次查找有生成文本的按钮
                            if (hasGenerateText && isVisible && isEnabled) {
                                result.buttonFound = true;
                                btn.click();
                                result.buttonClicked = true;
                                console.log('[GENERATE] 通过备用方法(文本)点击了提交按钮');
                                break;
                            }
                        }
                    }

                } catch (error) {
                    result.error = error.message;
                    console.error('[GENERATE] 点击提交按钮失败:', error);
                }

                return result;
            }
        """)

        print(f"[GENERATE] 按钮查找结果:")
        print(f"   找到按钮: {submit_result['buttonFound']}")
        print(f"   点击成功: {submit_result['buttonClicked']}")

        if submit_result['buttonInfo']:
            print(f"   检查到 {len(submit_result['buttonInfo'])} 个按钮:")
            for i, info in enumerate(submit_result['buttonInfo'][:5]):  # 只显示前5个
                print(f"     按钮{i+1}: {info['text'][:20]}... - 可见:{info['visible']} 启用:{not info['disabled']}")

        if submit_result['buttonClicked']:
            print("[OK] 生成按钮点击成功，视频生成已开始")

            # 等待页面响应
            await asyncio.sleep(3)

            print("[GENERATE] 视频生成任务已提交")
            return True

        else:
            print("[ERROR] 生成按钮点击失败")
            if submit_result['error']:
                print(f"   错误: {submit_result['error']}")

            # 显示最终验证信息
            final_state = await page.evaluate("""
                () => {
                    const blobImages = document.querySelectorAll('img[src^="blob:"]').length;
                    const promptValue = document.querySelector('textarea, input[type="text"]')?.value || '';

                    return {
                        blobImages: blobImages,
                        promptValue: promptValue.length > 0
                    };
                }
            """)

            print(f"[INFO] 当前状态:")
            print(f"   首尾帧: {'2张图片' if final_state['blobImages'] >= 2 else '1张图片' if final_state['blobImages'] == 1 else '失败'}")
            print(f"   提示词: {'已设置' if final_state['promptValue'] else '需要手动输入'}")
            print("[INFO] 请手动点击提交按钮")

            return False

    except Exception as e:
        print(f"[GENERATE] 点击生成按钮过程出错: {e}")
        return False

async def manual_injection(first_frame_path, last_frame_path, prompt_text=None, duration="10s", clear_frames=True):
    """手动注入主函数"""

    print("=" * 80)
    print("                    手动注入系统启动                    ")
    print("=" * 80)
    print(f"[IMAGE] 首帧图片: {first_frame_path}")
    print(f"[IMAGE] 尾帧图片: {last_frame_path}")
    print(f"[PROMPT] 提示词: {prompt_text or '默认提示词'}")
    print(f"[DURATION] 时长: {duration}")
    print(f"[CLEAR] 清空现有帧: {'是' if clear_frames else '否'}")
    print("=" * 80)
    print("")

    # 1. 验证文件存在
    print("[STEP] 步骤1: 验证文件...")
    if not os.path.exists(first_frame_path):
        print(f"[ERROR] 首帧文件不存在: {first_frame_path}")
        return False

    if not os.path.exists(last_frame_path):
        print(f"[ERROR] 尾帧文件不存在: {last_frame_path}")
        return False

    print(f"[OK] 文件验证通过")

    # 2. 读取图片数据
    print("[STEP] 步骤2: 读取图片数据...")
    first_frame_data = read_image_as_base64(first_frame_path)
    last_frame_data = read_image_as_base64(last_frame_path)

    if not first_frame_data or not last_frame_data:
        print("[ERROR] 图片数据读取失败")
        return False

    print(f"[OK] 首帧: {first_frame_data['size']} 字节, {first_frame_data['type']}")
    print(f"[OK] 尾帧: {last_frame_data['size']} 字节, {last_frame_data['type']}")

    # 3. 检查并打开浏览器
    print("[STEP] 步骤3: 检查浏览器...")
    if not await check_and_open_browser():
        return False

    # 4. 连接到浏览器并执行注入
    print("[STEP] 步骤4: 连接浏览器...")
    async with async_playwright() as p:
        try:
            browser = await p.chromium.connect_over_cdp("http://localhost:9222")

            # 5. 查找或导航到视频页面
            print("[STEP] 步骤5: 查找视频页面...")
            page = await find_or_navigate_to_video_page(browser)
            if not page:
                return False

            # 6. 清空已存在的首尾帧（可选）
            if clear_frames:
                print("[STEP] 步骤6: 清空已存在的首尾帧...")
                await clear_existing_frames(page)
            else:
                print("[STEP] 步骤6: 跳过清空帧（用户选择保留）")

            # 7. 注入首尾帧图片
            print("[STEP] 步骤7: 注入首尾帧图片...")
            if not await inject_images(page, first_frame_data, last_frame_data):
                print("[ERROR] 图片注入失败")
                return False

            # 8. 注入提示词
            if prompt_text:
                print("[STEP] 步骤8: 注入提示词...")
                await inject_prompt(page, prompt_text)
            else:
                print("[STEP] 步骤8: 跳过提示词注入")

            # 9. 设置时长
            print("[STEP] 步骤9: 设置视频时长...")
            await set_duration(page, duration)

            # 10. 点击生成按钮
            print("[STEP] 步骤10: 点击生成按钮...")
            if await click_generate_button(page):
                print("[SUCCESS] 手动注入完成，视频生成已开始！")
                print("请在浏览器中查看生成进度。")
                return True
            else:
                print("[ERROR] 生成按钮点击失败")
                return False

        except Exception as e:
            print(f"[ERROR] 浏览器连接失败: {e}")
            return False

def get_specified_images():
    """获取指定的两张图片作为首尾帧"""
    first_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\保留黑色底背景_保留楼体轮廓_楼体的风格转换成赛博朋克风格.png"
    last_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (2).png"
    # 反过来
    last_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\保留黑色底背景_保留楼体轮廓_楼体的风格转换成赛博朋克风格.png"
    first_frame = r"D:\1、王云领\7.18华创瑞景园\ai生成的图2\一座完全由生物发光植物构成的奇幻建筑_孤立在纯黑色背景中_巨大的 (2).png"

    # 检查文件是否存在
    if not os.path.exists(first_frame):
        print(f"[ERROR] 首帧文件不存在: {first_frame}")
        return None, None

    if not os.path.exists(last_frame):
        print(f"[ERROR] 尾帧文件不存在: {last_frame}")
        return None, None

    print(f"[SPECIFIED] 使用指定首帧: {Path(first_frame).name}")
    print(f"[SPECIFIED] 使用指定尾帧: {Path(last_frame).name}")

    return first_frame, last_frame

def show_menu():
    """显示功能菜单"""
    print("\n" + "=" * 60)
    print("                  手动注入脚本 - 功能菜单                  ")
    print("=" * 60)
    print("1. 注入首尾帧")
    print("2. 清空首尾帧")
    print("3. 填入提示词")
    print("4. 更改时长")
    print("5. 点击生成")
    print("0. 退出程序")
    print("=" * 60)

async def connect_browser():
    """连接浏览器并返回页面对象"""
    try:
        # 检查浏览器
        if not await check_and_open_browser():
            return None

        # 连接浏览器
        playwright = await async_playwright().start()
        browser = await playwright.chromium.connect_over_cdp("http://localhost:9222")

        # 查找页面
        page = await find_or_navigate_to_video_page(browser)
        if not page:
            await playwright.stop()
            return None

        return page, playwright

    except Exception as e:
        print(f"[ERROR] 连接浏览器失败: {e}")
        return None

async def add_frames():
    """功能1: 注入首尾帧"""
    print("\n[功能1] 注入首尾帧")
    print("-" * 40)

    # 获取指定图片
    first_frame_path, last_frame_path = get_specified_images()
    if not first_frame_path or not last_frame_path:
        print("[ERROR] 无法获取图片文件！")
        return False

    # 读取图片数据
    first_frame_data = read_image_as_base64(first_frame_path)
    last_frame_data = read_image_as_base64(last_frame_path)

    if not first_frame_data or not last_frame_data:
        print("[ERROR] 图片数据读取失败")
        return False

    # 连接浏览器
    result = await connect_browser()
    if not result:
        return False

    page, playwright = result

    try:
        # 步骤1: 先清空已有的首尾帧
        print("[STEP] 步骤1: 清空已有的首尾帧...")
        clear_success = await clear_existing_frames(page)
        if clear_success:
            print("[OK] 首尾帧清空完成")
        else:
            print("[WARN] 首尾帧清空失败，继续注入新图片")

        # 等待清空完成
        await asyncio.sleep(2)

        # 步骤2: 注入新的首尾帧图片
        print("[STEP] 步骤2: 注入新的首尾帧图片...")
        success = await inject_images(page, first_frame_data, last_frame_data)
        await playwright.stop()
        return success
    except Exception as e:
        print(f"[ERROR] 注入图片失败: {e}")
        await playwright.stop()
        return False

async def clear_frames():
    """功能2: 清空首尾帧"""
    print("\n[功能2] 清空首尾帧")
    print("-" * 40)

    # 连接浏览器
    result = await connect_browser()
    if not result:
        return False

    page, playwright = result

    try:
        # 清空帧
        success = await clear_existing_frames(page)
        await playwright.stop()
        return success
    except Exception as e:
        print(f"[ERROR] 清空帧失败: {e}")
        await playwright.stop()
        return False

async def input_prompt():
    """功能3: 填入提示词"""
    print("\n[功能3] 填入提示词")
    print("-" * 40)

    # 获取用户输入
    default_prompt = "固定镜头，完美过渡效果，电影感调色，炫酷过渡"
    print(f"默认提示词: {default_prompt}")
    user_prompt = input("请输入提示词 (直接回车使用默认): ").strip()

    if not user_prompt:
        user_prompt = default_prompt

    print(f"使用提示词: {user_prompt}")

    # 连接浏览器
    result = await connect_browser()
    if not result:
        return False

    page, playwright = result

    try:
        # 注入提示词
        success = await inject_prompt(page, user_prompt)
        await playwright.stop()
        return success
    except Exception as e:
        print(f"[ERROR] 注入提示词失败: {e}")
        await playwright.stop()
        return False

async def change_duration():
    """功能4: 更改时长"""
    print("\n[功能4] 更改时长")
    print("-" * 40)

    # 获取用户输入
    print("可选时长: 5s, 10s")
    user_duration = input("请输入时长 (默认10s): ").strip()

    if not user_duration:
        user_duration = "10s"

    print(f"设置时长: {user_duration}")

    # 连接浏览器
    result = await connect_browser()
    if not result:
        return False

    page, playwright = result

    try:
        # 设置时长
        success = await set_duration(page, user_duration)
        await playwright.stop()
        return success
    except Exception as e:
        print(f"[ERROR] 设置时长失败: {e}")
        await playwright.stop()
        return False

async def click_generate():
    """功能5: 点击生成"""
    print("\n[功能5] 点击生成")
    print("-" * 40)

    # 连接浏览器
    result = await connect_browser()
    if not result:
        return False

    page, playwright = result

    try:
        # 点击生成
        success = await click_generate_button(page)
        await playwright.stop()
        return success
    except Exception as e:
        print(f"[ERROR] 点击生成失败: {e}")
        await playwright.stop()
        return False

def main():
    """主函数 - 菜单模式"""
    # 设置控制台编码
    if sys.platform == "win32":
        os.system("chcp 65001 > nul")

    print("欢迎使用手动注入脚本！")

    while True:
        try:
            show_menu()
            choice = input("请选择功能 (0-5): ").strip()

            if choice == "0":
                print("感谢使用，再见！")
                break
            elif choice == "1":
                success = asyncio.run(add_frames())
                if success:
                    print("[SUCCESS] 首尾帧注入成功！")
                else:
                    print("[FAILED] 首尾帧注入失败！")
            elif choice == "2":
                success = asyncio.run(clear_frames())
                if success:
                    print("[SUCCESS] 首尾帧清空成功！")
                else:
                    print("[FAILED] 首尾帧清空失败！")
            elif choice == "3":
                success = asyncio.run(input_prompt())
                if success:
                    print("[SUCCESS] 提示词填入成功！")
                else:
                    print("[FAILED] 提示词填入失败！")
            elif choice == "4":
                success = asyncio.run(change_duration())
                if success:
                    print("[SUCCESS] 时长设置成功！")
                else:
                    print("[FAILED] 时长设置失败！")
            elif choice == "5":
                success = asyncio.run(click_generate())
                if success:
                    print("[SUCCESS] 生成按钮点击成功！视频生成已开始！")
                else:
                    print("[FAILED] 生成按钮点击失败！")
            else:
                print("[ERROR] 无效选择，请输入0-5之间的数字")

            input("\n按回车键继续...")

        except KeyboardInterrupt:
            print("\n\n[CANCELLED] 用户取消操作，退出程序")
            break
        except Exception as e:
            print(f"\n[ERROR] 运行出错: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
