#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件处理工具
"""

import os
import re
import uuid
from pathlib import Path
from typing import Tuple

def sanitize_filename(filename: str) -> str:
    """
    清理文件名，保留中文字符
    只移除不安全的字符，保留中文
    """
    # 移除不安全的字符，但保留中文、英文、数字、点、横线、下划线
    safe_chars = re.sub(r'[<>:"/\\|?*]', '', filename)
    
    # 移除多余的空格
    safe_chars = re.sub(r'\s+', ' ', safe_chars).strip()
    
    # 如果文件名为空或只有扩展名，使用默认名称
    if not safe_chars or safe_chars.startswith('.'):
        safe_chars = f"image_{uuid.uuid4().hex[:8]}{safe_chars}"
    
    return safe_chars

def get_unique_filename(base_name: str, extension: str, output_dir: str) -> str:
    """
    生成唯一的文件名，避免覆盖，保持原始文件名格式
    
    Args:
        base_name: 基础文件名（不含扩展名）
        extension: 文件扩展名（不含点）
        output_dir: 输出目录
        
    Returns:
        唯一的文件名
    """
    # 清理基础文件名，保留中文
    clean_base_name = sanitize_filename(base_name)
    
    counter = 1
    while True:
        if counter == 1:
            filename = f"{clean_base_name}_1.{extension}"
        else:
            filename = f"{clean_base_name}_{counter}.{extension}"
        
        filepath = os.path.join(output_dir, filename)
        if not os.path.exists(filepath):
            return filename
        counter += 1

def extract_filename_parts(filename: str) -> Tuple[str, str]:
    """
    提取文件名和扩展名
    
    Args:
        filename: 完整文件名
        
    Returns:
        (文件名, 扩展名) 元组
    """
    if '.' in filename:
        name, ext = filename.rsplit('.', 1)
        return name, ext
    else:
        return filename, ''

def ensure_directory(directory: str) -> bool:
    """
    确保目录存在
    
    Args:
        directory: 目录路径
        
    Returns:
        是否成功创建或目录已存在
    """
    try:
        os.makedirs(directory, exist_ok=True)
        return True
    except Exception as e:
        print(f"创建目录失败: {e}")
        return False

def get_file_info(filepath: str) -> dict:
    """
    获取文件信息
    
    Args:
        filepath: 文件路径
        
    Returns:
        文件信息字典
    """
    try:
        stat = os.stat(filepath)
        return {
            'size': stat.st_size,
            'created': stat.st_ctime,
            'modified': stat.st_mtime,
            'exists': True
        }
    except Exception:
        return {
            'size': 0,
            'created': None,
            'modified': None,
            'exists': False
        }

def clean_old_files(directory: str, max_age_days: int = 30) -> int:
    """
    清理旧文件
    
    Args:
        directory: 目录路径
        max_age_days: 最大保留天数
        
    Returns:
        删除的文件数量
    """
    import time
    
    if not os.path.exists(directory):
        return 0
    
    current_time = time.time()
    max_age_seconds = max_age_days * 24 * 60 * 60
    deleted_count = 0
    
    try:
        for filename in os.listdir(directory):
            filepath = os.path.join(directory, filename)
            if os.path.isfile(filepath):
                file_age = current_time - os.path.getmtime(filepath)
                if file_age > max_age_seconds:
                    os.remove(filepath)
                    deleted_count += 1
    except Exception as e:
        print(f"清理文件失败: {e}")
    
    return deleted_count

def generate_safe_path(original_filename: str, base_dir: str = "web_output") -> Tuple[str, str]:
    """
    生成安全的文件路径
    
    Args:
        original_filename: 原始文件名
        base_dir: 基础目录
        
    Returns:
        (完整路径, 文件名) 元组
    """
    # 确保基础目录存在
    ensure_directory(base_dir)
    
    # 提取文件名和扩展名
    name, ext = extract_filename_parts(original_filename)
    
    # 生成唯一文件名
    unique_filename = get_unique_filename(name, ext or 'png', base_dir)
    
    # 生成完整路径
    full_path = os.path.join(base_dir, unique_filename)
    
    return full_path, unique_filename
