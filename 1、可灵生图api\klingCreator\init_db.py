#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库初始化脚本
"""

import pymysql
import os
from config import Config

def create_database():
    """创建数据库"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS {Config.DB_NAME} DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print(f"✅ 数据库 {Config.DB_NAME} 创建成功")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False

def init_tables():
    """初始化数据表"""
    try:
        # 读取SQL文件
        sql_file = os.path.join(os.path.dirname(__file__), 'database.sql')
        
        if not os.path.exists(sql_file):
            print(f"❌ SQL文件不存在: {sql_file}")
            return False
        
        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 连接数据库
        connection = pymysql.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD,
            database=Config.DB_NAME,
            charset='utf8mb4'
        )
        
        # 执行SQL语句
        sql_statements = sql_content.split(';')
        
        with connection.cursor() as cursor:
            for statement in sql_statements:
                statement = statement.strip()
                if statement and not statement.startswith('--'):
                    try:
                        cursor.execute(statement)
                        print(f"✅ 执行SQL: {statement[:50]}...")
                    except Exception as e:
                        if "already exists" not in str(e):
                            print(f"⚠️  SQL执行警告: {e}")
        
        connection.commit()
        connection.close()
        
        print("✅ 数据表初始化完成")
        return True
        
    except Exception as e:
        print(f"❌ 初始化数据表失败: {e}")
        return False

def test_connection():
    """测试数据库连接"""
    try:
        connection = pymysql.connect(
            host=Config.DB_HOST,
            port=Config.DB_PORT,
            user=Config.DB_USER,
            password=Config.DB_PASSWORD,
            database=Config.DB_NAME,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = %s", (Config.DB_NAME,))
            result = cursor.fetchone()
            table_count = result['count'] if result else 0
        
        connection.close()
        
        print(f"✅ 数据库连接成功，共有 {table_count} 个表")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始初始化数据库...")
    print(f"📊 数据库配置:")
    print(f"   主机: {Config.DB_HOST}:{Config.DB_PORT}")
    print(f"   用户: {Config.DB_USER}")
    print(f"   数据库: {Config.DB_NAME}")
    print()
    
    # 1. 创建数据库
    print("1️⃣ 创建数据库...")
    if not create_database():
        return False
    
    # 2. 初始化数据表
    print("\n2️⃣ 初始化数据表...")
    if not init_tables():
        return False
    
    # 3. 测试连接
    print("\n3️⃣ 测试数据库连接...")
    if not test_connection():
        return False
    
    print("\n🎉 数据库初始化完成！")
    print("\n📝 下一步:")
    print("1. 启动Web应用: python app.py")
    print("2. 访问主页: http://localhost:5000")
    print("3. 访问管理页面: http://localhost:5000/admin")
    
    return True

if __name__ == "__main__":
    main()
